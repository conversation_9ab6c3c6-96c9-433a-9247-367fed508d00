package com.adins.bsa.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.businesslogic.api.CircularLogic;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.CircularBean;
import com.adins.bsa.custom.CircularTransactionBean;
import com.adins.bsa.custom.queryfilter.ListCircularFilter;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrConsolidateResultCircularGroupingD;
import com.adins.bsa.model.TrConsolidateResultCircularGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.model.TrOcrResultHeader;
import com.adins.bsa.validator.api.DashboardValidator;
import com.adins.bsa.validator.api.ObjectRequestValidator;
import com.adins.bsa.validator.api.OcrResultValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.bsa.webservices.model.circular.AddCircularGroupRequest;
import com.adins.bsa.webservices.model.circular.CheckCircularGroupNameRequest;
import com.adins.bsa.webservices.model.circular.ListCircularGroupRequest;
import com.adins.bsa.webservices.model.circular.ListCircularGroupResponse;
import com.adins.bsa.webservices.model.circular.ListCircularTransactionRequest;
import com.adins.bsa.webservices.model.circular.ListCircularTransactionResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

@Component
public class GenericCircularLogic extends BaseLogic implements CircularLogic {
	
	@Autowired private TenantValidator tenantValidator;
	@Autowired private UserValidator userValidator;
	@Autowired private DashboardValidator dashboardValidator;
	@Autowired private OcrResultValidator ocrResultValidator;
	@Autowired private ObjectRequestValidator requestValidator;

	@Override
	public ListCircularGroupResponse getListCircularGroup(ListCircularGroupRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		ListCircularFilter filter = new ListCircularFilter();
		filter.setDashboardGroupH(dashboardGroupH);
		filter.setMin(min);
		filter.setMax(max);
		
		if (StringUtils.isNotBlank(request.getAccountNo())) {
			ocrResultValidator.validateAccountNoOnDashboard(dashboardGroupH, request.getAccountNo(), audit);
			filter.setAccountNo(request.getAccountNo());
		}
		
		if (StringUtils.isNotBlank(request.getTransactionWindow())) {
			filter.setTransactionWindow(Integer.parseInt(request.getTransactionWindow()));
		}
		
		List<CircularBean> result = daoFactory.getCircularDao().getListCircular(filter);
		long totalData = daoFactory.getCircularDao().countListCircular(filter);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		ListCircularGroupResponse response = new ListCircularGroupResponse();
		response.setCirculars(result);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public MssResponseType checkCircularGroupName(CheckCircularGroupNameRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultCircularGroupingH circularGroupingH = daoFactory.getCircularDao().getCircularGroupingHIgnoreCase(dashboardGroupH, request.getGroupName());
		if (null != circularGroupingH) {
			throw new CommonException(getMessage("businesslogic.global.namealreadyused", new String[] {request.getGroupName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return new MssResponseType();
	}
	
	@Override
	public MssResponseType addCircularGroup(AddCircularGroupRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultCircularGroupingH circularGroupingH = daoFactory.getCircularDao().getCircularGroupingHIgnoreCase(dashboardGroupH, request.getGroupName());
		if (null != circularGroupingH) {
			throw new CommonException(getMessage("businesslogic.global.namealreadyused", new String[] {request.getGroupName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		List<TrOcrResultDetail> ocrResultDetails = validateAddCircularGroupTransactions(request, dashboardGroupH, audit);
		TrOcrResultHeader ocrResultHeader = ocrResultDetails.get(0).getTrOcrResultHeader();
		Date updateTime = new Date();
		
		circularGroupingH = new TrConsolidateResultCircularGroupingH();
		circularGroupingH.setTrDashboardGroupH(dashboardGroupH);
		circularGroupingH.setTrOcrResultHeader(ocrResultHeader);
		circularGroupingH.setGroupName(request.getGroupName());
		circularGroupingH.setRollingWindow(ocrResultDetails.size());
		circularGroupingH.setIsUserEdited("1");
		circularGroupingH.setIsDeleted("0");
		circularGroupingH.setUsrCrt(audit.getCallerId());
		circularGroupingH.setDtmCrt(updateTime);
		circularGroupingH.setUsrUpd(audit.getCallerId());
		circularGroupingH.setDtmUpd(updateTime);
		daoFactory.getCircularDao().insertCircularGroupingH(circularGroupingH);
		
		for (TrOcrResultDetail ocrResultDetail : ocrResultDetails) {
			
			TrConsolidateResultCircularGroupingD circularGroupingD = new TrConsolidateResultCircularGroupingD();
			circularGroupingD.setTrConsolidateResultCircularGroupingH(circularGroupingH);
			circularGroupingD.setTrOcrResultDetail(ocrResultDetail);
			circularGroupingD.setIsUserEdited("1");
			circularGroupingD.setIsDeleted("0");
			circularGroupingD.setUsrCrt(audit.getCallerId());
			circularGroupingD.setDtmCrt(updateTime);
			circularGroupingD.setUsrUpd(audit.getCallerId());
			circularGroupingD.setDtmUpd(updateTime);
			daoFactory.getCircularDao().insertCircularGroupingD(circularGroupingD);
		}
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setCircularLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}
	
	private List<TrOcrResultDetail> validateAddCircularGroupTransactions(AddCircularGroupRequest request, TrDashboardGroupH dashboardGroupH, AuditContext audit) {
		
		String maxLimitString = daoFactory.getLovDao().getHighestSequenceLovCode(GlobalVal.LOV_GROUP_CIRCULAR_TRANSACTION_WINDOW);
		String minLimitString = daoFactory.getLovDao().getLowestSequenceLovCode(GlobalVal.LOV_GROUP_CIRCULAR_TRANSACTION_WINDOW);
		
		int maxLimit = Integer.parseInt(maxLimitString);
		int minLimit = Integer.parseInt(minLimitString);
		int dataSize = request.getResultDetailIds().size();
		
		if (dataSize < minLimit) {
			throw new CommonException(getMessage("businesslogic.global.arrayminlength", new Object[] {"Transaction", minLimit}, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		if (dataSize > maxLimit) {
			throw new CommonException(getMessage("businesslogic.global.arraymaxlength", new Object[] {"Transaction", maxLimit}, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		List<TrOcrResultDetail> details = new ArrayList<>();
		for (String resultDetailId : request.getResultDetailIds()) {
			TrOcrResultDetail detail = ocrResultValidator.getOcrResultDetail(dashboardGroupH, resultDetailId, audit);
			details.add(detail);
		}
		
		return details;
	}

	@Override
	public MssResponseType deleteCircularGroup(CheckCircularGroupNameRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultCircularGroupingH circularGroupingH = daoFactory.getCircularDao().getCircularGroupingH(dashboardGroupH, request.getGroupName());
		if (null == circularGroupingH) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {request.getGroupName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		if ("1".equals(circularGroupingH.getIsUserEdited())) {
			daoFactory.getCircularDao().deleteCircularGroupDs(circularGroupingH);
			daoFactory.getCircularDao().deleteCircularGroupH(circularGroupingH);
		} else {
			daoFactory.getCircularDao().updateCircularGroupDsIsDeleted(circularGroupingH, "1", audit);
			daoFactory.getCircularDao().updateCircularGroupHIsDeleted(circularGroupingH, "1", audit);
		}
		
		Date updateTime = new Date();
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setCircularLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public ListCircularTransactionResponse getListCircularTransaction(ListCircularTransactionRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		TrConsolidateResultCircularGroupingH circularGroupingH = daoFactory.getCircularDao().getCircularGroupingH(dashboardGroupH, request.getGroupName());
		if (null == circularGroupingH) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {request.getGroupName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		List<CircularTransactionBean> transactions = daoFactory.getCircularDao().getListCircularTransaction(circularGroupingH, min, max);
		long totalData = circularGroupingH.getRollingWindow().longValue();
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		ListCircularTransactionResponse response = new ListCircularTransactionResponse();
		response.setTransactions(transactions);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

}
