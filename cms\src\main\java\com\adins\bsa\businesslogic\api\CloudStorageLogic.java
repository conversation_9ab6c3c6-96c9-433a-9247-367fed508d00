package com.adins.bsa.businesslogic.api;

import com.adins.bsa.model.MsTenant;

public interface CloudStorageLogic {
	
	// direct_upload/
	String storeTemporaryDashboardFile(String filename, byte[] fileByteArray);
	byte[] getTemporaryDashboardFile(String filename);
	void deleteTemporaryDashboardFile(String filename);
	
	// dashboard_documents/
	String storeDashboardFile(MsTenant tenant, String filename, byte[] fileByteArray);
	byte[] getDashboardFile(MsTenant tenant, String filename);
	
	// consolidate_documents/ (.pdf)
	String storeConsolidatePdf(MsTenant tenant, String filename, byte[] fileByteArray);
	byte[] getConsolidatePdf(MsTenant tenantH, String filename);
	void deleteConsolidatePdf(MsTenant tenant, String filename);
	
	// consolidate_documents/ (.xlsx)
	String storeConsolidateXlsx(MsTenant tenant, String filename, byte[] fileByteArray);
	byte[] getConsolidateXlsx(MsTenant tenant, String filename);
	void deleteConsolidateXlsx(MsTenant tenant, String filename);
}
