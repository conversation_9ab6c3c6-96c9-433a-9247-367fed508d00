package com.adins.bsa.webservices.model.common;

import com.adins.framework.service.base.model.MssResponseType;

public class GenericPagingResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private int page;
	private long totalPage;
	private long totalResult;
	
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public long getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}
	public long getTotalResult() {
		return totalResult;
	}
	public void setTotalResult(long totalResult) {
		this.totalResult = totalResult;
	}
	
}
