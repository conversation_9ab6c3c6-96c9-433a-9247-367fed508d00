package com.adins.bsa.validator.api;

import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupD;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupDMember;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface SupplierBuyerGroupValidator {
	TrConsolidateResultSupplierBuyerGroupH getConsolidateResultSupplierBuyerGroupH(TrDashboardGroupH trDashboardGroupH,String groupName, boolean objectMustExists, AuditContext audit);
	TrConsolidateResultSupplierBuyerGroupD getGroupDetail(TrDashboardGroupH dashboardGroupH, String subGroupName, boolean objectMustExists, AuditContext audit);
	TrConsolidateResultSupplierBuyerGroupDMember getGroupDetailMember(TrConsolidateResultSupplierBuyerGroupD groupD, String resultDetailId, boolean objectMustExists, AuditContext audit);
}
