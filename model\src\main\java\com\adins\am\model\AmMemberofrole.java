package com.adins.am.model;
// Generated 09-Sep-2021 22:49:32 by Hibernate Tools 5.2.12.Final

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "am_memberofrole")
public class AmMemberofrole extends UpdateableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	public static final String ID_AM_MEMBEROFROLE_HBM = "idAmMemberofrole";
	private long idAmMemberofrole;
	private AmMsrole amMsrole;
	private AmMsuser amMsuser;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_am_memberofrole", unique = true, nullable = false)
	public long getIdAmMemberofrole() {
		return this.idAmMemberofrole;
	}

	public void setIdAmMemberofrole(long idAmMemberofrole) {
		this.idAmMemberofrole = idAmMemberofrole;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_role", nullable = false)
	public AmMsrole getAmMsrole() {
		return this.amMsrole;
	}

	public void setAmMsrole(AmMsrole amMsrole) {
		this.amMsrole = amMsrole;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

}
