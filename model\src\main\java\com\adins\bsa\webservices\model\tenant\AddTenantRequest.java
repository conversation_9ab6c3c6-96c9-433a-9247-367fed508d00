package com.adins.bsa.webservices.model.tenant;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.framework.service.base.model.MssRequestType;

public class AddTenantRequest extends MssRequestType {
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("Tenant code")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 20)
	private String tenantCode;
	
	@ValidationObjectName("Tenant name")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 100)
	private String tenantName;
	
	@ValidationObjectName("API Key")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 80)
	private String apiKey;
	
	@ValidationObjectName("Name")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 80)
	private String name;
	
	@ValidationObjectName("Email")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 80)
	private String email;
	
	@ValidationObjectName("Password")
	@Required(allowBlankString = false)
	@StringLength(minValue = 8, maxValue = 50)
	private String password;

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public String getApiKey() {
		return apiKey;
	}

	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}
	
}
