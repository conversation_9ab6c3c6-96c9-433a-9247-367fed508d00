package com.adins.bsa.webservices.model.insights;

import java.math.BigDecimal;

import com.adins.framework.service.base.model.MssResponseType;

public class InsightsGeneralCircularHeaderDataResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private Integer realCircularCount;
	private Integer circularCount;
	private BigDecimal circularCountRatio;
	private BigDecimal realCircularAmount;
	private BigDecimal circularAmount;
	private BigDecimal circularAmountRatio;
	
	public Integer getRealCircularCount() {
		return realCircularCount;
	}
	public void setRealCircularCount(Integer realCircularCount) {
		this.realCircularCount = realCircularCount;
	}
	public Integer getCircularCount() {
		return circularCount;
	}
	public void setCircularCount(Integer circularCount) {
		this.circularCount = circularCount;
	}
	public BigDecimal getCircularCountRatio() {
		return circularCountRatio;
	}
	public void setCircularCountRatio(BigDecimal circularCountRatio) {
		this.circularCountRatio = circularCountRatio;
	}
	public BigDecimal getRealCircularAmount() {
		return realCircularAmount;
	}
	public void setRealCircularAmount(BigDecimal realCircularAmount) {
		this.realCircularAmount = realCircularAmount;
	}
	public BigDecimal getCircularAmount() {
		return circularAmount;
	}
	public void setCircularAmount(BigDecimal circularAmount) {
		this.circularAmount = circularAmount;
	}
	public BigDecimal getCircularAmountRatio() {
		return circularAmountRatio;
	}
	public void setCircularAmountRatio(BigDecimal circularAmountRatio) {
		this.circularAmountRatio = circularAmountRatio;
	}
	
}
