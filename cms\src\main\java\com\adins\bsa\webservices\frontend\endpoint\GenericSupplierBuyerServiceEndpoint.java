package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.SupplierBuyerGroupLogic;
import com.adins.bsa.webservices.frontend.api.SupplierBuyerGroupService;
import com.adins.bsa.webservices.model.dashboard.ListSubGroupSupplierBuyerGroupRequest;
import com.adins.bsa.webservices.model.dashboard.ListSubGroupSupplierBuyerGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerSubGroupMembersRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.DeleteSupplierBuyerMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.DeleteSupplierBuyerSubGroupMemberRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.EditSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListMasterSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListMasterSupplierBuyerSubGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupMemberRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupMemberResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupOfMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupOfMainGroupResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/supplierBuyerGroup")
@Api(value = "SupplierBuyerGroupService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericSupplierBuyerServiceEndpoint implements SupplierBuyerGroupService {

	@Autowired private SupplierBuyerGroupLogic supplierBuyerGroupLogic;

	@Override
	@POST
	@Path("/s/list")
	public ListSupplierBuyerGroupResponse getList(ListSupplierBuyerGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.getList(request, audit);
	}

	@Override
	@POST
	@Path("/s/getListSubGroup")
	public ListSubGroupSupplierBuyerGroupResponse getListSubGroup(ListSubGroupSupplierBuyerGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.getListSubGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/addMainGroup")
	public MssResponseType addMainGroup(AddSupplierBuyerMainGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.addMainGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/deleteMainGroup")
	public MssResponseType deleteMainGroup(DeleteSupplierBuyerMainGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.deleteMainGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/addSubGroup")
	public MssResponseType addSubGroup(AddSupplierBuyerSubGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.addSubGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/getListSubGroupOfMainGroup")
	public ListSupplierBuyerSubGroupOfMainGroupResponse getListSubGroupOfMainGroup(ListSupplierBuyerSubGroupOfMainGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.getListSubGroupOfMainGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/getListMasterSubGroup")
	public ListMasterSupplierBuyerSubGroupResponse getListMasterSubGroup(ListMasterSupplierBuyerSubGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.getListMasterSubGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/deleteSubGroup")
	public MssResponseType deleteSupplierBuyerSubGroup(AddSupplierBuyerSubGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.deleteSupplierBuyerSubGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/getListSubGroupMember")
	public ListSupplierBuyerSubGroupMemberResponse getListSubGroupMember(ListSupplierBuyerSubGroupMemberRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.getListSubGroupMember(request, audit);
	}

	@Override
	@POST
	@Path("/s/deleteSubGroupMember")
	public MssResponseType deleteSubGroupMember(DeleteSupplierBuyerSubGroupMemberRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.deleteSubGroupMember(request, audit);
	}

	@Override
	@POST
	@Path("/s/addSubGroupMembers")
	public MssResponseType addSupplierBuyerSubGroupMembers(AddSupplierBuyerSubGroupMembersRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.addSupplierBuyerSubGroupMembers(request, audit);
	}

	@Override
	@POST
	@Path("/s/editMainGroupDetail")
	public MssResponseType editSupplierBuyerSubGroup(EditSupplierBuyerSubGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return supplierBuyerGroupLogic.editSupplierBuyerSubGroup(request, audit);
	}

}
