package com.adins.bsa.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.businesslogic.api.NonBusinessTransactionGroupLogic;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.custom.NonBusinessTransactionGroupBean;
import com.adins.bsa.custom.NonBusinessTransactionGroupDetailBean;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrConsolidateResultNonbusinessGroupingD;
import com.adins.bsa.model.TrConsolidateResultNonbusinessGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.validator.api.DashboardValidator;
import com.adins.bsa.validator.api.NonBusinessGroupingValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailResponse;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

@Component
public class GenericNonBusinessTransactionGroupLogic extends BaseLogic implements NonBusinessTransactionGroupLogic {
	
	@Autowired private DashboardValidator dashboardValidator;
	@Autowired private TenantValidator tenantValidator;
	@Autowired private UserValidator userValidator;
	@Autowired private NonBusinessGroupingValidator nbgValidator;
	
	 
	@Override
	public ListNonBusinessTransactionGroupResponse getListNonBusinessTransactionGroup(GenericDashboardPagingRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		List<NonBusinessTransactionGroupBean> nonBusinessTransactionGroups = daoFactory.getNonBusinessTransactionGroupDao().getListNonBusinessTransactionGroup(dashboardGroupH, min, max);
		long totalData = daoFactory.getNonBusinessTransactionGroupDao().countListNonBusinessTransactionGroup(dashboardGroupH);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		ListNonBusinessTransactionGroupResponse response = new ListNonBusinessTransactionGroupResponse();
		response.setListNonBusinessTransactionGroup(nonBusinessTransactionGroups);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public MssResponseType addNonBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(user, dashboardGroupH, audit);
		
		TrConsolidateResultNonbusinessGroupingH groupingH = nbgValidator.validateGetGroupingHeader(dashboardGroupH, request.getGroupName(), false, audit);
		if (null != groupingH && !"1".equals(groupingH.getIsDeleted())) {
			throw new CommonException(getMessage("businesslogic.global.namealreadyused", new String[] {request.getGroupName()}, audit), ReasonCommon.NAME_ALREADY_USED);
		}
		
		Date currentTime = new Date();
		
		TrConsolidateResultNonbusinessGroupingH newGrouping = new TrConsolidateResultNonbusinessGroupingH();
		newGrouping.setTrDashboardGroupH(dashboardGroupH);
		newGrouping.setGroupName(request.getGroupName());
		newGrouping.setIsUserEdited("1");
		newGrouping.setIsDeleted("0");
		newGrouping.setUsrCrt(audit.getCallerId());
		newGrouping.setDtmCrt(currentTime);
		newGrouping.setUsrUpd(audit.getCallerId());
		newGrouping.setDtmUpd(currentTime);
		daoFactory.getNonBusinessTransactionGroupDao().insertConsolidateResultNonbusinessGroupingH(newGrouping);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(currentTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setNonbusinessLastUpdDate(currentTime);
		dashboardLastUpdate.setDtmUpd(currentTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public MssResponseType deleteNonBusinessTransactionGroup(DeleteNonBusinessTransactionGroupRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(user, dashboardGroupH, audit);
		
		TrConsolidateResultNonbusinessGroupingH groupingH = nbgValidator.validateGetGroupingHeader(dashboardGroupH, request.getGroupName(), true, audit);
		
		Date updateTime = new Date();
		
		if ("1".equals(groupingH.getIsUserEdited())) {
			daoFactory.getNonBusinessTransactionGroupDao().deleteGroupingDByGroupingH(groupingH);
			daoFactory.getNonBusinessTransactionGroupDao().deleteConsolidateResultNonbusinessGroupingH(groupingH);
			
			dashboardGroupH.setUsrUpd(audit.getCallerId());
			dashboardGroupH.setDtmUpd(updateTime);
			daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
			
			TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
			dashboardLastUpdate.setNonbusinessLastUpdDate(updateTime);
			dashboardLastUpdate.setUsrUpd(audit.getCallerId());
			dashboardLastUpdate.setDtmUpd(updateTime);
			daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
			
			return new MssResponseType();
		}
		
		// Delete rows with is_user_edited = 1
		daoFactory.getNonBusinessTransactionGroupDao().deleteGroupingDByGroupingHAndIsUserEdited(groupingH, "1");
		
		// Update rows with is_user_edited = 0
		daoFactory.getNonBusinessTransactionGroupDao().updateGroupingDetailIsDeletedByGroupingHeaderAndIsUserEdited(groupingH, "0", "1", audit);
		
		groupingH.setIsUserEdited("1");
		groupingH.setIsDeleted("1");
		groupingH.setUsrUpd(audit.getCallerId());
		groupingH.setDtmUpd(updateTime);
		daoFactory.getNonBusinessTransactionGroupDao().updateConsolidateResultNonbusinessGroupingH(groupingH);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setNonbusinessLastUpdDate(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		dashboardLastUpdate.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public ListNonBusinessTransactionGroupDetailResponse getListNonBusinessTransactionGroupDetail(ListNonBusinessTransactionGroupDetailRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultNonbusinessGroupingH nonbusinessGroupingH = nbgValidator.validateGetGroupingHeader(dashboardGroupH, request.getGroupName(), true, audit);
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		List<NonBusinessTransactionGroupDetailBean> groups = daoFactory.getNonBusinessTransactionGroupDao().getListNonBusinessTransactionGRoupDetail(nonbusinessGroupingH, min, max);
		long totalData = daoFactory.getNonBusinessTransactionGroupDao().countListNonBusinessTransactionGRoupDetail(nonbusinessGroupingH);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		ListNonBusinessTransactionGroupDetailResponse response = new ListNonBusinessTransactionGroupDetailResponse();
		response.setGroups(groups);
		response.setTotalResult(totalData);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		return response;
	}

	@Override
	public MssResponseType deleteNonBusinessTransactionGroupDetail(DeleteNonBusinessTransactionGroupDetailRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultNonbusinessGroupingH nonbusinessGroupingH = nbgValidator.validateGetGroupingHeader(dashboardGroupH, request.getGroupName(), true, audit);
		TrConsolidateResultNonbusinessGroupingD nonbusinessGroupingD = nbgValidator.validateGetGroupingDetail(nonbusinessGroupingH, request.getDetailId(), true, audit);
		
		Date updateTime = new Date();
		
		if ("1".equals(nonbusinessGroupingD.getIsUserEdited())) {
			daoFactory.getNonBusinessTransactionGroupDao().deleteGroupingD(nonbusinessGroupingD);
		} else {
			nonbusinessGroupingD.setIsUserEdited("1");
			nonbusinessGroupingD.setIsDeleted("1");
			nonbusinessGroupingD.setDtmUpd(updateTime);
			nonbusinessGroupingD.setUsrUpd(audit.getCallerId());
			daoFactory.getNonBusinessTransactionGroupDao().updateGroupingD(nonbusinessGroupingD);
		}
		
		nonbusinessGroupingH.setDtmUpd(updateTime);
		nonbusinessGroupingH.setUsrUpd(audit.getCallerId());
		daoFactory.getNonBusinessTransactionGroupDao().updateConsolidateResultNonbusinessGroupingH(nonbusinessGroupingH);
		
		dashboardGroupH.setDtmUpd(updateTime);
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setNonbusinessLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}
	
	private List<TrOcrResultDetail> validateResultDetailIdForInsert(TrDashboardGroupH dashboardGroupH, List<String> resultDetailIds, AuditContext audit) {
		List<TrOcrResultDetail> details = new ArrayList<>();
		
		for (String resultDetailId : resultDetailIds) {
			TrOcrResultDetail ocrResultDetail = daoFactory.getOcrResultDao().getOcrResultDetail(dashboardGroupH, resultDetailId);
			if (null == ocrResultDetail) {
				throw new CommonException(getMessage("businesslogic.global.objectnotfound2",
						new String[] {"Transaction " + resultDetailId, dashboardGroupH.getDashboardGroupName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
			}
			
			TrConsolidateResultNonbusinessGroupingD groupingD = daoFactory.getNonBusinessTransactionGroupDao().getGroupingD(ocrResultDetail, "0");
			if (null != groupingD) {
				throw new CommonException(getMessage("businesslogic.global.objectalreadysaved",
						new String[] {"Transaction " + resultDetailId, groupingD.getTrConsolidateResultNonbusinessGroupingH().getGroupName()}, audit), ReasonCommon.DUPLICATE_OBJECT);
			}
			
			details.add(ocrResultDetail);
		}
		
		return details;
	}

	@Override
	public MssResponseType addNonBusinessTransactionGroupDetail(AddNonBusinessTransactionGroupDetailRequest request, AuditContext audit) {
		
		if (CollectionUtils.isEmpty(request.getResultDetailIds())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Result detail ID(s)"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultNonbusinessGroupingH nonbusinessGroupingH = nbgValidator.validateGetGroupingHeader(dashboardGroupH, request.getGroupName(), true, audit);
		List<TrOcrResultDetail> ocrResultDetails = validateResultDetailIdForInsert(dashboardGroupH, request.getResultDetailIds(), audit);
		
		Date createTime = new Date();
		
		for (TrOcrResultDetail ocrResultDetail : ocrResultDetails) {
			TrConsolidateResultNonbusinessGroupingD groupingD = new TrConsolidateResultNonbusinessGroupingD();
			groupingD.setTrConsolidateResultNonbusinessGroupingH(nonbusinessGroupingH);
			groupingD.setTrOcrResultDetail(ocrResultDetail);
			groupingD.setIsUserEdited("1");
			groupingD.setIsDeleted("0");
			groupingD.setUsrCrt(audit.getCallerId());
			groupingD.setDtmCrt(createTime);
			daoFactory.getNonBusinessTransactionGroupDao().insertGroupingD(groupingD);
		}
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(createTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setNonbusinessLastUpdDate(createTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		dashboardLastUpdate.setDtmUpd(createTime);
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	
	
}
