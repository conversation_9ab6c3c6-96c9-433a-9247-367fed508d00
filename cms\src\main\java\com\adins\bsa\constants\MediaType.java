package com.adins.bsa.constants;

import javax.ws.rs.core.HttpHeaders;

public class MediaType {
	protected MediaType() {
		throw new IllegalStateException("MediaType class shall not be instantiated! Class=" + this.getClass().getName());
	}
	
	// Header Key
	public static final String CONTENT_TYPE = HttpHeaders.CONTENT_TYPE;
	public static final String AUTHORIZATION = HttpHeaders.AUTHORIZATION;
	public static final String X_REAL_IP = "x-real-ip";
	
	// Header Value
	public static final String APPLICATION_JSON = javax.ws.rs.core.MediaType.APPLICATION_JSON;
	public static final String APPLICATION_PDF = org.springframework.http.MediaType.APPLICATION_PDF_VALUE;
	public static final String APPLICATION_FORM_URLENCODED = javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
	public static final String MULTIPART_FORMDATA = "multipart/form-data";
}
