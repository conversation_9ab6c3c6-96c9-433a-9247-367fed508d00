package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.common.GeneralSettingRequest;
import com.adins.bsa.webservices.model.common.GeneralSettingResponse;
import com.adins.bsa.webservices.model.common.LovListRequest;
import com.adins.bsa.webservices.model.common.LovListResponse;

public interface DataService {
	LovListResponse getLovList(LovListRequest request);
	GeneralSettingResponse getGeneralSetting(GeneralSettingRequest request);
}
