package com.adins.bsa.custom;

import com.adins.am.model.AmMsuser;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;

public class AuditLogDetailBean {
    
    private TrDashboardGroupH dashboardGroupH;
    private TrDashboardGroupD dashboardGroupD;
    private AmMsuser user;
    private String field;
    private String previousValue;
    private String newValue;
    private String notes;

    public TrDashboardGroupH getDashboardGroupH() {
        return this.dashboardGroupH;
    }

    public void setDashboardGroupH(TrDashboardGroupH dashboardGroupH) {
        this.dashboardGroupH = dashboardGroupH;
    }

    public TrDashboardGroupD getDashboardGroupD() {
        return this.dashboardGroupD;
    }

    public void setDashboardGroupD(TrDashboardGroupD dashboardGroupD) {
        this.dashboardGroupD = dashboardGroupD;
    }

    public AmMsuser getUser() {
        return this.user;
    }

    public void setUser(AmMsuser user) {
        this.user = user;
    }

    public String getField() {
        return this.field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getPreviousValue() {
        return this.previousValue;
    }

    public void setPreviousValue(String previousValue) {
        this.previousValue = previousValue;
    }

    public String getNewValue() {
        return this.newValue;
    }

    public void setNewValue(String newValue) {
        this.newValue = newValue;
    }

    public String getNotes() {
        return this.notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

}
