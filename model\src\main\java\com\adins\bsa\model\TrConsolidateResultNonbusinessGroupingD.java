package com.adins.bsa.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "tr_consolidate_result_nonbusiness_grouping_d")
public class TrConsolidateResultNonbusinessGroupingD extends UpdateableEntity {

	private long idConsolidateResultNonbusinessGroupingD;
	private TrConsolidateResultNonbusinessGroupingH trConsolidateResultNonbusinessGroupingH;
	private TrOcrResultDetail trOcrResultDetail;
	private String isUserEdited;
	private String isDeleted;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_consolidate_result_nonbusiness_grouping_d", unique = true, nullable = false)
	public long getIdConsolidateResultNonbusinessGroupingD() {
		return idConsolidateResultNonbusinessGroupingD;
	}
	
	public void setIdConsolidateResultNonbusinessGroupingD(long idConsolidateResultNonbusinessGroupingD) {
		this.idConsolidateResultNonbusinessGroupingD = idConsolidateResultNonbusinessGroupingD;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_consolidate_result_nonbusiness_grouping_h", nullable = false)
	public TrConsolidateResultNonbusinessGroupingH getTrConsolidateResultNonbusinessGroupingH() {
		return trConsolidateResultNonbusinessGroupingH;
	}
	
	public void setTrConsolidateResultNonbusinessGroupingH(
			TrConsolidateResultNonbusinessGroupingH trConsolidateResultNonbusinessGroupingH) {
		this.trConsolidateResultNonbusinessGroupingH = trConsolidateResultNonbusinessGroupingH;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ocr_result_detail", nullable = false)
	public TrOcrResultDetail getTrOcrResultDetail() {
		return trOcrResultDetail;
	}
	
	public void setTrOcrResultDetail(TrOcrResultDetail trOcrResultDetail) {
		this.trOcrResultDetail = trOcrResultDetail;
	}
	
	@Column(name = "is_user_edited", length = 1)
	public String getIsUserEdited() {
		return isUserEdited;
	}
	
	public void setIsUserEdited(String isUserEdited) {
		this.isUserEdited = isUserEdited;
	}
	
	@Column(name = "is_deleted", length = 1)
	public String getIsDeleted() {
		return isDeleted;
	}
	
	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}
	
}
