package com.adins.bsa.businesslogic.impl.provider;

import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.framework.persistence.dao.api.IAuditable;

@Component
public class AuditlogLogic extends BaseLogic implements IAuditable {

	@Override
	public void insertAuditLog(String activity, String tableName, String fieldName, String oldValue, String newValue, String keyValue) {
		// Intentionally empty
	}

}
