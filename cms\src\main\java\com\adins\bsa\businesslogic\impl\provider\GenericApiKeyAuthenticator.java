package com.adins.bsa.businesslogic.impl.provider;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.businesslogic.api.TenantLogic;
import com.adins.bsa.model.MsTenant;
import com.adins.framework.service.security.ApiKeyAuthenticator;
import com.adins.framework.service.security.ClientDetails;
import com.adins.framework.service.security.InvalidApiKeyException;

@Component
@Transactional(readOnly=true)
public class GenericApiKeyAuthenticator extends BaseLogic implements ApiKeyAuthenticator {
    
	@Autowired TenantLogic tenantLogic;    
	
	@Override
	public ClientDetails authenticate(String apiKey) {
		return this.authenticate(apiKey, null);
	}
	
	@Override
	public ClientDetails authenticate(String apiKey, String remoteAddress) {
		if (!StringUtils.contains(apiKey, '@')) {
			throw new InvalidApiKeyException("Api Key does not contains tenant code. example: apikey@tenantCode");
		}
		
		String[] apiKeyStripped = StringUtils.splitPreserveAllTokens(apiKey, '@');
		String key = apiKeyStripped[0];
		String tenantCode = StringUtils.upperCase(apiKeyStripped[1]);
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByApiKeyAndTenantCode(key, tenantCode);
		if (null == tenant) {
			throw new InvalidApiKeyException(messageSource.getMessage("businesslogic.tenant.incorrectapikey", null, this.retrieveDefaultLocale()));
		}
		
		ClientDetails client = new ClientDetails();
		client.setClientId(tenant.getTenantCode());
		return client;
    }
}