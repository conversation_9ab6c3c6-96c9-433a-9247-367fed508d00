package com.adins.bsa.constants;

public class GlobalVal {
	/**
	 * The class shall not be instantiated.
	 * This constructor will throw IllegalStateException when instantiated
	 */
	protected GlobalVal() {
		throw new IllegalStateException("GlobalVal class shall not be instantiated! Class=" + this.getClass().getName());
	}

	/**
	 * Constant for searching (number of data column per page).
	 */
	public static final int COL_PER_PAGE = 20;


	/**
	 * Constant for paging (number of form per page).
	 */
	public static final int FORM_PER_PAGE = 10;
	
	/**
	 * Constant for paging (start row).
	 */
	public static final int START_ROW = 1;


	/**
	 * Constant for stacking (number of stack for storing page parameter).
	 */
	public static final int STACK_PER_PAGE = 5;


	/**
	 * Constant for paging (number of data row per page).
	 */
	public static final int ROW_PER_PAGE = 25;
	public static final int ROW_PER_PAGE_LOOKUP = 10;
	public static final int ROW_PER_PAGE_UNASSIGN = 100;


	/**
	 * Constant for row ordering (ascending).
	 */
	public static final String ROW_ORDER_ASC = "ASC";


	/**
	 * Constant for row ordering (descending).
	 */
	public static final String ROW_ORDER_DESC = "DESC";
	
	/**
	 * Token separator, used in loginId to identify tenant in multitenant configuration
	 */
	public static final char TENANT_TOKEN = '@';
	
	/*
	 * LOV Group
	 */
	public static final String LOV_GROUP_PWD_CHANGE_TYPE = "PWD_CHANGE_TYPE";
	public static final String LOV_GROUP_PROCESS_STATUS = "PROCESS_STATUS";
	public static final String LOV_GROUP_CATEGORY = "CATEGORY";
	public static final String LOV_GROUP_ANOMALY_REASON = "ANOMALY_REASON";
	public static final String LOV_GROUP_ANOMALY_RISK = "ANOMALY_RISK";
	public static final String LOV_GROUP_CIRCULAR_TRANSACTION_WINDOW = "CIRCULAR_TRANSACTION_WINDOW";
	public static final String LOV_GROUP_DASHBOARD_FILE_TYPE = "DASHBOARD_FILE_TYPE";
	public static final String LOV_GROUP_DASHBOARD_MODULE = "DASHBOARD_MODULE";
	public static final String LOV_GROUP_DASHBOARD_ACTION = "DASHBOARD_ACTION";
	
	/*
	 * LOV file type
	 */
	public static final String CODE_LOV_FILE_TYPE_PDF = "PDF";
	public static final String CODE_LOV_FILE_TYPE_IMAGE = "IMAGE";

	/*
	 * LOV process type
	 */
	public static final String CODE_LOV_PROCESS_TYPE_PENDING = "PENDING";
	public static final String CODE_LOV_PROCESS_TYPE_FAILED = "FAILED";
	public static final String CODE_LOV_PROCESS_TYPE_COMPLETE = "COMPLETE";
	public static final String CODE_LOV_PROCESS_TYPE_EDIT = "EDIT";
	public static final String CODE_LOV_PROCESS_TYPE_CONSOLIDATED = "CONSOLIDATED";
	
	/*
	 * LOV change password type
	 */
	public static final String CODE_LOV_CHANGE_PWD_TYPE_RESET = "RESET";
	public static final String CODE_LOV_CHANGE_PWD_TYPE_NEW = "NEW";
	
	/*
	 * IMAGE PREFIX
	 */
	public static final String IMG_PNG_PREFIX	= "data:image/png;base64,";
	public static final String IMG_JPEG_PREFIX	= "data:image/jpeg;base64,";
	public static final String IMG_JPG_PREFIX	= "data:image/jpg;base64,";
	
	/*
	 * FILE FORMAT
	 */
	public static final String FILE_FORMAT_JPEG = ".jpeg";
	public static final String FILE_FORMAT_JPG	= ".jpg";
	public static final String FILE_FORMAT_PNG	= ".png";
	public static final String FILE_FORMAT_PDF	= ".pdf";
	public static final String FILE_DIGI_FORMAT_JPEG = ".jpeg\"";
	
	/*
	 * DATE FORMAT
	 */
    public static final String DATE_TIME_FORMAT_SEQ			= "yyyyMMddHHmmsss";
	public static final String DATE_TIME_FORMAT_ISO 		= "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
	public static final String DATE_TIME_FORMAT_MIL_SEC		= "yyyy-MM-dd HH:mm:ss.SSS";
	public static final String DATE_TIME_FORMAT_SEC			= "yyyy-MM-dd HH:mm:ss";
	public static final String DATE_TIME_FORMAT_SEC_NO_SPACE = "yyyyMMddHHmmss";
	public static final String DATE_TIME_FORMAT_MIN			= "yyyy-MM-dd HH:mm";
	public static final String DATE_FORMAT 					= "yyyy-MM-dd";
	public static final String DATE_FORMAT_EMAIL			= "ddMMyyyy";
	public static final String DATE_FORMAT_PERIOD 			= "yyyy-MM";
	
    public static final String TIME_FORMAT_SEC      		= "HH:mm:ss";
	public static final String TIME_FORMAT 					= "HH:mm";

	public static final String DATE_TIME_FORMAT_MON_IN		= "dd MMM yyyy HH:mm";
	public static final String DATE_FORMAT_MON_IN 			= "dd-MMM-yyyy";
	
	public static final String DATE_TIME_FORMAT_SEC_WITHSLASH	= "yyyy/MM/dd HH:mm:ss";
	
	public static final String DATE_TIME_FORMAT_SEC_IN      = "dd/MM/yyyy HH:mm:ss";
	public static final String DATE_TIME_FORMAT_MIN_IN      = "dd/MM/yyyy HH:mm";
	public static final String DATE_FORMAT_IN   			= "dd/MM/yyyy";
	public static final String DATE_FORMAT_DASH_IN      	= "dd-MM-yyyy";
	
	public static final String DATE_FORMAT_SDT_REPORT		= "dd-MM-yyyy-HH-mm-ss";

	public static final String DEFAULT_DATE 				= "1990-01-01T00:00:00.000Z";
	
	public static final String POSTGRE_DATE_TIME_FORMAT_MON_IN	= "dd Mon yyyy HH24:MI";
	public static final String POSTGRE_DATE_TIME_FORMAT_SEC		= "YYYY-MM-DD HH24:MI:SS";
	public static final String POSTGRE_DATE_TIME_FORMAT_MIL_SEC	= "YYYY-MM-DD HH24:MI:SS.MS";
	
	/*
	 * TIME FORMAT FOR DATE RANGE
	 */
	public static final String SOD_TIME	= " 00:00:00"; //start of day
	public static final String EOD_TIME	= " 23:59:59"; //end of day
	public static final String SOD_TIME_MILL_SEC	= " 00:00:00.000"; //start of day
	public static final String EOD_TIME_MILL_SEC	= " 23:59:59.999"; //end of day
	
	/*
	 * HASH METHOD
	 */
	public static final String HASH_SHA	=  "SHA-256"; 
	
	/*
	 * Character generate password
	 */
	public static final String CHRS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
	public static final String LOWER_CHRS = "abcdefghijklmnopqrstuvwxyz";
	public static final String UPPER_CHRS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	public static final String NUMBER = "0123456789";
	
	/*
	 * OSS paths
	 */
	public static final String OSS_DASHBOARD_DOC_FORMAT = "dashboard_documents/%1$s/%2$s";
	public static final String OSS_DASHBOARD_DIR_FORMAT = "direct_upload/%1$s";
	public static final String OSS_INSIGHTS_PDF_FORMAT = "consolidate_documents/%1$s/%2$s Consolidate Report.pdf"; // tenantCode, dashboardName
	public static final String OSS_INSIGHTS_XLSX_FORMAT = "consolidate_documents/%1$s/%2$s Consolidate Report.xlsx"; // tenantCode, dashboardName
	
	/*
	 * Query parameter key
	 */
	public static final String QUERY_PARAM_MIN = "min";
	public static final String QUERY_PARAM_MAX = "max";
	public static final String QUERY_PARAM_IS_DELETED = "isDeleted";
	public static final String QUERY_PARAM_IS_USER_EDITED = "isUserEdited";
	
	public static final String GROUP_TYPE_SUPPLIER 	= "Supplier";
	public static final String GROUP_TYPE_BUYER		= "Buyer";
	public static final String GROUP_TYPE_RELATED_PARTIES		= "Related Parties";
	
	/*
	 * Form Source List Transaction
	 */
	public static final String FORM_SOURCE_BUSINESS = "BUSINESS";
	public static final String FORM_SOURCE_NONBUSINESS_TRANSACTION = "NON BUSINESS";
	public static final String FORM_SOURCE_ANOMALY = "ANOMALY";
	public static final String FORM_SOURCE_SUPPLIER_BUYER = "SUPPLIER/BUYER";
	public static final String FORM_SOURCE_CIRCULAR = "CIRCULAR";
	
	/*
	 * Transaction type
	 */
	public static final String TRX_TYPE_CREDIT = "CREDIT";
	public static final String TRX_TYPE_DEBIT = "DEBIT";
	public static final String TRX_TYPE_HEADER = "HEADER";
	public static final String TRX_TYPE_SUMMARY = "SUMMARY";
	
	/*
	 * Consolidate status
	 */
	public static final String CONSO_STATUS_CAN_CONSOLIDATE = "CAN_CONSOLIDATE";
	public static final String CONSO_STATUS_CANNOT_CONSOLIDATE = "CANNOT_CONSOLIDATE";
	public static final String CONSO_STATUS_CONSOLIDATE_COMPLETE = "CONSOLIDATE_COMPLETE";
	
	/*
	 * Transaction save type
	 */
	public static final String SAVE_TYPE_COMPLETE = "COMPLETE";
	public static final String SAVE_TYPE_EDIT = "EDIT";
	
	/*
	 * Download document type
	 */
	public static final String DOC_TYPE_PDF = "PDF";
	public static final String DOC_TYPE_EXCEL = "EXCEL";
	
	/*
	 * Message template type
	 */
	public static final String TEMPLATE_TYPE_EMAIL = "EMAIL";
	
	/*
	 * Message template code
	 */
	public static final String TEMPLATE_CODE_OTP_FORGOT_PASSWORD = "OTP_FORGOT_PASSWORD";
	
	/*
	 * Role constant
	 */
	public static final String ROLE_CODE_CREDIT_ANALYST = "CREDIT_ANALYST";
	public static final String ROLE_NAME_CREDIT_ANALYST = "Credit Analyst";
	
	/*
	 * Menu code
	 */
	public static final String MENU_CODE_DASHBOARD = "DASHBOARD";
}

