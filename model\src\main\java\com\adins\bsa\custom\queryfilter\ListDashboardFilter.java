package com.adins.bsa.custom.queryfilter;

import java.util.Date;

import com.adins.am.model.AmMsuser;
import com.adins.bsa.model.MsTenant;

public class ListDashboardFilter {
	private Date uploadDateStart;
	private Date uploadDateEnd;
	private boolean filterCurrentUserOnly;
	private String dashboardName;
	private MsTenant msTenant;
	private AmMsuser requestingUser;
	private int min;
	private int max;
	
	public Date getUploadDateStart() {
		return uploadDateStart;
	}
	public void setUploadDateStart(Date uploadDateStart) {
		this.uploadDateStart = uploadDateStart;
	}
	public Date getUploadDateEnd() {
		return uploadDateEnd;
	}
	public void setUploadDateEnd(Date uploadDateEnd) {
		this.uploadDateEnd = uploadDateEnd;
	}
	public boolean isFilterCurrentUserOnly() {
		return filterCurrentUserOnly;
	}
	public void setFilterCurrentUserOnly(boolean filterCurrentUserOnly) {
		this.filterCurrentUserOnly = filterCurrentUserOnly;
	}
	public String getDashboardName() {
		return dashboardName;
	}
	public void setDashboardName(String dashboardName) {
		this.dashboardName = dashboardName;
	}
	public MsTenant getMsTenant() {
		return msTenant;
	}
	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	public AmMsuser getRequestingUser() {
		return requestingUser;
	}
	public void setRequestingUser(AmMsuser requestingUser) {
		this.requestingUser = requestingUser;
	}
	public int getMin() {
		return min;
	}
	public void setMin(int min) {
		this.min = min;
	}
	public int getMax() {
		return max;
	}
	public void setMax(int max) {
		this.max = max;
	}
	
}
