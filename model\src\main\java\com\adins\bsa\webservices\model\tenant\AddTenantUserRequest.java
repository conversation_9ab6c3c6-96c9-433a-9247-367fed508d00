package com.adins.bsa.webservices.model.tenant;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.framework.service.base.model.MssRequestType;

public class AddTenantUserRequest extends MssRequestType {
	
	private static final long serialVersionUID = 1L;

	@ValidationObjectName("Tenant code")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 20)
	private String tenantCode;
	
	@ValidationObjectName("Email")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 80)
	private String email;
	
	@ValidationObjectName("Name")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 80)
	private String name;
	
	@ValidationObjectName("Password")
	@Required(allowBlankString = false)
	@StringLength(minValue = 8, maxValue = 50)
	private String password;

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}
	
}
