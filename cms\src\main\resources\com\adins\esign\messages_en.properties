businesslogic.global.mandatory = {0} must be filled
businesslogic.global.stringmaxlength = {0} cannot exceeds {1} character(s)
businesslogic.global.stringminlength = {0} must be at least {1} character(s)
businesslogic.global.arraymaxlength = {0} size cannot be greater than {1}
businesslogic.global.arrayminlength = {0} size must be at least {1}
businesslogic.global.mustbefilledwith = {0} must be filled with {1}
businesslogic.global.numeric = {0} must be numeric
businesslogic.global.positivenumeric = {0} must be a positive number
businesslogic.global.dateformat = {0} must use {1} date format
businesslogic.global.objectnotfound = {0} {1} is not found in the system
businesslogic.global.objectnotfound1 = {0} is not found in the system
businesslogic.global.objectnotfound2 = {0} is not found in {1}
businesslogic.global.forbiddenaccess = Forbidden access
businesslogic.global.forbiddenaccess1 = Forbidden access to {0}
businesslogic.global.alreadyused = {0} is already used
businesslogic.global.namealreadyused = The name {0} is already used
businesslogic.global.duplicateobject = Duplicate object found: {0}
businesslogic.global.objectalreadysaved = {0} is already saved in {1}
businesslogic.global.datainvalidstatus = {0} status cannot be {1}
businesslogic.global.failedtogetossdocument = Failed to get document {0} from OSS
businesslogic.global.currentlyprocessed = {0} is currently being processed

businesslogic.global.cannotbegreater = {0} cannot be greater than {1}

businesslogic.global.decimal.digit = {0} cannot exceed {1} total digits (including before and after the decimal point)
businesslogic.global.decimal.point = {0} cannot have more than {1} digits after the decimal point
businesslogic.global.decimal.maxvalue = {0} cannot be equals or bigger than {1}

businesslogic.global.string.numericonly = {0} must be a numeric value
businesslogic.global.string.alphabetonly = {0} must only contain alphabetic characters
businesslogic.global.string.alphabetspaceonly = {0} must only contain alphabetic characters or space
businesslogic.global.string.alphanumericonly = {0} must only contain alphanumeric characters
businesslogic.global.string.alphanumericspaceonly = {0} must only contain alphanumeric characters or space

businesslogic.global.date.beforetoday = The selected date must be before today. Please choose a valid date.

businesslogic.login.idpassword = Login failed. Please check your Username and Access Code
businesslogic.login.inactive = Login failed. Username {0} is not active
businesslogic.login.invalididpassword = Invalid Username or Access Code
businesslogic.login.locked = Login failed. Username {0} is locked

AbstractUserDetailsAuthenticationProvider.badCredentials=Login Failed. Please Check Your Username and Access Code

businesslogic.tenant.incorrectapikey = Incorrect API key

businesslogic.context.useralreadylocked = Your account has been locked due to too many failed login attempts

businesslogic.user.maxotpattempt = You have requested the maximum number of OTPs allowed for today. Please try again tomorrow.
businesslogic.user.maxverifyotpattempt = You have entered the wrong OTP too many times today. Please try again tomorrow.
businesslogic.user.invalidotp = Invalid OTP. Please check the code and try again.
businesslogic.user.invalidpasswordformat = Password must contain uppercase letters, lowercase letters, numbers, and special characters
businesslogic.user.passwordmismatch = The new password and confirmation password do not match. Please try again.

businesslogic.userval.emailusedbyother = Email {0} have been used by another user
businesslogic.userval.phoneusedbyother = Phone {0} have been used by another user
businesslogic.userval.emailnotfound = Email {0} is not found in the system
businesslogic.userval.phonenotfound = Phone {0} is not found in the system

businesslogic.supplierbuyergroup.invalidtype = Type invalid. Please only fill with 'Supplier' or 'Buyer' or 'Related Parties'.
businesslogic.transactiontype.invalidtype = Type invalid. Please only fill with 'Credit' or 'Debit' or 'Header' or 'Summary'.
businesslogic.supplierbuyergroup.mustbeunassigned = Sub group must be unassigned first

businesslogic.anomaly.transactionalreadysaved = Transaction {0} is already saved as an anomaly
businesslogic.anomaly.unmappedrisk = Selected anomaly reason does not have a risk mapped

businesslogic.dashboard.currentlyconsolidating = {0} is being consolidated
businesslogic.dashboard.notyetconsolidated = {0} has not been consolidated yet

businesslogic.ocrresult.stillprocessed = Bank statement is still processed
businesslogic.ocrresult.completed = Bank statement has been processed
businesslogic.ocrresult.cannotedit  = Bank statement status cannot be edited
businesslogic.ocrresult.transactionoverlapped = Transaction overlapped