package com.adins.bsa.util;

import java.math.BigDecimal;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.transport.http.HTTPConduit;
import org.slf4j.LoggerFactory;

import com.adins.bsa.constants.GlobalVal;
import com.devskiller.friendly_id.FriendlyId;

public class MssTool {
	public static final long[] EMPTY_LONG_ARRAY = new long[0];
	public static final Long[] EMPTY_LONG_OBJECT_ARRAY = new Long[0];
	private static SecureRandom randGen = new SecureRandom();
	private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(MssTool.class);
	
	public static final long WEB_CLIENT_DEFAULT_CONNECTION_TIMEOUT = 30_000; //30s
	public static final long WEB_CLIENT_DEFAULT_READ_TIMEOUT = 60_000; //60s
	
	private MssTool() {
		throw new IllegalStateException("Utility class");
	}
	  
	/**
	 * Get type of MasterSequence
	 * 
	 * @param isYear
	 * @param isMonth
	 * @param isDate
	 * @return
	 *   "D" - if sequence is start from 1 daily 
	 *   "M" - if sequence is start from 1 monthly
	 *   "Y" - if sequence is start from 1 annually
	 *   "0" - if sequence is none of above
	 */
		
	public static String sequenceDateType(String isYear, String isMonth, String isDate) {
		if ("1".equals(isYear) && "1".equals(isMonth) && "1".equals(isDate))
			return "D";
		
		if ("1".equals(isYear) && "1".equals(isMonth))
			return "M";
		
		if ("1".equals(isYear))
			return "Y";
			
		return "0";
	}
	
	public static boolean isNewYear(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return (cal.get(Calendar.MONTH) == Calendar.JANUARY &&
				cal.get(Calendar.DATE) == 1);
	}
	
	public static boolean isNewMonth(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return (cal.get(Calendar.DATE) == 1);
	}
	
	public static int randomNumberGenerator(int length) {
		int startNum = (int) Math.pow(10,(double) length - 1);
		int range = (int) (Math.pow(10, length) - startNum + 1);

		return randGen.nextInt(range) + startNum;
	}
	
	/**
	 * 
	 * @param x must be >= 1
	 * @return randomly generated number where 1 <= result <= x
	 */
	public static int generateRandomNumber(int x) {
		return randGen.nextInt(x) + 1;
	}
	
	/**
	 * <p>Checking string result of LiteDMS ILDMSDocWS.uploadImage
	 * whether the upload success of failed.
	 * 
	 * <p>example result:
	 * <li><code>success-21-Document Created : Bukti Kepemilikan Rumah Pemohon_SVY16000039</code></li>
	 * <li><code>Failed-Error in Create Opportunity Document - Error : failed-Invalid login Name : YUWONO</code></li>
	 * 
	 * @param wsResult string result from uploadImage webservice
	 * @return
	 * 	<p><b><code>true<code></b> if result starts with "success" else return <b><code>false<code></b>  
	 */
	public static boolean isDmsUploadSuccess(String wsResult) {
		return StringUtils.startsWithIgnoreCase(wsResult, "success");
	}
	
	public static String readableFileSize(long size) {
	    if(size <= 0) return "0";
	    final String[] units = new String[] { "B", "kB", "MB", "GB", "TB" };
	    int digitGroups = (int) (Math.log10(size)/Math.log10(1024));
	    return new DecimalFormat("#,##0.#").format(size/Math.pow(1024, digitGroups)) + " " + units[digitGroups];
	}
	
	public static String toJenisDocDms(String refId) {
		if (StringUtils.isEmpty(refId))
			return refId;
		
		int lastIdx = refId.lastIndexOf('_');
		if (lastIdx == -1)
			return refId;
		
		return refId.substring(0, lastIdx);
	}
	
	public static long[] strArrayToLongArray(String[] arr){
		if (arr == null || arr.length == 0) {
			return EMPTY_LONG_ARRAY;
		}
		
	    long[] resultArr = new long[arr.length];
	    for (int i = 0; i < arr.length; i++) {
	        resultArr[i] = Long.parseLong(arr[i]);
	    }

	    return resultArr;
	}
	
	public static String formatDateToStringIn(Date inputDate, String dateFormat) {
		if (null == inputDate) {
			return null;
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);  
		return sdf.format(inputDate);
	}
	
	public static Date changeDateFormat(Date inputDate, String dateFormat) {
		String formattedDate = formatDateToStringIn(inputDate, dateFormat);
		return formatStringToDate(formattedDate, dateFormat);
	}

	public static Date formatStringToDate(String inputDateString, String dateFormat) {
		if (StringUtils.isEmpty(inputDateString)) {
			return null;
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
		try {
			return sdf.parse(inputDateString);
		} catch (ParseException e) {
			LOG.warn("Cannot parse '{}' to format '{}'", inputDateString, dateFormat, e);
			return null;
		}
	}
	
	public static String generateStampDutyNo() {
		return FriendlyId.createFriendlyId();
	}
	
	public static String parseFloatDecimal(String number, String decimalFormat) {
		return String.format(decimalFormat, new BigDecimal(number));
	}
	
	public static String changePrefixToPlus62(String phoneNo) {
		if (null == phoneNo) {
			return null;
		}
		
		if (StringUtils.startsWithIgnoreCase(phoneNo, "0")) {
			return "+62".concat(phoneNo.substring(1));
		}
		else if (StringUtils.startsWithIgnoreCase(phoneNo, "62")) {
			return "+".concat(phoneNo);
		}
		else if (StringUtils.startsWithIgnoreCase(phoneNo, "+62")) {
			return phoneNo;
		}
		else {
			throw new IllegalArgumentException();
		}
	}
	
	public static String changePrefixTo62(String phoneNo) {
		if (null == phoneNo) {
			return null;
		}
		
		if (StringUtils.startsWithIgnoreCase(phoneNo, "0")) {
			return "62".concat(phoneNo.substring(1));
		}
		
		return phoneNo;
	}
	
	public static String bytesToHex(byte[] hash) {
	    StringBuilder hexString = new StringBuilder(2 * hash.length);
	    for (int i = 0; i < hash.length; i++) {
	        String hex = Integer.toHexString(0xff & hash[i]);
	        if(hex.length() == 1) {
	            hexString.append('0');
	        }
	        hexString.append(hex);
	    }
	    return hexString.toString();
	}
	
	public static String getHashedString(String params) {
		String hashedString = StringUtils.EMPTY;
		try {
			MessageDigest digest = MessageDigest.getInstance(GlobalVal.HASH_SHA);
			byte[] hashedByteArray = digest.digest(params.getBytes());
			hashedString = MssTool.bytesToHex(hashedByteArray);
		} catch (NoSuchAlgorithmException e) {
			LOG.warn("Failed on hashing '{}' sha-256", params, e);
		}
		return hashedString;
	}
	
	public static void setWebClientConnReadTimeout(WebClient client, long connTimeoutMillis, long readTimeoutMillis) {
		HTTPConduit conduit = WebClient.getConfig(client).getHttpConduit();
		conduit.getClient().setConnectionTimeout(connTimeoutMillis);
		conduit.getClient().setReceiveTimeout(readTimeoutMillis);		
	}
	
	/**
	 * 
	 * <pre>
     * MssTool.maskEmailAddress("<EMAIL>", 2) = "ab**@gmail.com"
     * MssTool.maskEmailAddress("<EMAIL>", 3) = "abc*@gmail.com"
     * MssTool.maskEmailAddress("",  2)              = ""
     * MssTool.maskEmailAddress(null,  2)            = null
     * MssTool.maskEmailAddress("test",  2)          = "test"
     * </pre>
	 * 
	 * @param email 
	 * @param prefixLength number of characters that will not be masked
	 * @return
	 */
	public static String maskEmailAddress(String email, int prefixLength) {
		if (StringUtils.isBlank(email)) {
			return email;
		}
		// Regex example: (?<=.{3}).(?=.*@)
		String regex = "(?<=.{" + prefixLength + "}).(?=.*@)";
		return email.replaceAll(regex, "*");
	}
	
	public static String cutImageStringPrefix(String base64Image) {
		if (StringUtils.isEmpty(base64Image)) {
			return base64Image;
		}
		
		String prefix = StringUtils.EMPTY;
		if (base64Image.startsWith(GlobalVal.IMG_JPEG_PREFIX)) {
			prefix = GlobalVal.IMG_JPEG_PREFIX;
		} else if (base64Image.startsWith(GlobalVal.IMG_JPG_PREFIX)) {
			prefix = GlobalVal.IMG_JPG_PREFIX;
		} else if (base64Image.startsWith(GlobalVal.IMG_PNG_PREFIX)) {
			prefix = GlobalVal.IMG_PNG_PREFIX;
		}
		return base64Image.substring(prefix.length());
	}
	
	public static byte[] imageStringToByteArray(String base64Image) {
		if (StringUtils.isBlank(base64Image)) {
			// return empty byte array
			return new byte[0];
		}
		
		String imageString = cutImageStringPrefix(base64Image);
		return Base64.getDecoder().decode(imageString);
	}
	
	/**
	 * 
	 * <pre>
	 * MssTool.cutLastObjectFromListString("abc", ";") = ""
     * MssTool.cutLastObjectFromListString("abc;def", ";") = "abc"
     * MssTool.cutLastObjectFromListString("ab;cd;ef", ";") = "ab;cd"
     * MssTool.cutLastObjectFromListString("", ";") = ""
     * MssTool.cutLastObjectFromListString(null, ";") = null
     * MssTool.cutLastObjectFromListString("ab;cd;ef", "") = "ab;cd;ef"
     * MssTool.cutLastObjectFromListString("ab;cd;ef", null) = "ab;cd;ef"
     * </pre>
	 */
	public static String cutLastObjectFromListString(String listString, String separator) {
		if (StringUtils.isBlank(listString) || StringUtils.isBlank(separator)) {
			return listString;
		}
		
		String[] array = listString.split(separator);
		List<String> stringList = new LinkedList<>(Arrays.asList(array));
		int index = stringList.size() - 1;
		stringList.remove(index);
		return StringUtils.join(stringList, separator);
	}
	
	public static String getApplicationIpAddress() {
		try {
			return InetAddress.getLocalHost().getHostAddress();
		} catch (Exception e) {
			LOG.error("Failed to get IP address", e);
			return null;
		}
	}
	
	public static final String generateRandomCharacters(String pools, int length) {
		if (length < 1) {
			return null;
		}
		
		StringBuilder builder = new StringBuilder();
		for (int i = 0; i < length; i++) {
			int randomIndex = randGen.nextInt(pools.length());
			builder.append(pools.charAt(randomIndex));
		}
		return builder.toString();
	}
	
	public static final String urlEncode(String data) {
		try {
			return URLEncoder.encode(data, StandardCharsets.UTF_8.toString());
		} catch (Exception e) {
			LOG.error("Failed to URL encode", e);
			return null;
		}
	}
	
	/**
	 * Masks string, leaving the prefix and suffix unmasked
	 * <br><br>
	 * <code>
	 * MssTool.maskStringByIndex("0123456789", 3, 3, '*') = "012****789"
	 * <br>
	 * MssTool.maskStringByIndex("0123456789", 4, 3, '*') = "0123***789"
	 * </code>
	 */
	public static final String maskString(String input, int unmaskedPrefix, int unmaskedSuffix, char maskChar) {
		if (StringUtils.isBlank(input) || input.length() < unmaskedPrefix + unmaskedSuffix) {
			return input;
		}
		
		String prefixString = input.substring(0, unmaskedPrefix);
		String suffixString = input.substring(input.length() - unmaskedSuffix);
		
		int charsLengthToMask = input.length() - unmaskedPrefix - unmaskedSuffix;
		StringBuilder mask = new StringBuilder();
		for (int i = 0; i < charsLengthToMask; i++) {
			mask.append(maskChar);
		}
		
		return prefixString + mask.toString() + suffixString;
	}
	
}
