package com.adins.bsa.dataaccess.api;

import java.util.List;

import com.adins.bsa.custom.AnomalyBean;
import com.adins.bsa.custom.AnomalyGroupBean;
import com.adins.bsa.custom.AnomalyMetadataBean;
import com.adins.bsa.model.MsAnomalyReasonRisk;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrConsolidateResultAnomaly;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyMetadataRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListGroupAnomalyRequest;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface AnomalyDao {
	
	void insertAnomaly(TrConsolidateResultAnomaly anomaly);
	void updateAnomaly(TrConsolidateResultAnomaly anomaly);
	void deleteAnomaly(TrConsolidateResultAnomaly anomaly);
	
	TrConsolidateResultAnomaly getAnomaly(TrDashboardGroupH dashboardGroupH, TrOcrResultDetail ocrResultDetail);
	TrConsolidateResultAnomaly getAnomaly(TrDashboardGroupH dashboardGroupH, String anomalyId);
	
	long countList(ListAnomalyRequest request, long idDashboardGroupH);
	List<AnomalyBean> getList(ListAnomalyRequest request, long idDashboardGroupH, int min, int max);
	
	long countHighRiskAnomaly(TrDashboardGroupH dashboardGroupH);
	long countMediumRiskAnomaly(TrDashboardGroupH dashboardGroupH);
	
	MsAnomalyReasonRisk getRiskByReasonAndTenant(MsLov lovReason, MsTenant msTenant);

	long countListGroupAnomaly(ListGroupAnomalyRequest request, long idDashboardGroupH);
	List<AnomalyGroupBean> getListAnomalyGroup(ListGroupAnomalyRequest request, long idDashboardGroupH, int min, int max);

	void deleteAnomalyByGroup(long idDashboardGroupH, long lovReason, String isUserEdited);
	void updateAnomalyByGroupDeleted(long idDashboardGroupH, long lovReason, String isUserEdited, String isDeleted, AuditContext audit);

	long countListAnomalyMetadata(ListAnomalyMetadataRequest request, long idDashboardGroupH);
	List<AnomalyMetadataBean> getListAnomalyMetadata(ListAnomalyMetadataRequest request, long idDashboardGroupH, int min, int max);
}
