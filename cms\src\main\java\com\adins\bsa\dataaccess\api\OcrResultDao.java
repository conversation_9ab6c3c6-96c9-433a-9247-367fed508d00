package com.adins.bsa.dataaccess.api;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.bsa.custom.BankStatementHeaderSummaryBean;
import com.adins.bsa.custom.BankStatementTransactionDetailBean;
import com.adins.bsa.custom.GetBankStatementTransactionSummaryCalculationResponseBean;
import com.adins.bsa.custom.NonBusinessTransactionGroupTransactionBean;
import com.adins.bsa.custom.OcrResultBean;
import com.adins.bsa.custom.OcrResultPeriodBean;
import com.adins.bsa.custom.queryfilter.ListBankStatementTransactionDetailFilter;
import com.adins.bsa.custom.queryfilter.ListOcrResultFilter;
import com.adins.bsa.custom.queryfilter.ListTransactionFilter;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.model.TrOcrResultHeader;
import com.adins.bsa.model.TrOcrResultSummaryPeriod;

public interface OcrResultDao {
	// List OCR result
	List<OcrResultBean> getOcrResultList(ListOcrResultFilter filter);
	long countOcrResultList(ListOcrResultFilter filter);
	
	// tr_ocr_result_header
	void updateOcrResultHeader(TrOcrResultHeader header);
	void updateOcrResultHeaderNewTrx(TrOcrResultHeader header);
	
	TrOcrResultHeader getOcrResultHeaderByDashboardAndTenantAndAccountNo(TrDashboardGroupH trDashboardGroupH, MsTenant msTenant, String accountNo);
	TrOcrResultHeader getOcrResultHeaderByDashboardAndTenantAndDetail(TrDashboardGroupH trDashboardGroupH, TrDashboardGroupD trDashboardGroupD, MsTenant msTenant);
	TrOcrResultHeader getOcrResultHeaderByDashboard(TrDashboardGroupD dashboardGroupD);
	TrOcrResultHeader getOcrResultHeaderByDashboardNewTrx(TrDashboardGroupD dashboardGroupD);
	long countOcrResultByDashboardHAndAccountNo(TrDashboardGroupH dashboardGroupH, String accountNo);

	// tr_ocr_result_detail
	TrOcrResultDetail getOcrResultDetail(TrDashboardGroupH dashboardGroupH, String resultDetailId);
	TrOcrResultDetail getOcrResultDetail(TrDashboardGroupD dashboardGroupD, String resultDetailId);
	
	void insertOcrResultDetail(TrOcrResultDetail ocrResultDetail);
	void updateOcrResultDetail(TrOcrResultDetail ocrResultDetail);
	void updateOcrResultDetailIsDeleteOverlapNewTrx(Long idOcrResultHeader, Date periodDate, String isDeleteOverlap, String usrUpd);
	
	/**
	 * Count tr_ocr_result_date with its transaction_date is the same month as {transactionDate}
	 */
	long countOcrResultDetail(TrDashboardGroupD dashboardGroupD, Date transactionDate, String isDeleteOverlap);
	
	List<BankStatementTransactionDetailBean> getListBankStatementTransactionDetail(ListBankStatementTransactionDetailFilter filter);
	long countListBankStatementTransactionDetail(ListBankStatementTransactionDetailFilter filter);
	
	// tr_ocr_result_summary_period
	void updateOcrResultSummaryPeriod(TrOcrResultSummaryPeriod summaryPeriod);
	TrOcrResultSummaryPeriod getOcrResultSummaryPeriod(TrDashboardGroupD dashboardGroupD, Date period);
	List<BankStatementHeaderSummaryBean> getBankStatementHeaderSummaryPeriod(TrDashboardGroupD trDashboardGroupD);
	
	List<NonBusinessTransactionGroupTransactionBean> getListTransactionNonBusinessGroup (ListTransactionFilter filter, String formSource);
	
	// Untuk update pengecekan is_delete_overlap
	List<OcrResultPeriodBean> getOcrResultPeriodsNewTrx(TrDashboardGroupH dashboardGroupH, String accountNo);
	List<OcrResultPeriodBean> getOcrResultPeriodsNewTrx(TrDashboardGroupH dashboardGroupH, String accountNo, String period);
	
	//ambil summary bank statement
	List<GetBankStatementTransactionSummaryCalculationResponseBean> getListPeriodBankStatementTransactionSummaryCalculation(long idDashboardGroupH,long idDashboardGroupd);
	BigDecimal getPeriodBankStatementTransactionSummaryCalculation(long idDashboardGroupH,long idDashboardGroupD,String period, BigDecimal openingBalance);
	
	// For load test setup
	List<Map<String, Object>> getLatestBankStatementHeaders(MsTenant tenant, int totalData);
	List<Map<String, Object>> getLatestOcrResultDetails(MsTenant tenant, int totalData);
}
