package com.adins.bsa.businesslogic.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.bsa.businesslogic.api.CommonLogic;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.custom.LovBean;
import com.adins.bsa.validator.api.ObjectRequestValidator;
import com.adins.bsa.webservices.model.common.GeneralSettingRequest;
import com.adins.bsa.webservices.model.common.GeneralSettingResponse;
import com.adins.bsa.webservices.model.common.LovListRequest;
import com.adins.bsa.webservices.model.common.LovListResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Transactional
@Component
public class GenericCommonLogic extends BaseLogic implements CommonLogic {
	
	@Autowired private ObjectRequestValidator requestValidator;

	@Override
	public LovListResponse getLovByGroupAndConstraint(LovListRequest request) {
		
		if (null == request.getLovGroup() || 0 == request.getLovGroup().length()) {
			Status status = new Status();
			status.setCode(200);
			status.setMessage("lovGroup can not be empty.");
			
			LovListResponse response = new LovListResponse();
			response.setStatus(status);
			return response;
		}
		
		List<Map<String, Object>> result = this.daoFactory.getLovDao().getMsLovListByGroupAndConstraint(request);
		
		if (CollectionUtils.isEmpty(result)) {
			LovListResponse response = new LovListResponse();
			response.setLovList(new ArrayList<>());
			return response;
		}
		
		Iterator<Map<String, Object>> itr = result.iterator();
		List<LovBean> lovList = new ArrayList<>();
		
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			
			LovBean bean = new LovBean();
			bean.setCode(String.valueOf(map.get("d0")));
			bean.setDescription(String.valueOf(map.get("d1")));
			bean.setSequence(String.valueOf(map.get("d2")));
			bean.setConstraint1(null == map.get("d3") ? null : String.valueOf(map.get("d3")));
			bean.setConstraint2(null == map.get("d4") ? null : String.valueOf(map.get("d4")));
			bean.setConstraint3(null == map.get("d5") ? null : String.valueOf(map.get("d5")));
			bean.setConstraint4(null == map.get("d6") ? null : String.valueOf(map.get("d6")));
			bean.setConstraint5(null == map.get("d7") ? null : String.valueOf(map.get("d7")));
			
			lovList.add(bean);		
		}
		
		LovListResponse response = new LovListResponse();
		response.setLovList(lovList);
		return response;
	}

	@Override
	public GeneralSettingResponse getGeneralSetting(GeneralSettingRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		AmGeneralsetting generalsetting = daoFactory.getGeneralSettingDao().getGsObjByCode(request.getGeneralSettingCode());
		if (null == generalsetting) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_OBJ_NOT_FOUND, new String[] {"General setting", request.getGeneralSettingCode()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		GeneralSettingResponse response = new GeneralSettingResponse();
		response.setType(generalsetting.getGsType());
		response.setDescription(generalsetting.getGsPrompt());
		response.setValue(generalsetting.getGsValue());
		return response;
	}

}
