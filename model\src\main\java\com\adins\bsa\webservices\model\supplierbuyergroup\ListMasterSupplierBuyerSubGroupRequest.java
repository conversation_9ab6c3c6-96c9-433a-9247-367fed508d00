package com.adins.bsa.webservices.model.supplierbuyergroup;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class ListMasterSupplierBuyerSubGroupRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	private String mainGroupName;
	private String subGroupName;
	private int page;
	
	public String getMainGroupName() {
		return mainGroupName;
	}
	public void setMainGroupName(String mainGroupName) {
		this.mainGroupName = mainGroupName;
	}
	public String getSubGroupName() {
		return subGroupName;
	}
	public void setSubGroupName(String subGroupName) {
		this.subGroupName = subGroupName;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	
}
