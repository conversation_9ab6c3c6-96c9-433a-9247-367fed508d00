<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd        
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">
	
	<bean id="hikariConfig" class="com.zaxxer.hikari.HikariConfig">
	    <property name="driverClassName" value="${spring.datasource.driver-class-name}" />	    
		<property name="jdbcUrl" value="${spring.datasource.url}" />
		<property name="username" value="${spring.datasource.username}" />
		<property name="password" value="${spring.datasource.password}" />
	    <property name="minimumIdle" value="${spring.datasource.hikari.minimum-idle}" />
	    <property name="maximumPoolSize" value="${spring.datasource.hikari.maximum-pool-size}" />
	    <property name="connectionTestQuery" value="${spring.datasource.hikari.connection-test-query}" />
	</bean>
	
	<bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
	    <constructor-arg ref="hikariConfig" />
	</bean>

	<bean id="hibernateSessionFactoryBean" class="org.springframework.orm.hibernate5.LocalSessionFactoryBean">
		<property name="dataSource" ref="dataSource"/>
        <property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.PostgreSQLDialect</prop>
                <prop key="hibernate.show_sql">false</prop>
                <prop key="hibernate.format_sql">false</prop>
                <prop key="hibernate.jdbc.use_streams_for_binary">true</prop>
            </props>
        </property>
		<property name="packagesToScan">
			<list>
				<value>com.adins.am.model</value>
				<value>com.adins.bsa.model</value>
			</list>
		</property>
    </bean>
        	
	<bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
		<property name="basenames">
		  <list>
			<value>classpath:com/adins/esign/messages</value>
		  </list>
		</property>
    	<property name="cacheSeconds" value="-1"/>
	</bean>
    
	<bean id="transactionManager" class="org.springframework.orm.hibernate5.HibernateTransactionManager">
	    <property name="sessionFactory" ref="hibernateSessionFactoryBean" />
	</bean>
	
    <bean id="globalManagerDAO" class="com.adins.framework.persistence.dao.hibernate.manager.global.GlobalManagerDao">
		<property name="sessionFactory" ref="hibernateSessionFactoryBean"/>
    </bean>
    
    <bean id="auditTrail" class="com.adins.framework.persistence.dao.hibernate.interceptor.audit.GenericAuditLog" scope="singleton">
    	<property name="sessionFactory" ref="hibernateSessionFactoryBean"/>
    	<property name="auditManager" ref="auditlogLogic"/>
    </bean>
    
    <bean id="defaultLdapBean" class="com.adins.framework.tool.ldap.UnboundidLdapLogicImpl">
    	<property name="host" value="${ldap.host}" />
    	<property name="port" value="${ldap.port}" />
    </bean>
    
    <bean id="gson" class="com.google.gson.Gson" />
	
	<bean id="auditContextAspect" class="com.adins.framework.service.base.aspect.AuditContextAspect" scope="prototype" />
	
	<bean id="alicloud.ossClientBuilder" class="com.aliyun.oss.OSSClientBuilder" />
	
	<bean id="alicloud.ossClient" class="com.aliyun.oss.OSSClient" factory-bean="alicloud.ossClientBuilder" factory-method="build">
		<constructor-arg name="endpoint" value="${alicloud.oss.endpoint.url}" />
		<constructor-arg name="accessKeyId" value="${alicloud.access-key}"/>
		<constructor-arg name="secretAccessKey" value="${alicloud.secret-key}" />
	</bean>
	
	<bean id="alicloud.functionComputeClient" class="com.aliyuncs.fc.client.FunctionComputeClient">
		<constructor-arg name="region" value="${alicloud.region}"></constructor-arg>
		<constructor-arg name="uid" value="${alicloud.uid}"></constructor-arg>
		<constructor-arg name="accessKeyId" value="${alicloud.access-key}"></constructor-arg>
		<constructor-arg name="accessKeySecret" value="${alicloud.secret-key}"></constructor-arg>
	</bean>
	
	<aop:config>
	  <aop:aspect id="aspect.auditContext" ref="auditContextAspect">
	    <aop:pointcut id="blPointCut" expression="execution(* com.adins.bsa.businesslogic.api..*(..))
	    		or execution(* com.adins.am.businesslogic.api..*(..))" />
	    <aop:before method="putAuditContext" pointcut-ref="blPointCut" />
	    <aop:after method="removeAuditContext" pointcut-ref="blPointCut" />
	  </aop:aspect>
	</aop:config>
	
</beans>