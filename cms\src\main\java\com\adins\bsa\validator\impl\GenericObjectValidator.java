package com.adins.bsa.validator.impl;

import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.validator.api.ObjectValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericObjectValidator extends BaseLogic implements ObjectValidator {

	@Override
	public void validateDateFormat(String date, String dateFormat, String objectName, AuditContext audit) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
			sdf.setLenient(false);
			sdf.parse(date);
		} catch (Exception e) {
			throw new CommonException(getMessage("businesslogic.global.dateformat", new String[] {objectName, dateFormat}, audit), ReasonCommon.INVALID_FORMAT);
		}
	}

	
	@Override
	public void validateDateFormatter(String date, String dateFormat, String objectName, AuditContext audit) {
		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
			YearMonth.parse(date, formatter);
		} catch (Exception e) {
			throw new CommonException(getMessage("businesslogic.global.dateformat", new String[] {objectName, dateFormat}, audit), ReasonCommon.INVALID_FORMAT);
		}
	}
}
