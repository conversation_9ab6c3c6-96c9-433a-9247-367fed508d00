package com.adins.bsa.businesslogic.impl;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.EmailSenderLogic;
import com.adins.bsa.custom.email.EmailAttachmentBean;
import com.adins.bsa.custom.email.EmailInformationBean;

@Component
public class GenericEmailSenderLogic implements EmailSenderLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericEmailSenderLogic.class);
	
	@Autowired @Qualifier("primaryMailSender") private JavaMailSender mailSender;

	@Override
	@Async
	public void sendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments) {
		
		if (ArrayUtils.isEmpty(emailInfo.getTo())) {
			LOG.error("Email recipient is required");
			return;
		}
		
		try {
			executeSendEmail(emailInfo, attachments);
		} catch (Exception e) {
			LOG.error("Failed to send email", e);
		}
		
	}
	
	private void executeSendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments) throws MessagingException {
		LOG.info("Sending mail '{}' to: {}, cc: {}, bcc: {}", emailInfo.getSubject(), emailInfo.getTo(), emailInfo.getCc(), emailInfo.getBcc());
		
		MimeMessage email = mailSender.createMimeMessage();
		MimeMessageHelper helper = new MimeMessageHelper(email, true);
		helper.setTo(emailInfo.getTo());
		helper.setSubject(emailInfo.getSubject());
		helper.setText(emailInfo.getBodyMessage(), true);
		helper.setFrom(((JavaMailSenderImpl) mailSender).getUsername());
		
		if (StringUtils.isNotBlank(emailInfo.getReplyTo())) {
			helper.setReplyTo(emailInfo.getReplyTo());
		}
		if (null != emailInfo.getCc()) {
			helper.setCc(emailInfo.getCc());
		}
		if (null != emailInfo.getBcc()) {
			helper.setBcc(emailInfo.getBcc());
		}
		if (null != attachments) {
			checkAttachment(helper, attachments);
		}
		mailSender.send(email);
		LOG.info("Mail '{}' sent to: {}, cc: {}, bcc: {}, backup={}", emailInfo.getSubject(), emailInfo.getTo(), emailInfo.getCc(), emailInfo.getBcc());
	}
	
	private void checkAttachment(MimeMessageHelper helper, EmailAttachmentBean[] attachments) throws MessagingException {
		for (EmailAttachmentBean attachment : attachments) {
			if (ArrayUtils.isEmpty(attachment.getBinary())) {
				LOG.info("Attachment '{}' is empty, skipping attachment", attachment.getFileName());
				continue;
			}
			helper.addAttachment(attachment.getFileName(), new ByteArrayResource(attachment.getBinary()));
		}
	}

}
