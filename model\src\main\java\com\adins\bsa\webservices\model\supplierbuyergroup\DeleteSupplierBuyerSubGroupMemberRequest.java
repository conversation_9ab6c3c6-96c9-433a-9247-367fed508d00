package com.adins.bsa.webservices.model.supplierbuyergroup;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class DeleteSupplierBuyerSubGroupMemberRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	private String subGroupName;
	private String resultDetailId;
	
	public String getSubGroupName() {
		return subGroupName;
	}
	public void setSubGroupName(String subGroupName) {
		this.subGroupName = subGroupName;
	}
	public String getResultDetailId() {
		return resultDetailId;
	}
	public void setResultDetailId(String resultDetailId) {
		this.resultDetailId = resultDetailId;
	}
	
}
