package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.businesstransactiongroup.ListBusinessTransactionGroupDetailResponse;
import com.adins.bsa.webservices.model.businesstransactiongroup.ListBusinessTransactionGroupResponse;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailRequest;
import com.adins.framework.service.base.model.MssResponseType;

public interface BusinessTransactionGroupService {
	
	ListBusinessTransactionGroupResponse getListBusinessTransactionGroup(GenericDashboardPagingRequest request);
	MssResponseType addBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request);
	MssResponseType deleteBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request);
	ListBusinessTransactionGroupDetailResponse getListBusinessTransactionGroupDetail(ListNonBusinessTransactionGroupDetailRequest request);
	MssResponseType addBusinessTransactionGroupDetail(AddNonBusinessTransactionGroupDetailRequest request);
	MssResponseType deleteBusinessTransactionGroupDetail(DeleteNonBusinessTransactionGroupDetailRequest request);
}
