package com.adins.bsa.dataaccess.api;

import java.util.List;

import com.adins.bsa.custom.NonBusinessTransactionGroupDetailBean;
import com.adins.bsa.custom.NonBusinessTransactionGroupBean;
import com.adins.bsa.model.TrConsolidateResultNonbusinessGroupingD;
import com.adins.bsa.model.TrConsolidateResultNonbusinessGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.framework.persistence.dao.model.AuditContext; 

public interface NonBusinessTransactionGroupDao {
	
	// tr_consolidate_result_nonbusiness_grouping_h
	void insertConsolidateResultNonbusinessGroupingH(TrConsolidateResultNonbusinessGroupingH consolidateResultNonbusinessGroupingH);
	void updateConsolidateResultNonbusinessGroupingH(TrConsolidateResultNonbusinessGroupingH consolidateResultNonbusinessGroupingH);
	void deleteConsolidateResultNonbusinessGroupingH(TrConsolidateResultNonbusinessGroupingH consolidateResultNonbusinessGroupingH);
	
	TrConsolidateResultNonbusinessGroupingH getGroupingH(String groupName, TrDashboardGroupH dashboardGroupH);
	TrConsolidateResultNonbusinessGroupingH getGroupingH(String groupName, TrDashboardGroupH dashboardGroupH, String isDeleted);
	
	List<NonBusinessTransactionGroupBean> getListNonBusinessTransactionGroup(TrDashboardGroupH dashboardGroupH, int min, int max);
	long countListNonBusinessTransactionGroup(TrDashboardGroupH dashboardGroupH);
	
	// tr_consolidate_result_nonbusiness_grouping_d
	void insertGroupingD(TrConsolidateResultNonbusinessGroupingD groupingD);
	
	void updateGroupingD(TrConsolidateResultNonbusinessGroupingD groupingD);
	void updateGroupingDetailIsDeletedByGroupingHeaderAndIsUserEdited(TrConsolidateResultNonbusinessGroupingH groupingH, String isUserEdited, String isDeleted, AuditContext audit);
	
	void deleteGroupingD(TrConsolidateResultNonbusinessGroupingD groupingD);
	void deleteGroupingDByGroupingH(TrConsolidateResultNonbusinessGroupingH groupingH);
	void deleteGroupingDByGroupingHAndIsUserEdited(TrConsolidateResultNonbusinessGroupingH groupingH, String isUserEdited);
	
	TrConsolidateResultNonbusinessGroupingD getGroupingD(TrConsolidateResultNonbusinessGroupingH groupingH, String resultDetailId, String isDeleted);
	TrConsolidateResultNonbusinessGroupingD getGroupingD(TrOcrResultDetail ocrResultDetail, String isDeleted);
	
	List<TrConsolidateResultNonbusinessGroupingD> getGroupingDs(TrConsolidateResultNonbusinessGroupingH groupingH);
	
	List<NonBusinessTransactionGroupDetailBean> getListNonBusinessTransactionGRoupDetail(TrConsolidateResultNonbusinessGroupingH groupingH, int min, int max);
	long countListNonBusinessTransactionGRoupDetail(TrConsolidateResultNonbusinessGroupingH groupingH);
	}
