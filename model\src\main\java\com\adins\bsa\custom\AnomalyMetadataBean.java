package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigInteger;

public class AnomalyMetadataBean implements Serializable {
    private static final long serialVersionUID = 1L;
	
	private BigInteger no;
	private String file;
	private String creator;
	private String producer;
	private String createdDateFile;
    private String modificationDateFile;
	private String anomalyId;
	private String type;

    public BigInteger getNo() {
        return no;
    }
    public void setNo(BigInteger no) {
        this.no = no;
    }
    public String getFile() {
        return file;
    }
    public void setFile(String file) {
        this.file = file;
    }
    public String getCreator() {
        return creator;
    }
    public void setCreator(String creator) {
        this.creator = creator;
    }
    public String getProducer() {
        return producer;
    }
    public void setProducer(String producer) {
        this.producer = producer;
    }
    public String getCreatedDateFile() {
        return createdDateFile;
    }
    public void setCreatedDateFile(String createdDateFile) {
        this.createdDateFile = createdDateFile;
    }
    public String getModificationDateFile() {
        return modificationDateFile;
    }
    public void setModificationDateFile(String modificationDateFile) {
        this.modificationDateFile = modificationDateFile;
    }
    public String getAnomalyId() {
        return anomalyId;
    }
    public void setAnomalyId(String anomalyId) {
        this.anomalyId = anomalyId;
    }
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }

    
}
