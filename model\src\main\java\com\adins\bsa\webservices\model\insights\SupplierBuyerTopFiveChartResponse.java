package com.adins.bsa.webservices.model.insights;

import java.util.List;

import com.adins.bsa.custom.InsightsChartBean;
import com.adins.framework.service.base.model.MssResponseType;

public class SupplierBuyerTopFiveChartResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	
	private List<String> topSupplierByAmountLabels;
	private transient List<Object> topSupplierByAmountPieChartData;
	private List<InsightsChartBean> topSupplierByAmountBarChartData;
	private transient List<Object> topSupplierByAmountBarChartDataDetails;
	
	private List<String> topSupplierByFreqLabels;
	private transient List<Object> topSupplierByFreqPieChartData;
	private List<InsightsChartBean> topSupplierByFreqBarChartData;
	private transient List<Object> topSupplierByFreqBarChartDataDetails;
	
	private List<String> topBuyerByAmountLabels;
	private transient List<Object> topBuyerByAmountPieChartData;
	private List<InsightsChartBean> topBuyerByAmountBarChartData;
	private transient List<Object> topBuyerByAmountBarChartDataDetails;
	
	private List<String> topBuyerByFreqLabels;
	private transient List<Object> topBuyerByFreqPieChartData;
	private List<InsightsChartBean> topBuyerByFreqBarChartData;
	private transient List<Object> topBuyerByFreqBarChartDataDetails;
	
	public List<String> getTopSupplierByAmountLabels() {
		return topSupplierByAmountLabels;
	}
	public void setTopSupplierByAmountLabels(List<String> topSupplierByAmountLabels) {
		this.topSupplierByAmountLabels = topSupplierByAmountLabels;
	}
	public List<Object> getTopSupplierByAmountPieChartData() {
		return topSupplierByAmountPieChartData;
	}
	public void setTopSupplierByAmountPieChartData(List<Object> topSupplierByAmountPieChartData) {
		this.topSupplierByAmountPieChartData = topSupplierByAmountPieChartData;
	}
	public List<InsightsChartBean> getTopSupplierByAmountBarChartData() {
		return topSupplierByAmountBarChartData;
	}
	public void setTopSupplierByAmountBarChartData(List<InsightsChartBean> topSupplierByAmountBarChartData) {
		this.topSupplierByAmountBarChartData = topSupplierByAmountBarChartData;
	}
	public List<Object> getTopSupplierByAmountBarChartDataDetails() {
		return topSupplierByAmountBarChartDataDetails;
	}
	public void setTopSupplierByAmountBarChartDataDetails(List<Object> topSupplierByAmountBarChartDataDetails) {
		this.topSupplierByAmountBarChartDataDetails = topSupplierByAmountBarChartDataDetails;
	}
	public List<String> getTopSupplierByFreqLabels() {
		return topSupplierByFreqLabels;
	}
	public void setTopSupplierByFreqLabels(List<String> topSupplierByFreqLabels) {
		this.topSupplierByFreqLabels = topSupplierByFreqLabels;
	}
	public List<Object> getTopSupplierByFreqPieChartData() {
		return topSupplierByFreqPieChartData;
	}
	public void setTopSupplierByFreqPieChartData(List<Object> topSupplierByFreqPieChartData) {
		this.topSupplierByFreqPieChartData = topSupplierByFreqPieChartData;
	}
	public List<InsightsChartBean> getTopSupplierByFreqBarChartData() {
		return topSupplierByFreqBarChartData;
	}
	public void setTopSupplierByFreqBarChartData(List<InsightsChartBean> topSupplierByFreqBarChartData) {
		this.topSupplierByFreqBarChartData = topSupplierByFreqBarChartData;
	}
	public List<Object> getTopSupplierByFreqBarChartDataDetails() {
		return topSupplierByFreqBarChartDataDetails;
	}
	public void setTopSupplierByFreqBarChartDataDetails(List<Object> topSupplierByFreqBarChartDataDetails) {
		this.topSupplierByFreqBarChartDataDetails = topSupplierByFreqBarChartDataDetails;
	}
	public List<String> getTopBuyerByAmountLabels() {
		return topBuyerByAmountLabels;
	}
	public void setTopBuyerByAmountLabels(List<String> topBuyerByAmountLabels) {
		this.topBuyerByAmountLabels = topBuyerByAmountLabels;
	}
	public List<Object> getTopBuyerByAmountPieChartData() {
		return topBuyerByAmountPieChartData;
	}
	public void setTopBuyerByAmountPieChartData(List<Object> topBuyerByAmountPieChartData) {
		this.topBuyerByAmountPieChartData = topBuyerByAmountPieChartData;
	}
	public List<InsightsChartBean> getTopBuyerByAmountBarChartData() {
		return topBuyerByAmountBarChartData;
	}
	public void setTopBuyerByAmountBarChartData(List<InsightsChartBean> topBuyerByAmountBarChartData) {
		this.topBuyerByAmountBarChartData = topBuyerByAmountBarChartData;
	}
	public List<Object> getTopBuyerByAmountBarChartDataDetails() {
		return topBuyerByAmountBarChartDataDetails;
	}
	public void setTopBuyerByAmountBarChartDataDetails(List<Object> topBuyerByAmountBarChartDataDetails) {
		this.topBuyerByAmountBarChartDataDetails = topBuyerByAmountBarChartDataDetails;
	}
	public List<String> getTopBuyerByFreqLabels() {
		return topBuyerByFreqLabels;
	}
	public void setTopBuyerByFreqLabels(List<String> topBuyerByFreqLabels) {
		this.topBuyerByFreqLabels = topBuyerByFreqLabels;
	}
	public List<Object> getTopBuyerByFreqPieChartData() {
		return topBuyerByFreqPieChartData;
	}
	public void setTopBuyerByFreqPieChartData(List<Object> topBuyerByFreqPieChartData) {
		this.topBuyerByFreqPieChartData = topBuyerByFreqPieChartData;
	}
	public List<InsightsChartBean> getTopBuyerByFreqBarChartData() {
		return topBuyerByFreqBarChartData;
	}
	public void setTopBuyerByFreqBarChartData(List<InsightsChartBean> topBuyerByFreqBarChartData) {
		this.topBuyerByFreqBarChartData = topBuyerByFreqBarChartData;
	}
	public List<Object> getTopBuyerByFreqBarChartDataDetails() {
		return topBuyerByFreqBarChartDataDetails;
	}
	public void setTopBuyerByFreqBarChartDataDetails(List<Object> topBuyerByFreqBarChartDataDetails) {
		this.topBuyerByFreqBarChartDataDetails = topBuyerByFreqBarChartDataDetails;
	}
	
}
