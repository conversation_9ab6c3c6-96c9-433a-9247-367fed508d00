package com.adins.bsa.webservices.model.loadtest;

import java.util.List;

import com.adins.framework.service.base.model.MssResponseType;

public class SetupDeleteAnomalyResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<String> dashboardNames;
	private List<String> anomalyIds;
	
	public List<String> getDashboardNames() {
		return dashboardNames;
	}
	public void setDashboardNames(List<String> dashboardNames) {
		this.dashboardNames = dashboardNames;
	}
	public List<String> getAnomalyIds() {
		return anomalyIds;
	}
	public void setAnomalyIds(List<String> anomalyIds) {
		this.anomalyIds = anomalyIds;
	}
	
	
}
