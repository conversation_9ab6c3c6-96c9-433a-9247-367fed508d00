package com.adins.bsa.webservices.model.nonbusinesstransactiongroup;

import java.util.List;

import com.adins.bsa.custom.NonBusinessTransactionGroupDetailBean;
import com.adins.framework.service.base.model.MssResponseType;

public class ListNonBusinessTransactionGroupDetailResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<NonBusinessTransactionGroupDetailBean> groups;
	private int page;
	private long totalPage;
	private long totalResult;
	
	public List<NonBusinessTransactionGroupDetailBean> getGroups() {
		return groups;
	}
	public void setGroups(List<NonBusinessTransactionGroupDetailBean> groups) {
		this.groups = groups;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public long getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}
	public long getTotalResult() {
		return totalResult;
	}
	public void setTotalResult(long totalResult) {
		this.totalResult = totalResult;
	}
	
}
