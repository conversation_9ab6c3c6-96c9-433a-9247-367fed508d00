package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class CommonException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonCommon {
		MANDATORY_PARAM,
		OBJECT_NOT_FOUND,
		INVALID_FORMAT,
		FORBIDDEN_ACCESS,
		NAME_ALREADY_USED,
		DUPLICATE_OBJECT,
		INVALID_VALUE,
		INVALID_CONDITION,
		CONNECTION_ISSUE,
		UNKNOWN
	}
	
	private final ReasonCommon reason;
	
	public CommonException(String message, ReasonCommon reason) {
		super(message);
		this.reason = reason;
	}
	
	public CommonException(String message, ReasonCommon reason, Throwable e) {
		super(message, e);
		this.reason = reason;
	}

	@Override
	public int getErrorCode() {
		if (reason != null) {
			switch (reason) {
				case MANDATORY_PARAM:
					return StatusCode.MANDATORY_PARAM;
				case OBJECT_NOT_FOUND:
					return StatusCode.OBJECT_NOT_FOUND;
				case INVALID_FORMAT:
					return StatusCode.INVALID_FORMAT;
				case FORBIDDEN_ACCESS:
					return StatusCode.FORBIDDEN_ACCESS;
				case NAME_ALREADY_USED:
					return StatusCode.NAME_ALREADY_USED;
				case DUPLICATE_OBJECT:
						return StatusCode.DUPLICATE_OBJECT;
				case INVALID_VALUE:
					return StatusCode.INVALID_VALUE;
				case INVALID_CONDITION:
					return StatusCode.INVALID_CONDITION;
				case CONNECTION_ISSUE:
					return StatusCode.CONNECTION_ISSUE;
				case UNKNOWN:
					return StatusCode.UNKNOWN;
				default:
					return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}

}
