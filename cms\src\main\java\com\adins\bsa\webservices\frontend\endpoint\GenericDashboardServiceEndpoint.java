package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.DashboardLogic;
import com.adins.bsa.webservices.frontend.api.DashboardService;
import com.adins.bsa.webservices.model.dashboard.AddDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardAccountNoListResponse;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardBankListResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardConsolidateStatusResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardPeriodListRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardPeriodListResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardWarningStatusResponse;
import com.adins.bsa.webservices.model.dashboard.DeleteDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.ListDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.ListDashboardResponse;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListTransactionnonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListTransactionnonBusinessTransactionGroupResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/dashboard")
@Api(value = "DashboardService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericDashboardServiceEndpoint implements DashboardService {
	
	@Autowired private DashboardLogic dashboardLogic;

	@Override
	@POST
	@Path("/s/list")
	public ListDashboardResponse getListDashboard(ListDashboardRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.getListDashboard(request, audit);
	}

	@Override
	@POST
	@Path("/s/delete")
	public MssResponseType deleteDashboard(DeleteDashboardRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.deleteDashboard(request, audit);
	}

	@Override
	@POST
	@Path("/s/add")
	public MssResponseType addDashboard(AddDashboardRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.addDashboard(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/addNewBankStatement")
	public MssResponseType addNewBankStatement(AddDashboardRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.addNewBankStatement(request, audit);
	}

	@Override
	@POST
	@Path("/s/bankList")
	public DashboardBankListResponse getDashboardBankList(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.getDashboardBankList(request, audit);
	}

	@Override
	@POST
	@Path("/s/accountNoList")
	public DashboardAccountNoListResponse getDashboardAccountNoList(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.getDashboardAccountNoList(request, audit);
	}

	@Override
	@POST
	@Path("/s/periodList")
	public DashboardPeriodListResponse getDashboardPeriodList(DashboardPeriodListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.getDashboardPeriodList(request, audit);
	}

	@Override
	@POST
	@Path("/s/getListTransaction")
	public ListTransactionnonBusinessTransactionGroupResponse getListTransaction(ListTransactionnonBusinessTransactionGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.getListTransactionnonBusinessTransactionGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/startConsolidate")
	public MssResponseType startConsolidate(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.startConsolidate(request, audit);
	}

	@Override
	@POST
	@Path("/s/consolidateStatus")
	public DashboardConsolidateStatusResponse getConsolidateStatus(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.getConsolidateStatus(request, audit);
	}

	@Override
	@POST
	@Path("/s/warningStatus")
	public DashboardWarningStatusResponse getDashboardWarningStatus(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardLogic.getDashboardWarningStatus(request, audit);
	}
}
