package com.adins.bsa.businesslogic.api;

import com.adins.bsa.webservices.model.common.GeneralSettingRequest;
import com.adins.bsa.webservices.model.common.GeneralSettingResponse;
import com.adins.bsa.webservices.model.common.LovListRequest;
import com.adins.bsa.webservices.model.common.LovListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface CommonLogic {
	LovListResponse getLovByGroupAndConstraint(LovListRequest request);
	GeneralSettingResponse getGeneralSetting(GeneralSettingRequest request, AuditContext audit);
}
