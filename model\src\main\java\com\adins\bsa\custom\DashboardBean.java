package com.adins.bsa.custom;

import java.io.Serializable;

public class DashboardBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private String dashboardName;
	private String createdBy;
	private String uploadDate;
	private String consolidateDate;
	private String lastUpdated;
	private String editable;
	private String deletable;
	private String isConsolidating;
	
	public String getDashboardName() {
		return dashboardName;
	}
	public void setDashboardName(String dashboardName) {
		this.dashboardName = dashboardName;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public String getUploadDate() {
		return uploadDate;
	}
	public void setUploadDate(String uploadDate) {
		this.uploadDate = uploadDate;
	}
	public String getConsolidateDate() {
		return consolidateDate;
	}
	public void setConsolidateDate(String consolidateDate) {
		this.consolidateDate = consolidateDate;
	}
	public String getLastUpdated() {
		return lastUpdated;
	}
	public void setLastUpdated(String lastUpdated) {
		this.lastUpdated = lastUpdated;
	}
	public String getEditable() {
		return editable;
	}
	public void setEditable(String editable) {
		this.editable = editable;
	}
	public String getDeletable() {
		return deletable;
	}
	public void setDeletable(String deletable) {
		this.deletable = deletable;
	}
	public String getIsConsolidating() {
		return isConsolidating;
	}
	public void setIsConsolidating(String isConsolidating) {
		this.isConsolidating = isConsolidating;
	}
	
}
