package com.adins.bsa.webservices.model.insights;

import java.util.List;

import com.adins.bsa.custom.InsightsChartBean;
import com.adins.bsa.custom.SupplierBuyerCashFlowDetailBean;
import com.adins.framework.service.base.model.MssResponseType;

public class SupplierBuyerMonthlyCashflowAndGrowthRateResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<String> labels;
	private List<InsightsChartBean> cashFlow;
	private List<InsightsChartBean> growthRate;
	private List<SupplierBuyerCashFlowDetailBean> cashFlowDetails;
	
	public List<String> getLabels() {
		return labels;
	}
	public void setLabels(List<String> labels) {
		this.labels = labels;
	}
	public List<InsightsChartBean> getCashFlow() {
		return cashFlow;
	}
	public void setCashFlow(List<InsightsChartBean> cashFlow) {
		this.cashFlow = cashFlow;
	}
	public List<InsightsChartBean> getGrowthRate() {
		return growthRate;
	}
	public void setGrowthRate(List<InsightsChartBean> growthRate) {
		this.growthRate = growthRate;
	}
	public List<SupplierBuyerCashFlowDetailBean> getCashFlowDetails() {
		return cashFlowDetails;
	}
	public void setCashFlowDetails(List<SupplierBuyerCashFlowDetailBean> cashFlowDetails) {
		this.cashFlowDetails = cashFlowDetails;
	}
	
}
