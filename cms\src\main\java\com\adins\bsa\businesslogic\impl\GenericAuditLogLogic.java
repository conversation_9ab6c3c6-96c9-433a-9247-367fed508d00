package com.adins.bsa.businesslogic.impl;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.businesslogic.api.AuditLogLogic;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.constants.enums.DashboardAction;
import com.adins.bsa.constants.enums.DashboardModule;
import com.adins.bsa.custom.AuditLogDetailBean;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.TrDashboardAuditLog;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericAuditLogLogic extends BaseLogic implements AuditLogLogic {

    @Override
    public void insertAuditLog(DashboardModule module, DashboardAction action, AuditLogDetailBean logDetail, AuditContext audit) {
        validateAuditRequest(module, action, logDetail);
        if (StringUtils.equals(logDetail.getPreviousValue(), logDetail.getNewValue())) {
            return;
        }

        TrDashboardAuditLog auditLog = prepareAuditObject(module, action, logDetail, audit);
        daoFactory.getDashboardAuditLogDao().insertDashboardAuditLog(auditLog);
    }

    @Override
    public void insertAuditLogNewTrx(DashboardModule module, DashboardAction action, AuditLogDetailBean logDetail, AuditContext audit) {
        validateAuditRequest(module, action, logDetail);
        if (StringUtils.equals(logDetail.getPreviousValue(), logDetail.getNewValue())) {
            return;
        }
        
        TrDashboardAuditLog auditLog = prepareAuditObject(module, action, logDetail, audit);
        daoFactory.getDashboardAuditLogDao().insertDashboardAuditLogNewTrx(auditLog);
    }

    private void validateAuditRequest(DashboardModule module, DashboardAction action, AuditLogDetailBean logDetail) {
        if (null == module) {
            throw new CommonException("Module cannot be null", ReasonCommon.MANDATORY_PARAM);
        }

        if (null == action) {
            throw new CommonException("Action cannot be null", ReasonCommon.MANDATORY_PARAM);
        }

        if (null == logDetail) {
           throw new CommonException("Log detail cannot be null", ReasonCommon.MANDATORY_PARAM);
        }
    }

    private TrDashboardAuditLog prepareAuditObject(DashboardModule module, DashboardAction action, AuditLogDetailBean logDetail, AuditContext audit) {


        MsLov lovModule = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_DASHBOARD_MODULE, module.toString());
        MsLov lovAction = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_DASHBOARD_ACTION, action.toString());

        Date currentDate = new Date();

        TrDashboardAuditLog auditLog = new TrDashboardAuditLog();
        auditLog.setAuditLogDate(currentDate);
        auditLog.setTrDashboardGroupH(logDetail.getDashboardGroupH());
        auditLog.setTrDashboardGroupD(logDetail.getDashboardGroupD());
        auditLog.setAmMsuser(logDetail.getUser());
        auditLog.setLovModule(lovModule);
        auditLog.setLovAction(lovAction);
        auditLog.setField(logDetail.getField());
        auditLog.setPreviousValue(logDetail.getPreviousValue());
        auditLog.setNewValue(logDetail.getNewValue());
        auditLog.setNotes(logDetail.getNotes());
        auditLog.setUsrCrt(audit.getCallerId());
        auditLog.setDtmCrt(currentDate);

        return auditLog;
    }
    
}
