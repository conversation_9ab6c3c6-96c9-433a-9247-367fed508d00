package com.adins.bsa.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "tr_ocr_result_summary_period")
public class TrOcrResultSummaryPeriod extends UpdateableEntity {

	private long idOcrResultSummaryPeriod;
	private TrDashboardGroupD trDashboardGroupD;
	private TrOcrResultHeader trOcrResultHeader;
	private Date period;
	private Integer totalTransaction;
	private Integer totalCreditTransaction;
	private Integer totalDebitTransaction;
	private Double totalCreditTransactionAmount;
	private Double totalDebitTransactionAmount;
	private BigDecimal openingBalance;
	private BigDecimal endingBalance;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ocr_result_summary_period", unique = true, nullable = false)
	public long getIdOcrResultSummaryPeriod() {
		return idOcrResultSummaryPeriod;
	}
	
	public void setIdOcrResultSummaryPeriod(long idOcrResultSummaryPeriod) {
		this.idOcrResultSummaryPeriod = idOcrResultSummaryPeriod;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_d", nullable = false)
	public TrDashboardGroupD getTrDashboardGroupD() {
		return trDashboardGroupD;
	}
	
	public void setTrDashboardGroupD(TrDashboardGroupD trDashboardGroupD) {
		this.trDashboardGroupD = trDashboardGroupD;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ocr_result_header", nullable = false)
	public TrOcrResultHeader getTrOcrResultHeader() {
		return trOcrResultHeader;
	}
	
	public void setTrOcrResultHeader(TrOcrResultHeader trOcrResultHeader) {
		this.trOcrResultHeader = trOcrResultHeader;
	}
	
	@Column(name = "period")
	public Date getPeriod() {
		return period;
	}
	
	public void setPeriod(Date period) {
		this.period = period;
	}
	
	@Column(name = "total_transaction")
	public Integer getTotalTransaction() {
		return totalTransaction;
	}
	
	public void setTotalTransaction(Integer totalTransaction) {
		this.totalTransaction = totalTransaction;
	}
	
	@Column(name = "total_credit_transaction")
	public Integer getTotalCreditTransaction() {
		return totalCreditTransaction;
	}
	
	public void setTotalCreditTransaction(Integer totalCreditTransaction) {
		this.totalCreditTransaction = totalCreditTransaction;
	}
	
	@Column(name = "total_debit_transaction")
	public Integer getTotalDebitTransaction() {
		return totalDebitTransaction;
	}
	
	public void setTotalDebitTransaction(Integer totalDebitTransaction) {
		this.totalDebitTransaction = totalDebitTransaction;
	}
	
	@Column(name = "total_credit_transaction_amount")
	public Double getTotalCreditTransactionAmount() {
		return totalCreditTransactionAmount;
	}
	
	public void setTotalCreditTransactionAmount(Double totalCreditTransactionAmount) {
		this.totalCreditTransactionAmount = totalCreditTransactionAmount;
	}
	
	@Column(name = "total_debit_transaction_amount")
	public Double getTotalDebitTransactionAmount() {
		return totalDebitTransactionAmount;
	}
	
	public void setTotalDebitTransactionAmount(Double totalDebitTransactionAmount) {
		this.totalDebitTransactionAmount = totalDebitTransactionAmount;
	}
	
	@Column(name = "opening_balance")
	public BigDecimal getOpeningBalance() {
		return openingBalance;
	}
	
	public void setOpeningBalance(BigDecimal openingBalance) {
		this.openingBalance = openingBalance;
	}
	
	@Column(name = "ending_balance")
	public BigDecimal getEndingBalance() {
		return endingBalance;
	}
	
	public void setEndingBalance(BigDecimal endingBalance) {
		this.endingBalance = endingBalance;
	}
	
}
