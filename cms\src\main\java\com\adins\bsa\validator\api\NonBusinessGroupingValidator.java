package com.adins.bsa.validator.api;

import com.adins.bsa.model.TrConsolidateResultNonbusinessGroupingD;
import com.adins.bsa.model.TrConsolidateResultNonbusinessGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface NonBusinessGroupingValidator {
	TrConsolidateResultNonbusinessGroupingH validateGetGroupingHeader(TrDashboardGroupH dashboardGroupH, String groupName, boolean objectMustExist, AuditContext audit);
	TrConsolidateResultNonbusinessGroupingD validateGetGroupingDetail(TrConsolidateResultNonbusinessGroupingH groupingH, String resultDetailId, boolean objectMustExist, AuditContext audit);
}
