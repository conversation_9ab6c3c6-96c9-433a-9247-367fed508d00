package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.BusinessTransactionGroupLogic;
import com.adins.bsa.webservices.frontend.api.BusinessTransactionGroupService;
import com.adins.bsa.webservices.model.businesstransactiongroup.ListBusinessTransactionGroupDetailResponse;
import com.adins.bsa.webservices.model.businesstransactiongroup.ListBusinessTransactionGroupResponse;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/businessTransactionGroup")
@Api(value = "DashboardService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericBusinessTransactionGroupServiceEndpoint implements BusinessTransactionGroupService {
	
	@Autowired private BusinessTransactionGroupLogic businessTransactionGroupLogic;

	@Override
	@POST
	@Path("/s/list")
	public ListBusinessTransactionGroupResponse getListBusinessTransactionGroup(GenericDashboardPagingRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return businessTransactionGroupLogic.getListBusinessTransactionGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/add")
	public MssResponseType addBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return businessTransactionGroupLogic.addBusinessTransactionGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/delete")
	public MssResponseType deleteBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return businessTransactionGroupLogic.deleteBusinessTransactionGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/listDetail")
	public ListBusinessTransactionGroupDetailResponse getListBusinessTransactionGroupDetail(ListNonBusinessTransactionGroupDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return businessTransactionGroupLogic.getListBusinessTransactionGroupDetail(request, audit);
	}

	@Override
	@POST
	@Path("/s/addDetails")
	public MssResponseType addBusinessTransactionGroupDetail(AddNonBusinessTransactionGroupDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return businessTransactionGroupLogic.addBusinessTransactionGroupDetail(request, audit);
	}

	@Override
	@POST
	@Path("/s/deleteDetail")
	public MssResponseType deleteBusinessTransactionGroupDetail(DeleteNonBusinessTransactionGroupDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return businessTransactionGroupLogic.deleteBusinessTransactionGroupDetail(request, audit);
	}

}
