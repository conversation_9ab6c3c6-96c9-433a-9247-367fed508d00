package com.adins.bsa.webservices.model.external.dashboard;

import com.adins.framework.service.base.model.MssRequestType;

public class UpdateBankStatementProcessStatusExternalRequest extends MssRequestType {
	
	private static final long serialVersionUID = 1L;
	private String fileSourcePath;
	private String processStatusCode;
	private String failMessage;
	
	public String getFileSourcePath() {
		return fileSourcePath;
	}
	public void setFileSourcePath(String fileSourcePath) {
		this.fileSourcePath = fileSourcePath;
	}
	public String getProcessStatusCode() {
		return processStatusCode;
	}
	public void setProcessStatusCode(String processStatusCode) {
		this.processStatusCode = processStatusCode;
	}
	public String getFailMessage() {
		return failMessage;
	}
	public void setFailMessage(String failMessage) {
		this.failMessage = failMessage;
	}
	
}
