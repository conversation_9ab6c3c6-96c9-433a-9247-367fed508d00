package com.adins.bsa.webservices.model.tenant;

import java.util.List;

import com.adins.bsa.custom.TenantBean;
import com.adins.framework.service.base.model.MssResponseType;

public class TenantListResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<TenantBean> tenants;
	private int page;
	private long totalPage;
	private long totalResult;
	
	public List<TenantBean> getTenants() {
		return tenants;
	}
	public void setTenants(List<TenantBean> tenants) {
		this.tenants = tenants;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public long getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}
	public long getTotalResult() {
		return totalResult;
	}
	public void setTotalResult(long totalResult) {
		this.totalResult = totalResult;
	}
	
}
