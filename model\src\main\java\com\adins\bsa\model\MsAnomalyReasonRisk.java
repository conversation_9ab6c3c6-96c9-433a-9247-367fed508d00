package com.adins.bsa.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "ms_anomaly_reason_risk")
public class MsAnomalyReasonRisk extends UpdateableEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private long idAnomalyReasonRisk;
	private MsTenant msTenant;
	private MsLov lovAnomalyReason;
	private MsLov lovAnomalyRisk;
	
	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_anomaly_reason_risk", unique = true, nullable = false)
	public long getIdAnomalyReasonRisk() {
		return idAnomalyReasonRisk;
	}
	public void setIdAnomalyReasonRisk(long idAnomalyReasonRisk) {
		this.idAnomalyReasonRisk = idAnomalyReasonRisk;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return msTenant;
	}
	
	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_anomaly_reason", nullable = false)
	public MsLov getLovAnomalyReason() {
		return lovAnomalyReason;
	}
	
	public void setLovAnomalyReason(MsLov lovAnomalyReason) {
		this.lovAnomalyReason = lovAnomalyReason;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_anomaly_risk", nullable = false)
	public MsLov getLovAnomalyRisk() {
		return lovAnomalyRisk;
	}
	
	public void setLovAnomalyRisk(MsLov lovAnomalyRisk) {
		this.lovAnomalyRisk = lovAnomalyRisk;
	}
}
