package com.adins.bsa.businesslogic.api;

import java.io.IOException;

import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.webservices.model.dataanalytics.DataAnalyticsConsolidateResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface DataAnalyticsLogic {

	DataAnalyticsConsolidateResponse consolidateDashboard(TrDashboardGroupH dashboardGroupH, AuditContext audit) throws IOException;
}
