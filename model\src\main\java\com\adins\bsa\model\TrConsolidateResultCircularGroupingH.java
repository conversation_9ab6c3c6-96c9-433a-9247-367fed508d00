package com.adins.bsa.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "tr_consolidate_result_circular_grouping_h")
public class TrConsolidateResultCircularGroupingH extends UpdateableEntity {
	
	public static final String KEY_ID_CIRC_GROUP_H = "idCircularGroupH";

	private long idConsolidateResultCircularGroupingH;
	private TrDashboardGroupH trDashboardGroupH;
	private TrOcrResultHeader trOcrResultHeader;
	private String groupName;
	private Integer rollingWindow;
	private String isUserEdited;
	private String isDeleted;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_consolidate_result_circular_grouping_h", unique = true, nullable = false)
	public long getIdConsolidateResultCircularGroupingH() {
		return idConsolidateResultCircularGroupingH;
	}
	
	public void setIdConsolidateResultCircularGroupingH(long idConsolidateResultCircularGroupingH) {
		this.idConsolidateResultCircularGroupingH = idConsolidateResultCircularGroupingH;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_h", nullable = false)
	public TrDashboardGroupH getTrDashboardGroupH() {
		return trDashboardGroupH;
	}
	
	public void setTrDashboardGroupH(TrDashboardGroupH trDashboardGroupH) {
		this.trDashboardGroupH = trDashboardGroupH;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ocr_result_header", nullable = false)
	public TrOcrResultHeader getTrOcrResultHeader() {
		return trOcrResultHeader;
	}
	
	public void setTrOcrResultHeader(TrOcrResultHeader trOcrResultHeader) {
		this.trOcrResultHeader = trOcrResultHeader;
	}
	
	@Column(name = "group_name", length = 64)
	public String getGroupName() {
		return groupName;
	}
	
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	
	@Column(name = "rolling_window")
	public Integer getRollingWindow() {
		return rollingWindow;
	}
	
	public void setRollingWindow(Integer rollingWindow) {
		this.rollingWindow = rollingWindow;
	}
	
	@Column(name = "is_user_edited", length = 1)
	public String getIsUserEdited() {
		return isUserEdited;
	}
	
	public void setIsUserEdited(String isUserEdited) {
		this.isUserEdited = isUserEdited;
	}
	
	@Column(name = "is_deleted", length = 1)
	public String getIsDeleted() {
		return isDeleted;
	}
	
	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}
	
}
