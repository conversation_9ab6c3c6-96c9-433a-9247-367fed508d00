package com.adins.bsa.webservices.model.aliyun;

import com.adins.framework.service.base.model.MssResponseType;

public class OssSignatureResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private String ossAccessKeyId;
	private String policy;
	private String signature;
	private String dir;
	private String host;
	
	public String getOssAccessKeyId() {
		return ossAccessKeyId;
	}
	public void setOssAccessKeyId(String ossAccessKeyId) {
		this.ossAccessKeyId = ossAccessKeyId;
	}
	public String getPolicy() {
		return policy;
	}
	public void setPolicy(String policy) {
		this.policy = policy;
	}
	public String getSignature() {
		return signature;
	}
	public void setSignature(String signature) {
		this.signature = signature;
	}
	public String getDir() {
		return dir;
	}
	public void setDir(String dir) {
		this.dir = dir;
	}
	public String getHost() {
		return host;
	}
	public void setHost(String host) {
		this.host = host;
	}
	
}
