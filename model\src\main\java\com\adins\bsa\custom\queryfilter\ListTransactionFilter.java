package com.adins.bsa.custom.queryfilter;

import com.adins.bsa.model.TrDashboardGroupH;

public class ListTransactionFilter {
	
	private TrDashboardGroupH dashboardGroupH;
	private String accountNo;
	private String category;
	private String type;
	private String amountMoreThan;
	private String amountLessThan;
	private String period;
	private String description;
	
	public TrDashboardGroupH getDashboardGroupH() {
		return dashboardGroupH;
	}
	public void setDashboardGroupH(TrDashboardGroupH dashboardGroupH) {
		this.dashboardGroupH = dashboardGroupH;
	}
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public String getCategory() {
		return category;
	}
	public void setCategory(String category) {
		this.category = category;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getAmountMoreThan() {
		return amountMoreThan;
	}
	public void setAmountMoreThan(String amountMoreThan) {
		this.amountMoreThan = amountMoreThan;
	}
	public String getAmountLessThan() {
		return amountLessThan;
	}
	public void setAmountLessThan(String amountLessThan) {
		this.amountLessThan = amountLessThan;
	}
	public String getPeriod() {
		return period;
	}
	public void setPeriod(String period) {
		this.period = period;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}

}
