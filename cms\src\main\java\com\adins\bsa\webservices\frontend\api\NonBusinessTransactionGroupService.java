package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailResponse;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface NonBusinessTransactionGroupService {
	ListNonBusinessTransactionGroupResponse getListNonBusinessTransactionGroup(GenericDashboardPagingRequest request);
	ListNonBusinessTransactionGroupDetailResponse getListNonBusinessTransactionGroupDetail(ListNonBusinessTransactionGroupDetailRequest request);
	MssResponseType addNonBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request);
	MssResponseType addNonBusinessTransactionGroup(AddNonBusinessTransactionGroupDetailRequest request);
	MssResponseType deleteNonBusinessTransactionGroup(DeleteNonBusinessTransactionGroupRequest request);
	MssResponseType deleteNonBusinessTransactionGroupDetail(DeleteNonBusinessTransactionGroupDetailRequest request);
	
}
