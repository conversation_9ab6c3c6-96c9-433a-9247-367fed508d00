package com.adins.bsa.custom.queryfilter;

import com.adins.bsa.model.TrDashboardGroupH;

public class ListOcrResultFilter {
	private TrDashboardGroupH dashboardGroupH;
	private String bankName;
	private String lovProcessResultCode;
	private int min;
	private int max;
	
	public TrDashboardGroupH getDashboardGroupH() {
		return dashboardGroupH;
	}
	public void setDashboardGroupH(TrDashboardGroupH dashboardGroupH) {
		this.dashboardGroupH = dashboardGroupH;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getLovProcessResultCode() {
		return lovProcessResultCode;
	}
	public void setLovProcessResultCode(String lovProcessResultCode) {
		this.lovProcessResultCode = lovProcessResultCode;
	}
	public int getMin() {
		return min;
	}
	public void setMin(int min) {
		this.min = min;
	}
	public int getMax() {
		return max;
	}
	public void setMax(int max) {
		this.max = max;
	}
	
}
