package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.CircularLogic;
import com.adins.bsa.webservices.frontend.api.CircularService;
import com.adins.bsa.webservices.model.circular.AddCircularGroupRequest;
import com.adins.bsa.webservices.model.circular.CheckCircularGroupNameRequest;
import com.adins.bsa.webservices.model.circular.ListCircularGroupRequest;
import com.adins.bsa.webservices.model.circular.ListCircularGroupResponse;
import com.adins.bsa.webservices.model.circular.ListCircularTransactionRequest;
import com.adins.bsa.webservices.model.circular.ListCircularTransactionResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/circular")
@Api(value = "CircularService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericCircularServiceEndpoint implements CircularService {
	
	@Autowired private CircularLogic circularLogic;
	
	@Override
	@POST
	@Path("/s/list")
	public ListCircularGroupResponse getListCircularGroup(ListCircularGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return circularLogic.getListCircularGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/checkGroupName")
	public MssResponseType checkCircularGroupName(CheckCircularGroupNameRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return circularLogic.checkCircularGroupName(request, audit);
	}

	@Override
	@POST
	@Path("/s/listTransaction")
	public ListCircularTransactionResponse getListCircularTransaction(ListCircularTransactionRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return circularLogic.getListCircularTransaction(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/add")
	public MssResponseType addCircularGroup(AddCircularGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return circularLogic.addCircularGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/delete")
	public MssResponseType deleteCircularGroup(CheckCircularGroupNameRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return circularLogic.deleteCircularGroup(request, audit);
	}

}
