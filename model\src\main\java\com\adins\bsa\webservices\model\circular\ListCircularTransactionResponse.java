package com.adins.bsa.webservices.model.circular;

import java.util.List;

import com.adins.bsa.custom.CircularTransactionBean;
import com.adins.bsa.webservices.model.common.GenericPagingResponse;

public class ListCircularTransactionResponse extends GenericPagingResponse {
	
	private static final long serialVersionUID = 1L;
	private List<CircularTransactionBean> transactions;
	
	public List<CircularTransactionBean> getTransactions() {
		return transactions;
	}
	public void setTransactions(List<CircularTransactionBean> transactions) {
		this.transactions = transactions;
	}
	
}
