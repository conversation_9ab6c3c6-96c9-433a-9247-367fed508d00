package com.adins.bsa.interceptor;

import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.AbstractPhaseInterceptor;
import org.apache.cxf.phase.Phase;

public class GetTokenInterceptor extends AbstractPhaseInterceptor<Message>{
		
	public GetTokenInterceptor() {
		super(Phase.RECEIVE);
	}
	
	@Override
	public void handleMessage(Message message) throws Fault {
		// Already empty from base project
	}


}
