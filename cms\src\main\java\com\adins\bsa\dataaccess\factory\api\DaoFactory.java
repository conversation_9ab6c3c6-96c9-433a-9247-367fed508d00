package com.adins.bsa.dataaccess.factory.api;

import com.adins.bsa.dataaccess.api.AnomalyDao;
import com.adins.bsa.dataaccess.api.BusinessTransactionGroupDao;
import com.adins.bsa.dataaccess.api.CircularDao;
import com.adins.bsa.dataaccess.api.CommonDao;
import com.adins.bsa.dataaccess.api.DashboardAuditLogDao;
import com.adins.bsa.dataaccess.api.DashboardGroupDao;
import com.adins.bsa.dataaccess.api.DashboardTabDao;
import com.adins.bsa.dataaccess.api.GeneralSettingDao;
import com.adins.bsa.dataaccess.api.LovDao;
import com.adins.bsa.dataaccess.api.MenuDao;
import com.adins.bsa.dataaccess.api.MessageTemplateDao;
import com.adins.bsa.dataaccess.api.NonBusinessTransactionGroupDao;
import com.adins.bsa.dataaccess.api.OauthAccessTokenDao;
import com.adins.bsa.dataaccess.api.OcrResultDao;
import com.adins.bsa.dataaccess.api.RoleDao;
import com.adins.bsa.dataaccess.api.SupplierBuyerGroupDao;
import com.adins.bsa.dataaccess.api.TenantDao;
import com.adins.bsa.dataaccess.api.UserDao;
import com.adins.bsa.dataaccess.api.UseroftenantDao;

public interface DaoFactory {
	CommonDao getCommonDao();
	DashboardGroupDao getDashboardGroupDao();
	NonBusinessTransactionGroupDao getNonBusinessTransactionGroupDao();
	GeneralSettingDao getGeneralSettingDao();
	LovDao getLovDao();
	MenuDao getMenuDao();
	OauthAccessTokenDao getAccessTokenDao();
	OcrResultDao getOcrResultDao();
	RoleDao getRoleDao();
	TenantDao getTenantDao();
	UserDao getUserDao();
	SupplierBuyerGroupDao getSupplierBuyerDao();
	AnomalyDao getAnomalyDao();
	MessageTemplateDao getMessageTemplateDao();
	CircularDao getCircularDao();
	BusinessTransactionGroupDao getBusinessTransactionGroupDao();
	DashboardAuditLogDao getDashboardAuditLogDao();
	UseroftenantDao getUseroftenantDao();
	DashboardTabDao getDashboardTabDao();
}
