package com.adins.bsa.webservices.model.ocrresult;

import java.io.Serializable;
import java.util.List;

import com.adins.bsa.custom.GetBankStatementTransactionSummaryCalculationResponseBean;
import com.adins.framework.service.base.model.MssResponseType;

public class GetBankStatementTransactionSummaryCalculationResponse extends MssResponseType implements Serializable {
	private static final long serialVersionUID = 1L;
	private List<GetBankStatementTransactionSummaryCalculationResponseBean> listSummary;
	
	public List<GetBankStatementTransactionSummaryCalculationResponseBean> getListSummary() {
		return listSummary;
	}
	public void setListSummary(List<GetBankStatementTransactionSummaryCalculationResponseBean> listSummary) {
		this.listSummary = listSummary;
	}
	
	
}
