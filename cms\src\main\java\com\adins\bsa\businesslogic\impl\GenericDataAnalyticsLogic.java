package com.adins.bsa.businesslogic.impl;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.businesslogic.api.DataAnalyticsLogic;
import com.adins.bsa.constants.HttpHeaders;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.webservices.model.dataanalytics.DataAnalyticsConsolidateResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.google.gson.Gson;

import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

@Component
public class GenericDataAnalyticsLogic extends BaseLogic implements DataAnalyticsLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericDataAnalyticsLogic.class);
	
	@Value("${dataanalytics.api.url.consolidate}") private String consolidateUrl;
	
	@Autowired private Gson gson;

	@Override
	public DataAnalyticsConsolidateResponse consolidateDashboard(TrDashboardGroupH dashboardGroupH, AuditContext audit) throws IOException {
		
		MsTenant tenant = dashboardGroupH.getMsTenant();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(15L, TimeUnit.SECONDS)
				.readTimeout(20L, TimeUnit.SECONDS)
				.build();
		
		Map<String, String> header = new HashMap<>();
		header.put(HttpHeaders.CONTENT_TYPE, HttpHeaders.MULTIPART_FORMDATA);
		Headers headers = Headers.of(header);
		
		FormBody requestBody = new FormBody.Builder()
				.add("dashboard_id", String.valueOf(dashboardGroupH.getIdDashboardGroupH()))
				.build();
		
		Map<String, String> requestBodyLog = new HashMap<>();
		requestBodyLog.put("dashboard_id", String.valueOf(dashboardGroupH.getIdDashboardGroupH()));
		
		Request request = new Request.Builder()
				.url(consolidateUrl)
				.headers(headers)
				.post(requestBody)
				.build();
		
		LOG.info("Tenant {}, Consolidate request: {}", tenant.getTenantName(), requestBodyLog);
		try (Response response = client.newCall(request).execute()) {
			String jsonResponse = response.body().string();
			int httpCode = response.code();
			
			LOG.info("Tenant {}, Consolidate response code: {}, body: {}", tenant.getTenantName(), httpCode, jsonResponse);
			return gson.fromJson(jsonResponse, DataAnalyticsConsolidateResponse.class);
		}
		
	}

}
