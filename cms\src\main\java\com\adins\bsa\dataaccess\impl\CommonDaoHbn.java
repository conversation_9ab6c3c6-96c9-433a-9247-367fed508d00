package com.adins.bsa.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmGeneralsetting;
import com.adins.bsa.dataaccess.api.CommonDao;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class CommonDaoHbn extends BaseDaoHbn implements CommonDao{

	@Override
	public String getUuidDao() {
		return this.getManagerDAO().getUUID();
	}

	@Override
	public long nextSequenceTrBalanceMutationTrxNo() {
		BigInteger result = (BigInteger) this.managerDAO.selectOneNativeString("select nextval('sq_trbalancemutation_trxno')", null);
		return result.longValue();
	}

	@Override
	public AmGeneralsetting getGeneralSetting(String code) {
		return this.managerDAO.selectOne(AmGeneralsetting.class, new Object[][] {{Restrictions.eq("gsCode", code)}});
	}

	@Override
	public AmGeneralsetting getGeneralSettingByTenant(String code, MsTenant tenant) {
		return this.managerDAO.selectOne(AmGeneralsetting.class, new Object[][] {
			{Restrictions.eq("gsCode", code)},
			{Restrictions.eq("msTenant", tenant)}
		});
	}

	@Override
	public void updateGeneralSetting(AmGeneralsetting generalSetting) {
		this.managerDAO.update(generalSetting);		
	}

	@Override
	public String executeDeleteBankStatement(TrDashboardGroupD dashboardGroupD, AuditContext audit) {
		Map<String, Object> params = new HashMap<>();
		params.put("idGroupD", dashboardGroupD.getIdDashboardGroupD());
		params.put("audit", audit.getCallerId());
		
		return (String) managerDAO.selectOneNativeString("select delete_bank_statement( :idGroupD , :audit )", params);
	}

	@Override
	public String executeDeleteOcrResultDetail(TrOcrResultDetail ocrResultDetail) {
		Map<String, Object> params = new HashMap<>();
		params.put("idOcrResultDetail", ocrResultDetail.getIdOcrResultDetail());
		
		return (String) managerDAO.selectOneNativeString("select delete_ocr_result_detail( :idOcrResultDetail )", params);
	}

	@Override
	public String executeDeleteDashboard(TrDashboardGroupH dashboardGroupH, AuditContext audit) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		params.put("audit", audit.getCallerId());
		
		return (String) managerDAO.selectOneNativeString("select delete_dashboard( :idDashboardGroupH , :audit )", params);
	}
	
}
