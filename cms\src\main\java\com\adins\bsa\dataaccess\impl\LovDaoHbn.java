package com.adins.bsa.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.bsa.dataaccess.api.LovDao;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.webservices.model.common.LovListRequest;

@Component
@Transactional
public class LovDaoHbn extends BaseDaoHbn implements LovDao {

	@Override
	public MsLov getLovByGroupAndCode(String lovGroup, String code) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(lovGroup));
		params.put(MsLov.CODE_HBM, StringUtils.upperCase(code));
		
		return managerDAO.selectOne(
				"from MsLov ml "
				+ "where ml.lovGroup = :lovGroup "
				+ "and ml.code = :code ", params);
	}
	
	@Override
	public List<Map<String, Object>> getMsLovListByGroupAndConstraint(LovListRequest request) {
		
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		
		query
			.append("select code, description, sequence, constraint_1, ")
			.append("constraint_2, constraint_3, constraint_4, constraint_5 from ms_lov ")
			.append("where lov_group = :lovGroup and is_active = '1' and is_deleted = '0' ");
		
		params.put(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(request.getLovGroup()));
		
		if (null != request.getConstraint1() && request.getConstraint1().length() > 0) {
			query.append("and constraint_1 = :constraint1 ");
			params.put(MsLov.CONSTRAINT_1_HBM, StringUtils.upperCase(request.getConstraint1()));
		}
		if (null != request.getConstraint2() && request.getConstraint2().length() > 0) {
			query.append("and constraint_2 = :constraint2 ");
			params.put(MsLov.CONSTRAINT_2_HBM, StringUtils.upperCase(request.getConstraint2()));
		}
		if (null != request.getConstraint3() && request.getConstraint3().length() > 0) {
			query.append("and constraint_3 = :constraint3 ");
			params.put(MsLov.CONSTRAINT_3_HBM, StringUtils.upperCase(request.getConstraint3()));
		}
		if (null != request.getConstraint4() && request.getConstraint4().length() > 0) {
			query.append("and constraint_4 = :constraint4 ");
			params.put(MsLov.CONSTRAINT_4_HBM, StringUtils.upperCase(request.getConstraint4()));
		}
		if (null != request.getConstraint5() && request.getConstraint5().length() > 0) {
			query.append("and constraint_5 = :constraint5 ");
			params.put(MsLov.CONSTRAINT_5_HBM, StringUtils.upperCase(request.getConstraint5()));
		}
		
		query.append("order by sequence asc ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	@Transactional(readOnly = true)
	public String getHighestSequenceLovCode(String lovGroup) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(lovGroup));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select code ")
			.append("from ms_lov ")
			.append("where lov_group = :lovGroup ")
			.append("order by sequence desc limit 1 ");
		
		return (String) managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	@Transactional(readOnly = true)
	public String getLowestSequenceLovCode(String lovGroup) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(lovGroup));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select code ")
			.append("from ms_lov ")
			.append("where lov_group = :lovGroup ")
			.append("order by sequence asc limit 1 ");
		
		return (String) managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public MsLov getLovByGroupAndId(String lovGroup, long idLov) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(lovGroup));
		params.put(MsLov.ID_LOV_HBM, idLov);
		
		return managerDAO.selectOne(
				"from MsLov ml "
				+ "where ml.lovGroup = :lovGroup "
				+ "and ml.idLov = :idLov ", params);
	}

}
