package com.adins.bsa.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import java.util.Date;

import com.adins.am.model.custom.ActiveAndUpdateableEntity;

@Entity
@Table(name = "tr_ocr_result_summary")
public class TrOcrResultSummary extends ActiveAndUpdateableEntity {
	private long idOcrResultSummary;
	private TrDashboardGroupD trDashboardGroupD;
	private TrOcrResultHeader trOcrResultHeader; 
	private Date period;
	private Integer totalTransaction;
	private Integer totalCreditTransaction;
	private Integer totalDebitTransaction;
	private Double totalCreditTransactionAmount;
	private Double totalDebitTransactionAmount;  
	private Double openingBalance; 
	private Double endingBalance;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ocr_result_summary", unique = true, nullable = false)
	public long getIdOcrResultSummary() {
		return idOcrResultSummary;
	}
	public void setIdOcrResultSummary(long idOcrResultSummary) {
		this.idOcrResultSummary = idOcrResultSummary;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_d", nullable = false)
	public TrDashboardGroupD getTrDashboardGroupD() {
		return trDashboardGroupD;
	}
	public void setTrDashboardGroupD(TrDashboardGroupD trDashboardGroupD) {
		this.trDashboardGroupD = trDashboardGroupD;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ocr_result_header", nullable = false)
	public TrOcrResultHeader getTrOcrResultHeader() {
		return trOcrResultHeader;
	}
	public void setTrOcrResultHeader(TrOcrResultHeader trOcrResultHeader) {
		this.trOcrResultHeader = trOcrResultHeader;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "period")
	public Date getPeriod() {
		return period;
	}
	public void setPeriod(Date period) {
		this.period = period;
	}
	
	@Column(name = "total_transaction")
	public Integer getTotalTransaction() {
		return totalTransaction;
	}
	public void setTotalTransaction(Integer totalTransaction) {
		this.totalTransaction = totalTransaction;
	}
	
	@Column(name = "total_credit_transaction")
	public Integer getTotalCreditTransaction() {
		return totalCreditTransaction;
	}
	public void setTotalCreditTransaction(Integer totalCreditTransaction) {
		this.totalCreditTransaction = totalCreditTransaction;
	}
	
	@Column(name = "total_debit_transaction")
	public Integer getTotalDebitTransaction() {
		return totalDebitTransaction;
	}
	public void setTotalDebitTransaction(Integer totalDebitTransaction) {
		this.totalDebitTransaction = totalDebitTransaction;
	}
	
	@Column(name = "total_credit_transaction_amount")
	public Double getTotalCreditTransactionAmount() {
		return totalCreditTransactionAmount;
	}
	public void setTotalCreditTransactionAmount(Double totalCreditTransactionAmount) {
		this.totalCreditTransactionAmount = totalCreditTransactionAmount;
	}
	
	@Column(name = "total_debit_transaction_amount")
	public Double getTotalDebitTransactionAmount() {
		return totalDebitTransactionAmount;
	}
	public void setTotalDebitTransactionAmount(Double totalDebitTransactionAmount) {
		this.totalDebitTransactionAmount = totalDebitTransactionAmount;
	}  
	
	@Column(name = "opening_balance")
	public Double getOpeningBalance() {
		return openingBalance;
	}
	public void setOpeningBalance(Double openingBalance) {
		this.openingBalance = openingBalance;
	}
	
	@Column(name = "ending_balance")
	public Double getEndingBalance() {
		return endingBalance;
	}
	public void setEndingBalance(Double endingBalance) {
		this.endingBalance = endingBalance;
	} 
	
	
	
}
