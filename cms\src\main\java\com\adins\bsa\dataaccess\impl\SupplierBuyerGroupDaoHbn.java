package com.adins.bsa.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.SupplierBuyerGroupBean;
import com.adins.bsa.custom.SupplierBuyerSubGroupMemberBean;
import com.adins.bsa.custom.SupplierBuyerSubGroupOfMainGroupBean;
import com.adins.bsa.custom.queryfilter.ListMasterSubGroupFilter;
import com.adins.bsa.dataaccess.api.SupplierBuyerGroupDao;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupD;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupDMember;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
@Transactional
public class SupplierBuyerGroupDaoHbn extends BaseDaoHbn implements SupplierBuyerGroupDao {
	
	@Override
	public TrConsolidateResultSupplierBuyerGroupH getTrConsolidateResultSupplierBuyerGroupH (TrDashboardGroupH trDashboardGroupH, String groupName) {
		Map<String, Object> params = new HashMap<>();
		params.put("groupName", groupName);
		params.put("dashboardGroupH", trDashboardGroupH);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultSupplierBuyerGroupH tsbgh "
				+ "join fetch tsbgh.trDashboardGroupH tdgh "
				+ "where tsbgh.groupName = :groupName "
				+ "and tsbgh.trDashboardGroupH = :dashboardGroupH ", params);
	}

	@Override
	public List<SupplierBuyerGroupBean> getList(String type, long idDashboarGroupH, int min, int max) {
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		params.put("idDashboard", idDashboarGroupH);
		
		StringBuilder query = new StringBuilder();
		query.append("select * from ( ")
			 .append("select row_number() over(order by h.dtm_crt desc) as \"no\", ")
			 .append(" group_name as \"mainGroup\", to_char(h.dtm_crt, 'DD-Mon-YYYY HH24:MI') as \"createdDate\", ")
			 .append(" to_char(h.dtm_upd, 'DD-Mon-YYYY HH24:MI') as \"lastUpdated\", group_type as \"type\" ")
			 .append("from tr_consolidate_result_supplier_buyer_group_h h ")
			 .append("where id_dashboard_group_h = :idDashboard  and is_deleted = '0' ");
	
		if (StringUtils.isNotBlank(type)) {
			query.append("and group_type = :type ");
			params.put("type", type);
		}
		
		query.append(" ) as a where no between :min and :max");
		
		return managerDAO.selectForListString(SupplierBuyerGroupBean.class, query.toString(), params, null);
	}

	@Override
	public long countList(String type, long idDashboarGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put("idDashboard", idDashboarGroupH);
		
		StringBuilder query = new StringBuilder();
		query.append("select count(h.*) ")
			 .append("from tr_consolidate_result_supplier_buyer_group_h h ")
			 .append("where id_dashboard_group_h = :idDashboard and is_deleted = '0' ");
		
		if (StringUtils.isNotBlank(type)) {
			query.append("and group_type = :type ");
			params.put("type", type);
		}
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		return result.longValue();
	}
	
	
	@Override
	public List<SupplierBuyerSubGroupOfMainGroupBean> getListSubGroupByMainGroup(TrDashboardGroupH dashboardGroupH, TrConsolidateResultSupplierBuyerGroupH trConsolidateResultSupplierBuyerGroupH) {
		 
		Object[][] params= new Object[][]{
			{"idMainGroup", trConsolidateResultSupplierBuyerGroupH.getIdConsolidateResultSupplierBuyerGroupH()},
			{TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH()}
		};

		StringBuilder query = new StringBuilder();
		query
			.append("select sub_group_name as \"subGroupName\" ")
			.append("from tr_consolidate_result_supplier_buyer_group_d ")
			.append("where (id_consolidate_result_supplier_buyer_group_h is null or id_consolidate_result_supplier_buyer_group_h = :idMainGroup ) ")
			.append("and id_dashboard_group_h = :idDashboardGroupH ")
			.append("and is_deleted = '0' ");
			
		return managerDAO.selectForListString(SupplierBuyerSubGroupOfMainGroupBean.class, query.toString(), params, null);
		
	}

	@Override
	public void insertGroupH(TrConsolidateResultSupplierBuyerGroupH groupH) {
		managerDAO.insert(groupH);
	}

	@Override
	public void updateGroupH(TrConsolidateResultSupplierBuyerGroupH groupH) {
		managerDAO.update(groupH);
	}

	@Override
	public void deleteGroupH(TrConsolidateResultSupplierBuyerGroupH groupH) {
		managerDAO.delete(groupH);
	}

	@Override
	public void updateGroupDSetGroupHeaderNull(TrConsolidateResultSupplierBuyerGroupH groupH, AuditContext audit) {
		Map<String, Object> params = new HashMap<>();
		params.put("idGroupH", groupH.getIdConsolidateResultSupplierBuyerGroupH());
		params.put("audit", audit.getCallerId());
		
		StringBuilder query = new StringBuilder();
		query
			.append("update tr_consolidate_result_supplier_buyer_group_d ")
			.append("set id_consolidate_result_supplier_buyer_group_h = null, ")
			.append("usr_upd = :audit , ")
			.append("dtm_upd = now() ")
			.append("where id_consolidate_result_supplier_buyer_group_h = :idGroupH ");
		
		managerDAO.updateNativeString(query.toString(), params);
	} 

	@Override
	public TrConsolidateResultSupplierBuyerGroupD getTrConsolidateResultSupplierBuyerGroupD (TrDashboardGroupH trDashboardGroupH, String subGroupName) {
		Map<String, Object> params = new HashMap<>();
		params.put("subGroupName", subGroupName);
		params.put("dashboardGroupH", trDashboardGroupH);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultSupplierBuyerGroupD tsbgd "
				+ "join fetch tsbgd.trDashboardGroupH tdgh "
				+ "left join fetch tsbgd.trConsolidateResultSupplierBuyerGroupH tsbgh "
				+ "where tsbgd.subGroupName = :subGroupName "
				+ "and tsbgd.trDashboardGroupH = :dashboardGroupH "
		        + "and tsbgd.isDeleted = '0' ", params);
	}
	
	@Override
	public void insertGroupD(TrConsolidateResultSupplierBuyerGroupD groupD) {
		managerDAO.insert(groupD); 
	}

	@Override
	public void updateGroupD(TrConsolidateResultSupplierBuyerGroupD groupD) {
		managerDAO.update(groupD); 
	}

	@Override
	public void deleteGroupD(TrConsolidateResultSupplierBuyerGroupD groupD) {
		managerDAO.delete(groupD); 
	}

	@Override
	public List<SupplierBuyerSubGroupOfMainGroupBean> getSubGroupNames(TrConsolidateResultSupplierBuyerGroupH groupH) {
		Map<String, Object> params = new HashMap<>();
		params.put("idGroupH", groupH.getIdConsolidateResultSupplierBuyerGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select sub_group_name as \"subGroupName\" ")
			.append("from tr_consolidate_result_supplier_buyer_group_d ")
			.append("where id_consolidate_result_supplier_buyer_group_h = :idGroupH ")
			.append("and is_deleted = '0' ")
			.append("order by sub_group_name asc ");
			
		return managerDAO.selectForListString(SupplierBuyerSubGroupOfMainGroupBean.class, query.toString(), params, null);
	}
	
	private StringBuilder buildMasterSubGroupConditionalParam(ListMasterSubGroupFilter filter, Map<String, Object> params) {
		
		StringBuilder queryParam = new StringBuilder();
		
		// dashboard_h param
		queryParam.append("and gd.id_dashboard_group_h = :idDashboardH ");
		params.put("idDashboardH", filter.getDashboardGroupH().getIdDashboardGroupH());
		
		// is deleted param
		queryParam.append("and gd.is_deleted = '0' ");
		
		// main group name param
		if (StringUtils.isNotBlank(filter.getMainGroupName())) {
			queryParam.append("and UPPER(gh.group_name) LIKE :groupName ");
			params.put("groupName", "%" + StringUtils.upperCase(filter.getMainGroupName()) + "%");
		}
		
		// sub group name param
		if (StringUtils.isNotBlank(filter.getSubGroupName())) {
			queryParam.append("and UPPER(gd.sub_group_name) LIKE :subGroupName ");
			params.put("subGroupName", "%" + StringUtils.upperCase(filter.getSubGroupName()) + "%");
		}
		
		return queryParam;
	}


	@Override
	public List<SupplierBuyerSubGroupOfMainGroupBean> getListMasterSubGroup(ListMasterSubGroupFilter filter) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("min", filter.getMin());
		params.put("max", filter.getMax());
		
		StringBuilder conditionalParam = buildMasterSubGroupConditionalParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by gd.dtm_upd desc) as \"rowNum\", ")
				.append("gd.sub_group_name as \"subGroupName\", gh.group_name as \"mainGroupName\", ")
				.append("to_char(gd.dtm_crt, 'DD/MM/YYYY HH24:MI') as \"createDate\", ")
				.append("to_char(gd.dtm_upd, 'DD/MM/YYYY HH24:MI') as \"lastUpdated\" ")
				.append("from tr_consolidate_result_supplier_buyer_group_d gd ")
				.append("left join tr_consolidate_result_supplier_buyer_group_h gh on gd.id_consolidate_result_supplier_buyer_group_h = gh.id_consolidate_result_supplier_buyer_group_h ")
				.append("where 1=1 ")
				.append(conditionalParam)
			.append(") ")
			.append("select * from cte ")
			.append("where \"rowNum\" between :min and :max ");
		
		return managerDAO.selectForListString(SupplierBuyerSubGroupOfMainGroupBean.class, query.toString(), params, null);
	}

	@Override
	public long countListMasterSubGroup(ListMasterSubGroupFilter filter) {
		
		Map<String, Object> params = new HashMap<>();
		StringBuilder conditionalParam = buildMasterSubGroupConditionalParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_consolidate_result_supplier_buyer_group_d gd ")
			.append("left join tr_consolidate_result_supplier_buyer_group_h gh on gd.id_consolidate_result_supplier_buyer_group_h = gh.id_consolidate_result_supplier_buyer_group_h ")
			.append("where 1=1 ")
			.append(conditionalParam);
		
		BigInteger total = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == total) {
			return 0;
		}
		
		return total.longValue();
	}

	@Override
	public void deleteGroupDetailMembers(TrConsolidateResultSupplierBuyerGroupD groupD, String isUserEdited) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultSupplierBuyerGroupD.PARAM_ID_GROUP_D, groupD.getIdConsolidateResultNonbusinessGroupingD());
		params.put(GlobalVal.QUERY_PARAM_IS_USER_EDITED, isUserEdited);
		
		StringBuilder query = new StringBuilder();
		query
			.append("delete from tr_consolidate_result_supplier_buyer_group_d_member ")
			.append("where id_consolidate_result_supplier_buyer_group_d = :idGroupD ")
			.append("and is_user_edited = :isUserEdited ");
		
		managerDAO.deleteNativeString(query.toString(), params);
	}

	@Override
	public void deleteGroupDetailMembers(TrConsolidateResultSupplierBuyerGroupD groupD) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultSupplierBuyerGroupD.PARAM_ID_GROUP_D, groupD.getIdConsolidateResultNonbusinessGroupingD());
		
		StringBuilder query = new StringBuilder();
		query
			.append("delete from tr_consolidate_result_supplier_buyer_group_d_member ")
			.append("where id_consolidate_result_supplier_buyer_group_d = :idGroupD ");
		
		managerDAO.deleteNativeString(query.toString(), params);
	}

	@Override
	public void updateGroupDetailMembersFlagAsDeleted(TrConsolidateResultSupplierBuyerGroupD groupD, String isUserEdited, AuditContext audit) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultSupplierBuyerGroupD.PARAM_ID_GROUP_D, groupD.getIdConsolidateResultNonbusinessGroupingD());
		params.put(GlobalVal.QUERY_PARAM_IS_USER_EDITED, isUserEdited);
		params.put("audit", audit.getCallerId());
		
		StringBuilder query = new StringBuilder();
		query
			.append("update tr_consolidate_result_supplier_buyer_group_d_member ")
			.append("set is_deleted = '1', ")
			.append("is_user_edited = '1', ")
			.append("usr_upd = :audit , ")
			.append("dtm_upd = now() ")
			.append("where id_consolidate_result_supplier_buyer_group_d = :idGroupD ")
			.append("and is_user_edited = :isUserEdited ");
		
		managerDAO.updateNativeString(query.toString(), params);
	}

	@Override
	public List<SupplierBuyerSubGroupMemberBean> getListSupplierBuyerSubGroupMember(TrConsolidateResultSupplierBuyerGroupD groupD, int min, int max) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultSupplierBuyerGroupD.PARAM_ID_GROUP_D, groupD.getIdConsolidateResultNonbusinessGroupingD());
		params.put(GlobalVal.QUERY_PARAM_MIN, min);
		params.put(GlobalVal.QUERY_PARAM_MAX, max);
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by gdm.dtm_crt desc) as \"rowNum\", ")
				.append("dgd.file_name as \"file\", orh.account_number as \"accountNo\", ")
				.append("to_char(ord.transaction_date, 'DD/MM/YYYY') as \"date\", ")
				.append("ord.transaction_desc as \"description\", ord.transaction_type as \"type\", ")
				.append("ord.result_detail_id as \"resultDetailId\", ord.transaction_amount as \"amount\" ")
				.append("from tr_consolidate_result_supplier_buyer_group_d_member gdm ")
				.append("join tr_ocr_result_detail ord on gdm.id_ocr_result_detail = ord.id_ocr_result_detail ")
				.append("join tr_ocr_result_header orh on ord.id_ocr_result_header = orh.id_ocr_result_header ")
				.append("join tr_dashboard_group_d dgd on ord.id_dashboard_group_d = dgd.id_dashboard_group_d ")
				.append("where gdm.id_consolidate_result_supplier_buyer_group_d = :idGroupD ")
				.append("and gdm.is_deleted = '0' ")
			.append(") ")
			.append("select * from cte ")
			.append("where \"rowNum\" between :min and :max ");
		
		return managerDAO.selectForListString(SupplierBuyerSubGroupMemberBean.class, query.toString(), params, null);
	}

	@Override
	public long countListSupplierBuyerSubGroupMember(TrConsolidateResultSupplierBuyerGroupD groupD) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultSupplierBuyerGroupD.PARAM_ID_GROUP_D, groupD.getIdConsolidateResultNonbusinessGroupingD());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_consolidate_result_supplier_buyer_group_d_member gdm ")
			.append("where gdm.id_consolidate_result_supplier_buyer_group_d = :idGroupD ")
			.append("and gdm.is_deleted = '0' ");
		
		BigInteger total = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == total) {
			return 0;
		}
		
		return total.longValue();
	}

	@Override
	public TrConsolidateResultSupplierBuyerGroupDMember getGroupDetailMember(TrConsolidateResultSupplierBuyerGroupD groupD, String resultDetailId) {
		Map<String, Object> params = new HashMap<>();
		params.put("groupD", groupD);
		params.put(TrOcrResultDetail.PARAM_RESULT_DETAIL_ID, resultDetailId);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultSupplierBuyerGroupDMember gdm "
				+ "join fetch gdm.trConsolidateResultSupplierBuyerGroupD gd "
				+ "join fetch gdm.trOcrResultDetail ord "
				+ "where gdm.trConsolidateResultSupplierBuyerGroupD = :groupD "
				+ "and ord.resultDetailId = :resultDetailId "
				+ "and gdm.isDeleted = '0' ", params);
	}

	@Override
	public void updateGroupDetailMember(TrConsolidateResultSupplierBuyerGroupDMember member) {
		managerDAO.update(member);
	}

	@Override
	public void deleteGroupDetailMember(TrConsolidateResultSupplierBuyerGroupDMember member) {
		managerDAO.delete(member);
	}

	@Override
	public TrConsolidateResultSupplierBuyerGroupDMember getGroupDetailMember(String resultDetailId) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrOcrResultDetail.PARAM_RESULT_DETAIL_ID, resultDetailId);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultSupplierBuyerGroupDMember gdm "
				+ "join fetch gdm.trConsolidateResultSupplierBuyerGroupD gd "
				+ "join fetch gdm.trOcrResultDetail ord "
				+ "where ord.resultDetailId = :resultDetailId "
				+ "and gdm.isDeleted = '0' ", params);
	}

	@Override
	public void insertGroupDetailMember(TrConsolidateResultSupplierBuyerGroupDMember member) {
		managerDAO.insert(member);
	}
	
}
