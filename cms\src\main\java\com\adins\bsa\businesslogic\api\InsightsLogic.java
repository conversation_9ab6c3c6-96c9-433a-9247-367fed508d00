package com.adins.bsa.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;
import com.adins.bsa.webservices.model.insights.ConsolidateDocumentDownloadUrlRequest;
import com.adins.bsa.webservices.model.insights.ConsolidateDocumentDownloadUrlResponse;
import com.adins.bsa.webservices.model.insights.ConsolidatedBankStatementListResponse;
import com.adins.bsa.webservices.model.insights.EditDashboardNameRequest;
import com.adins.bsa.webservices.model.insights.GeneralBodyCardDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralCircularChartDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralDailyAnalysisDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralMonthlyCashFlowAnalysisDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsGeneralCircularHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsGeneralHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsInformationResponse;
import com.adins.bsa.webservices.model.insights.InsightsSupplierBuyerHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerGpmWcrLrResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerMonthlyCashflowAndGrowthRateResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerTopFiveChartResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface InsightsLogic {

	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	InsightsInformationResponse getBasicInformation(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	ConsolidatedBankStatementListResponse getConsolidatedBankStatements(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType editDashboardName(EditDashboardNameRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	ConsolidateDocumentDownloadUrlResponse getConsolidateDocumentDownloadUrl(ConsolidateDocumentDownloadUrlRequest request, AuditContext audit);
	
	// INSIGHTS - SUPPLIER BUYER TAB START
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	InsightsSupplierBuyerHeaderDataResponse getSupplierBuyerHeaderData(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	SupplierBuyerMonthlyCashflowAndGrowthRateResponse getSupplierBuyerMonthlyCashflowAndGrowthRate(GenericDashboardDropdownListRequest request, AuditContext audit);

	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	SupplierBuyerGpmWcrLrResponse getSupplierBuyerGpmWcrLr(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	SupplierBuyerTopFiveChartResponse getSupplierBuyerTopFiveChartData(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	// INSIGHTS - SUPPLIER BUYER TAB FINISH
	
	// INSIGHTS - GENERAL START
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	InsightsGeneralHeaderDataResponse getGeneralInsightsHeaderData(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	GeneralMonthlyCashFlowAnalysisDataResponse getGeneralMonthlyCashFlowAnalysisData(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	GeneralBodyCardDataResponse getGeneralBodyCardData(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	GeneralDailyAnalysisDataResponse getGeneralDailyAnalysisData(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	InsightsGeneralCircularHeaderDataResponse getGeneralCircularHeaderData(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	GeneralCircularChartDataResponse getGeneralCircularChartData(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	// INSIGHTS - GENERAL END
}
