package com.adins.bsa.businesslogic.external.impl;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.businesslogic.external.api.DashboardExternalLogic;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;
import com.adins.bsa.validator.api.DashboardValidator;
import com.adins.bsa.validator.api.LovValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.webservices.model.external.dashboard.UpdateBankStatementProcessStatusExternalRequest;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

@Component
public class GenericDashboardExternalLogic extends BaseLogic implements DashboardExternalLogic {
	
	@Autowired private DashboardValidator dashboardValidator;
	@Autowired private LovValidator lovValidator;
	@Autowired private TenantValidator tenantValidator;

	@Override
	public MssResponseType updateBankStatementProcessStatus(UpdateBankStatementProcessStatusExternalRequest request, String xApiKey, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenantByXApiKey(xApiKey, audit);
		TrDashboardGroupD dashboardGroupD = dashboardValidator.getDashboardGroupD(tenant, request.getFileSourcePath(), true, audit);
		if (!GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(dashboardGroupD.getLovProcessStatus().getCode())) {
			throw new CommonException(getMessage("businesslogic.ocrresult.cannotedit", null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		MsLov lovProcessStatus = lovValidator.getLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_STATUS, request.getProcessStatusCode(), audit);
		
		Date updateTime = new Date();
		
		dashboardGroupD.setLovProcessStatus(lovProcessStatus);
		dashboardGroupD.setProcessResultMessage(StringUtils.left(request.getFailMessage(), 256));
		dashboardGroupD.setUsrUpd(audit.getCallerId());
		dashboardGroupD.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardD(dashboardGroupD);
		
		TrDashboardGroupH dashboardGroupH = dashboardGroupD.getTrDashboardGroupH();
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_FAILED.equals(lovProcessStatus.getCode())) {
			dashboardGroupH.setIsConsolidating("0");
		}
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setOcrResultLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

}
