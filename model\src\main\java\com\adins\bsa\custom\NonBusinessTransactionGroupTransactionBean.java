package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

public class NonBusinessTransactionGroupTransactionBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private BigInteger row;
	private String resultDetailId;
	private String file;
	private String accountNo;
	private String date;
	private String description;
	private BigDecimal amount;
	private String type;
	
	
	public BigInteger getRow() {
		return row;
	}
	public void setRow(BigInteger row) {
		this.row = row;
	}
	public String getFile() {
		return file;
	}
	public void setFile(String file) {
		this.file = file;
	}
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public BigDecimal getAmount() {
		return amount;
	}
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getResultDetailId() {
		return resultDetailId;
	}
	public void setResultDetailId(String resultDetailId) {
		this.resultDetailId = resultDetailId;
	}
	
	
}
