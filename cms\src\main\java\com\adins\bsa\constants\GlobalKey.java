package com.adins.bsa.constants;

public class GlobalKey {
	/**
	 * The class shall not be instantiated.
	 * This constructor will throw IllegalStateException when instantiated.
	 */
	protected GlobalKey() {
		throw new IllegalStateException("GlobalKey class shall not be instantiated! Class=" + this.getClass().getName());
	}


	/**
	 * Key for for storing exception error code, specified in as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_EXCEPTION = "excp";

	/**
	 * Key for for storing last action result, specified in as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_LAST_RESULT = "lastResult";

	/**
	 * Key for for storing default previous parameter for redirection, as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_REDIR_PARAM_MAP = "redir.params";

	/**
	 * Key for for storing default target for redirection, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_REDIR_TARGET = "redir.target";

	/**
	 *  Key for for storing state, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_STATE = "state";

	/**
	 *  Key for for storing mode, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_ACTION_NAME = "actionName";

	/**
	 *  Key for for storing mode, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_MODE = "mode";

	/**
	 * Property key for <i>other</i> context factory.
	 */
	public static final String CONTEXT_FACTORY = "context.factory";

	/**
	 * Property key for <i>other</i> Context URL provider.
	 */
	public static final String CONTEXT_URL = "context.url";

	/**
	 * Property key for initializing group of Context Class locations.
	 */
	public static final String CONTEXT_INITIALIZER_CLASS = "initializerClassLocation";
	
	/**
	 * Property key for database (datasource) JNDI name.
	 */
	public static final String JNDI_DATASOURCE = "jndi.datasource";

	/**
	 * Key for for storing result list object, used in map for storing query result.
	 */
	public static final String MAP_RESULT_LIST = "resultList";

	/**
	 * Key for for storing size of result list object, used in map for storing query result.
	 */
	public static final String MAP_RESULT_SIZE = "resultSize";

	/**
	 * Key for for storing result list object with that version, used in map for storing query result.
	 */
	public static final String MAP_RESULT_VERSION = "resultVersion";

	/**
	 * String that is used for storing action message.
	 */
	public static final String SESSION_ACTION_MESSAGE = "SESSION_ACTION_MESSAGE";

	/**
	 *Key for storing current parameter information in application session.
	 */
	public static final String SESSION_CURR_PARAMETER = "SESSION_CURR_PARAMETER";

	/**
	 *Key for storing login information in application session.
	 */
	public static final String SESSION_LOGIN = "SESSION_LOGIN";

	/**
	 * String that is used for storing model version for concurrency checking.
	 */
	public static final String SESSION_MODEL_VERSION = "SESSION_MODEL_VERSION";

	/**
	 *Key for storing push parameter information in application session.
	 */
	public static final String SESSION_PUSH_PARAMETER = "SESSION_PUSH_PARAMETER";

	/**
	 *Key for storing temporary parameter information in application session.
	 */
	public static final String SESSION_TEMP_PARAMETER = "SESSION_TEMP_PARAMETER";

	/**
	 *Key for specifying location of file application properties when using in system environment.
	 */
	public static final String SYSTEM_ENVIRONMENT = "application.properties";
	
	/**
	 * Query paging parameter
	 */
	public static final String PAGING_PARAM_MIN = "min";
	public static final String PAGING_PARAM_MAX = "max";

	/**
	 * Error message key
	 */
	public static final String MSG_COMMON_MANDATORY = "businesslogic.global.mandatory";
	public static final String MSG_COMMON_STRING_MAX_LENGTH = "businesslogic.global.stringmaxlength";
	public static final String MSG_COMMON_STRING_MIN_LENGTH = "businesslogic.global.stringminlength";
	public static final String MSG_COMMON_OBJ_NOT_FOUND = "businesslogic.global.objectnotfound";
	public static final String MSG_COMMON_FORBIDDEN = "businesslogic.global.forbiddenaccess";
	public static final String MSG_COMMON_NUMBER_ONLY = "businesslogic.global.numeric";
	public static final String MSG_COMMON_POSITIVE_NUMBER_ONLY = "businesslogic.global.positivenumeric";
	public static final String MSG_COMMON_MUST_BE_FILLED_WITH = "businesslogic.global.mustbefilledwith";
	public static final String MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1 = "businesslogic.global.objectnotfound1";
	public static final String MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_2 = "businesslogic.global.objectnotfound2";
	public static final String MESSAGE_ERROR_GLOBAL_DATAINVALIDSTATUS = "businesslogic.global.datainvalidstatus";
	public static final String MESSAGE_ERROR_GLOBAL_FAILED_GETDOCUMENT = "businesslogic.global.failedtogetossdocument";
	public static final String MESSAGE_ERROR_GLOBAL_ALREADY_USED = "businesslogic.global.alreadyused";
	
	public static final String MSG_COMMON_DECIMAL_DIGIT = "businesslogic.global.decimal.digit";
	public static final String MSG_COMMON_DECIMAL_POINT = "businesslogic.global.decimal.point";
	public static final String MSG_COMMON_DECIMAL_MAXVALUE = "businesslogic.global.decimal.maxvalue";
	
	public static final String MSG_USERVAL_EMAIL_NOT_FOUND = "businesslogic.userval.emailnotfound";
	
	public static final String MSG_OCR_RESULT_TRX_OVERLAPPED = "businesslogic.ocrresult.transactionoverlapped";
	public static final String MSG_OCR_RESULT_STILL_PROCESSED = "businesslogic.ocrresult.stillprocessed";
	
	public static final String MSG_SUPPLIER_BUYER_INVALID_TYPE = "businesslogic.supplierbuyergroup.invalidtype";
	public static final String MSG_TRX_TYPE_INVALID_TYPE = "businesslogic.transactiontype.invalidtype";
	
}
