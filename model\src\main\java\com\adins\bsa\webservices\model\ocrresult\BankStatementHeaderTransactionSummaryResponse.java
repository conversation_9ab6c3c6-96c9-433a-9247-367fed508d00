package com.adins.bsa.webservices.model.ocrresult;

import java.io.Serializable;
import java.util.List;

import com.adins.bsa.custom.BankStatementHeaderSummaryBean;
import com.adins.framework.service.base.model.MssResponseType;

public class BankStatementHeaderTransactionSummaryResponse extends MssResponseType implements Serializable {
	private static final long serialVersionUID = 1L;
	private List<BankStatementHeaderSummaryBean> summaries;
	private String isEdited;
	
	public List<BankStatementHeaderSummaryBean> getSummaries() {
		return summaries;
	}
	public void setSummaries(List<BankStatementHeaderSummaryBean> summaries) {
		this.summaries = summaries;
	}
	public String getIsEdited() {
		return isEdited;
	}
	public void setIsEdited(String isEdited) {
		this.isEdited = isEdited;
	}
 
}
