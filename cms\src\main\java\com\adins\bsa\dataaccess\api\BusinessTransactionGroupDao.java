package com.adins.bsa.dataaccess.api;

import java.util.List;

import com.adins.bsa.custom.NonBusinessTransactionGroupBean;
import com.adins.bsa.custom.NonBusinessTransactionGroupDetailBean;
import com.adins.bsa.model.TrConsolidateResultBusinessGroupingD;
import com.adins.bsa.model.TrConsolidateResultBusinessGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface BusinessTransactionGroupDao {
	
	// tr_consolidate_result_business_grouping_h
	
	void insertBusinessGroupingH(TrConsolidateResultBusinessGroupingH businessGroupingH);
	void updateBusinessGroupingH(TrConsolidateResultBusinessGroupingH businessGroupingH);
	void deleteBusinessGroupingH(TrConsolidateResultBusinessGroupingH businessGroupingH);
	
	TrConsolidateResultBusinessGroupingH getBusinessGroupingH(TrDashboardGroupH dashboardGroupH, String groupName);
	
	// tr_consolidate_result_business_grouping_d
	void insertBusinessGroupingD(TrConsolidateResultBusinessGroupingD groupingD);
	
	void updateBusinessGroupingD(TrConsolidateResultBusinessGroupingD groupingD);
	void updateBusinessGroupingDIsDeleted(TrConsolidateResultBusinessGroupingH businessGroupingH, String isUserEdited, String isDeleted, AuditContext audit);
	
	void deleteBusinessGroupingD(TrConsolidateResultBusinessGroupingD businessGroupingD);
	void deleteBusinessGroupingD(TrConsolidateResultBusinessGroupingH businessGroupingH);
	void deleteBusinessGroupingD(TrConsolidateResultBusinessGroupingH businessGroupingH, String isUserEdited);
	
	TrConsolidateResultBusinessGroupingD getBusinessGroupingD(TrOcrResultDetail ocrResultDetail);
	TrConsolidateResultBusinessGroupingD getBusinessGroupingD(TrConsolidateResultBusinessGroupingH groupingH, String resultDetailId);
	
	// Paging
	List<NonBusinessTransactionGroupBean> getListBusinessTransactionGroup(TrDashboardGroupH dashboardGroupH, int min, int max);
	long countListBusinessTransactionGroup(TrDashboardGroupH dashboardGroupH);
	
	List<NonBusinessTransactionGroupDetailBean> getListBusinessTransactionGroupDetail(TrConsolidateResultBusinessGroupingH groupingH, int min, int max);
	long countListBusinessTransactionGroupDetail(TrConsolidateResultBusinessGroupingH groupingH);
}
