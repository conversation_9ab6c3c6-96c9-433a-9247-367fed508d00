package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.InsightsLogic;
import com.adins.bsa.webservices.frontend.api.InsightsService;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;
import com.adins.bsa.webservices.model.insights.ConsolidateDocumentDownloadUrlRequest;
import com.adins.bsa.webservices.model.insights.ConsolidateDocumentDownloadUrlResponse;
import com.adins.bsa.webservices.model.insights.ConsolidatedBankStatementListResponse;
import com.adins.bsa.webservices.model.insights.EditDashboardNameRequest;
import com.adins.bsa.webservices.model.insights.GeneralBodyCardDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralCircularChartDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralDailyAnalysisDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralMonthlyCashFlowAnalysisDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsGeneralCircularHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsGeneralHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsInformationResponse;
import com.adins.bsa.webservices.model.insights.InsightsSupplierBuyerHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerGpmWcrLrResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerMonthlyCashflowAndGrowthRateResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerTopFiveChartResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/insights")
@Api(value = "InsightsService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericInsightsServiceEndpoint implements InsightsService {
	
	@Autowired private InsightsLogic insightsLogic;
	
	@Override
	@POST
	@Path("/s/basicInformation")
	public InsightsInformationResponse getBasicInformation(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getBasicInformation(request, audit);
	}

	@Override
	@POST
	@Path("/s/consolidatedBankStatementList")
	public ConsolidatedBankStatementListResponse getConsolidatedBankStatements(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getConsolidatedBankStatements(request, audit);
	}

	@Override
	@POST
	@Path("/s/editDashboardName")
	public MssResponseType editDashboardName(EditDashboardNameRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.editDashboardName(request, audit);
	}

	@Override
	@POST
	@Path("/s/consolidateDocumentUrl")
	public ConsolidateDocumentDownloadUrlResponse getConsolidateDocumentDownloadUrl(ConsolidateDocumentDownloadUrlRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getConsolidateDocumentDownloadUrl(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/supplierBuyerInsightHeader")
	public InsightsSupplierBuyerHeaderDataResponse getSupplierBuyerHeaderData(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getSupplierBuyerHeaderData(request, audit);
	}

	@Override
	@POST
	@Path("/s/supplierBuyerMonthlyCashflowAndGrowthRate")
	public SupplierBuyerMonthlyCashflowAndGrowthRateResponse getSupplierBuyerMonthlyCashflowAndGrowthRate(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getSupplierBuyerMonthlyCashflowAndGrowthRate(request, audit);
	}

	@Override
	@POST
	@Path("/s/supplierBuyerGpmWcrLr")
	public SupplierBuyerGpmWcrLrResponse getSupplierBuyerGpmWcrLr(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getSupplierBuyerGpmWcrLr(request, audit);
	}

	@Override
	@POST
	@Path("/s/supplierBuyerTopFive")
	public SupplierBuyerTopFiveChartResponse getSupplierBuyerTopFiveChartData(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getSupplierBuyerTopFiveChartData(request, audit);
	}

	@Override
	@POST
	@Path("/s/generalInsightHeader")
	public InsightsGeneralHeaderDataResponse getGeneralInsightsHeaderData(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getGeneralInsightsHeaderData(request, audit);
	}

	@Override
	@POST
	@Path("/s/generalCashFlow")
	public GeneralMonthlyCashFlowAnalysisDataResponse getGeneralMonthlyCashFlowAnalysisData(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getGeneralMonthlyCashFlowAnalysisData(request, audit);
	}

	@Override
	@POST
	@Path("/s/generalInsightBodyCard")
	public GeneralBodyCardDataResponse getGeneralBodyCardData(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getGeneralBodyCardData(request, audit);
	}

	@Override
	@POST
	@Path("/s/generalDailyAnalysis")
	public GeneralDailyAnalysisDataResponse getGeneralDailyAnalysisData(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getGeneralDailyAnalysisData(request, audit);
	}

	@Override
	@POST
	@Path("/s/generalCircularHeader")
	public InsightsGeneralCircularHeaderDataResponse getGeneralCircularHeaderData(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getGeneralCircularHeaderData(request, audit);
	}

	@Override
	@POST
	@Path("/s/generalCircularChartData")
	public GeneralCircularChartDataResponse getGeneralCircularChartData(GenericDashboardDropdownListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return insightsLogic.getGeneralCircularChartData(request, audit);
	}

}
