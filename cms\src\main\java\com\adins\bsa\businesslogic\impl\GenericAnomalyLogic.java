package com.adins.bsa.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.businesslogic.api.AnomalyLogic;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.AddAnomalyBean;
import com.adins.bsa.custom.AddAnomalyRequestBean;
import com.adins.bsa.custom.AnomalyBean;
import com.adins.bsa.custom.AnomalyGroupBean;
import com.adins.bsa.custom.AnomalyMetadataBean;
import com.adins.bsa.model.MsAnomalyReasonRisk;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrConsolidateResultAnomaly;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.model.TrOcrResultHeader;
import com.adins.bsa.validator.api.AnomalyReasonRiskValidator;
import com.adins.bsa.validator.api.DashboardValidator;
import com.adins.bsa.validator.api.ObjectRequestValidator;
import com.adins.bsa.validator.api.LovValidator;
import com.adins.bsa.validator.api.OcrResultValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.bsa.webservices.model.anomaly.AddAnomaliesRequest;
import com.adins.bsa.webservices.model.anomaly.DeleteAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.DeleteGroupAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyMetadataRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyMetadataResponse;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyResponse;
import com.adins.bsa.webservices.model.anomaly.ListGroupAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListGroupAnomalyResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

@Component
public class GenericAnomalyLogic extends BaseLogic implements AnomalyLogic {

	@Autowired private TenantValidator tenantValidator;
	@Autowired private UserValidator userValidator;
	@Autowired private DashboardValidator dashboardValidator;
	@Autowired private OcrResultValidator ocrResultValidator;
	@Autowired private LovValidator lovValidator;
	@Autowired private AnomalyReasonRiskValidator riskValidator;
	@Autowired private ObjectRequestValidator requestValidator;
	
	@Override
	public ListAnomalyResponse getList(ListAnomalyRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);

		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		if (StringUtils.isNotBlank(request.getType())
		&& !GlobalVal.TRX_TYPE_CREDIT.equalsIgnoreCase(request.getType())
		&& !GlobalVal.TRX_TYPE_DEBIT.equalsIgnoreCase(request.getType())
		&& !GlobalVal.TRX_TYPE_HEADER.equalsIgnoreCase(request.getType())
		&& !GlobalVal.TRX_TYPE_SUMMARY.equalsIgnoreCase(request.getType())){
			throw new CommonException(getMessage(GlobalKey.MSG_TRX_TYPE_INVALID_TYPE, null, audit), ReasonCommon.INVALID_VALUE);
		}

		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		long totalData = daoFactory.getAnomalyDao().countList(request, dashboardGroupH.getIdDashboardGroupH());
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		List<AnomalyBean> anomalies = daoFactory.getAnomalyDao().getList(request, dashboardGroupH.getIdDashboardGroupH(), min, max);
		
		ListAnomalyResponse response = new ListAnomalyResponse();
		response.setAnomalies(anomalies);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}
	
	private List<AddAnomalyBean> validateResultDetailIdsForAnomaly(List<AddAnomalyRequestBean> anomalyRequestBeans, TrDashboardGroupH dashboardGroupH, MsTenant msTenant, AuditContext audit) {
		if (CollectionUtils.isEmpty(anomalyRequestBeans)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Result detail ID(s)"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		List<AddAnomalyBean> details = new ArrayList<>();
		for (AddAnomalyRequestBean anomalyRequestBean : anomalyRequestBeans) {
			
			TrOcrResultDetail detail = ocrResultValidator.getOcrResultDetail(dashboardGroupH, anomalyRequestBean.getResultDetailId(), audit);
			MsLov lovReason = lovValidator.getLovByGroupAndCode(GlobalVal.LOV_GROUP_ANOMALY_REASON, anomalyRequestBean.getLovReasonCode(), audit);
			MsAnomalyReasonRisk risk = riskValidator.getRiskByReasonAndTenant(lovReason, msTenant, audit);
			
			TrConsolidateResultAnomaly anomaly = daoFactory.getAnomalyDao().getAnomaly(dashboardGroupH, detail);
			if (null != anomaly) {
				throw new CommonException(getMessage("businesslogic.anomaly.transactionalreadysaved", new String[] {anomalyRequestBean.getResultDetailId()}, audit), ReasonCommon.DUPLICATE_OBJECT);
			}
			
			details.add(new AddAnomalyBean(detail, lovReason, risk.getLovAnomalyRisk()));
		}
		
		return details;
	}

	@Override
	public MssResponseType addAnomalies(AddAnomaliesRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);

		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		List<AddAnomalyBean> addAnomalyBeans = validateResultDetailIdsForAnomaly(request.getAnomalies(), dashboardGroupH, tenant, audit);
		Date updateTime = new Date();
		
		
		for (AddAnomalyBean addAnomalyBean : addAnomalyBeans) {
			
			TrDashboardGroupD dashboardGroupD = addAnomalyBean.getOcrResultDetail().getTrDashboardGroupD();
			TrOcrResultHeader ocrResultHeader = addAnomalyBean.getOcrResultDetail().getTrOcrResultHeader();
			
			TrConsolidateResultAnomaly anomaly = new TrConsolidateResultAnomaly();
			anomaly.setAnomalyId(UUID.randomUUID().toString());
			anomaly.setTrDashboardGroupH(dashboardGroupH);
			anomaly.setTrDashboardGroupD(dashboardGroupD);
			anomaly.setTrOcrResultHeader(ocrResultHeader);
			anomaly.setTrOcrResultDetail(addAnomalyBean.getOcrResultDetail());
			anomaly.setLovAnomalyReason(addAnomalyBean.getLovReason());
			anomaly.setLovAnomalyRisk(addAnomalyBean.getLovRisk());
			anomaly.setIsUserEdited("1");
			anomaly.setIsDeleted("0");
			anomaly.setUsrCrt(audit.getCallerId());
			anomaly.setDtmCrt(updateTime);
			anomaly.setUsrUpd(audit.getCallerId());
			anomaly.setDtmUpd(updateTime);
			daoFactory.getAnomalyDao().insertAnomaly(anomaly);
		}
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setAnomalyLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public MssResponseType deleteAnomaly(DeleteAnomalyRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);

		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		if (StringUtils.isBlank(request.getAnomalyId())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Anomaly ID"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrConsolidateResultAnomaly anomaly = daoFactory.getAnomalyDao().getAnomaly(dashboardGroupH, request.getAnomalyId());
		if (null == anomaly) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_OBJ_NOT_FOUND, new String[] {"Anomaly ID", request.getAnomalyId()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		Date updateTime = new Date();
		if ("1".equals(anomaly.getIsUserEdited())) {
			daoFactory.getAnomalyDao().deleteAnomaly(anomaly);
		} else {
			anomaly.setIsDeleted("1");
			anomaly.setIsUserEdited("1");
			anomaly.setUsrUpd(audit.getCallerId());
			anomaly.setDtmUpd(updateTime);
			daoFactory.getAnomalyDao().updateAnomaly(anomaly);
		}
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setAnomalyLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public MssResponseType deleteGroupAnomaly(DeleteGroupAnomalyRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);

		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		MsLov lovReason = lovValidator.getLovByGroupAndCode(GlobalVal.LOV_GROUP_ANOMALY_REASON, request.getReason(), audit);
		
		if (StringUtils.isBlank(request.getReason())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Reason"}, audit), ReasonCommon.MANDATORY_PARAM);
		}

		daoFactory.getAnomalyDao().deleteAnomalyByGroup(dashboardGroupH.getIdDashboardGroupH(), lovReason.getIdLov(), "1");
		daoFactory.getAnomalyDao().updateAnomalyByGroupDeleted(dashboardGroupH.getIdDashboardGroupH(), lovReason.getIdLov(), "0", "1", audit);
		
		Date updateTime = new Date();
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setAnomalyLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);

		return new MssResponseType();
	}

	@Override
	public ListGroupAnomalyResponse getListGroupAnomaly(ListGroupAnomalyRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);

		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		if (StringUtils.isNotBlank(request.getReason())){
			MsLov reason = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_ANOMALY_REASON, request.getReason());
			if (null == reason){
				throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {"Reason"}, audit), ReasonCommon.OBJECT_NOT_FOUND);
			}
		}
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		long totalData = daoFactory.getAnomalyDao().countListGroupAnomaly(request, dashboardGroupH.getIdDashboardGroupH());
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		List<AnomalyGroupBean> anomalies = daoFactory.getAnomalyDao().getListAnomalyGroup(request, dashboardGroupH.getIdDashboardGroupH(), min, max);
		
		ListGroupAnomalyResponse response = new ListGroupAnomalyResponse();
		response.setAnomalies(anomalies);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public ListAnomalyMetadataResponse getListAnomalyMetadata(ListAnomalyMetadataRequest request, AuditContext audit) {
		requestValidator.validateAttributes(request, audit);

		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);

		if (StringUtils.isNotBlank(request.getReason())){
			MsLov reason = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_ANOMALY_REASON, request.getReason());
			if (null == reason){
				throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {"Reason"}, audit), ReasonCommon.OBJECT_NOT_FOUND);
			}
		}

		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		long totalData = daoFactory.getAnomalyDao().countListAnomalyMetadata(request, dashboardGroupH.getIdDashboardGroupH());
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		List<AnomalyMetadataBean> anomalies = daoFactory.getAnomalyDao().getListAnomalyMetadata(request, dashboardGroupH.getIdDashboardGroupH(), min, max);
		
		ListAnomalyMetadataResponse response = new ListAnomalyMetadataResponse();
		response.setAnomalies(anomalies);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

}
