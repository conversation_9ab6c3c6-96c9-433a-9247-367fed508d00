package com.adins.bsa.webservices.model.supplierbuyergroup;

import java.util.List;

import com.adins.bsa.custom.SupplierBuyerSubGroupMemberBean;
import com.adins.framework.service.base.model.MssResponseType;

public class ListSupplierBuyerSubGroupMemberResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<SupplierBuyerSubGroupMemberBean> members;
	private int page;
	private long totalPage;
	private long totalResult;
	
	public List<SupplierBuyerSubGroupMemberBean> getMembers() {
		return members;
	}
	public void setMembers(List<SupplierBuyerSubGroupMemberBean> members) {
		this.members = members;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public long getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}
	public long getTotalResult() {
		return totalResult;
	}
	public void setTotalResult(long totalResult) {
		this.totalResult = totalResult;
	}
	
}
