package com.adins.bsa.webservices.model.insights;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class EditDashboardNameRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("New dashboard name")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 64)
	private String newDashboardName;

	public String getNewDashboardName() {
		return newDashboardName;
	}

	public void setNewDashboardName(String newDashboardName) {
		this.newDashboardName = newDashboardName;
	}
	
}
