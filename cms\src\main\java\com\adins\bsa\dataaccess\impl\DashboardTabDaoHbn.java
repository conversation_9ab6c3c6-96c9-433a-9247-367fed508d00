package com.adins.bsa.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsrole;
import com.adins.bsa.dataaccess.api.DashboardTabDao;
import com.adins.bsa.model.MsDashboardTab;
import com.adins.bsa.model.MsDashboardTabOfRole;

@Component
@Transactional
public class DashboardTabDaoHbn extends BaseDaoHbn implements DashboardTabDao {

    @Override
    public MsDashboardTab getDashboardTab(String dashboardTabCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("dashboardTabCode", StringUtils.upperCase(dashboardTabCode));

        return managerDAO.selectOne(
            "from MsDashboardTab dt "
            + "where dt.dashboardTabCode = :dashboardTabCode ", params);
    }

    @Override
    @Transactional(readOnly = true)
    public MsDashboardTabOfRole getDashboardTabOfRoleReadOnly(AmMsrole role, MsDashboardTab dashboardTab) {
        Map<String, Object> params = new HashMap<>();
        params.put("role", role);
        params.put("dashboardTab", dashboardTab);

        return managerDAO.selectOne(
            "from MsDashboardTabOfRole tor "
            + "where tor.amMsrole = :role "
            + "and tor.msDashboardTab = :dashboardTab ", params);
    }
    
}
