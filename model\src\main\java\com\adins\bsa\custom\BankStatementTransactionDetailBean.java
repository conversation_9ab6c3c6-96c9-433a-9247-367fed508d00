package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

public class BankStatementTransactionDetailBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private BigInteger rowNum; 
	private String date;
	private String dateConfidenceRed;
	private String dateBoxLocation;
	private Integer dateBoxPage;
	private String description;  
	private String descriptionConfidenceRed;  
	private String descriptionBoxLocation; 
	private Integer descriptionBoxPage; 
	private BigDecimal amount;
	private String amountConfidenceRed;
	private String amountBoxLocation;
	private Integer amountBoxPage;
	private BigDecimal endingBalance;
	private String endingBalanceConfidenceRed;
	private String endingBalanceBoxLocation;
	private Integer endingBalanceBoxPage;
	private String type;
	private String typeConfidenceRed; 
	private String typeBoxLocation; 
	private Integer typeBoxPage; 
	private String isEdited;
	private String isDeleteOverlap; 
	private String resultDetailId;
	
	public BigInteger getRowNum() {
		return rowNum;
	}
	public void setRowNum(BigInteger rowNum) {
		this.rowNum = rowNum;
	}
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public String getDateConfidenceRed() {
		return dateConfidenceRed;
	}
	public void setDateConfidenceRed(String dateConfidenceRed) {
		this.dateConfidenceRed = dateConfidenceRed;
	}
	public String getDateBoxLocation() {
		return dateBoxLocation;
	}
	public void setDateBoxLocation(String dateBoxLocation) {
		this.dateBoxLocation = dateBoxLocation;
	}
	public Integer getDateBoxPage() {
		return dateBoxPage;
	}
	public void setDateBoxPage(Integer dateBoxPage) {
		this.dateBoxPage = dateBoxPage;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getDescriptionConfidenceRed() {
		return descriptionConfidenceRed;
	}
	public void setDescriptionConfidenceRed(String descriptionConfidenceRed) {
		this.descriptionConfidenceRed = descriptionConfidenceRed;
	}
	public String getDescriptionBoxLocation() {
		return descriptionBoxLocation;
	}
	public void setDescriptionBoxLocation(String descriptionBoxLocation) {
		this.descriptionBoxLocation = descriptionBoxLocation;
	}
	public Integer getDescriptionBoxPage() {
		return descriptionBoxPage;
	}
	public void setDescriptionBoxPage(Integer descriptionBoxPage) {
		this.descriptionBoxPage = descriptionBoxPage;
	}
	public BigDecimal getAmount() {
		return amount;
	}
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public String getAmountConfidenceRed() {
		return amountConfidenceRed;
	}
	public void setAmountConfidenceRed(String amountConfidenceRed) {
		this.amountConfidenceRed = amountConfidenceRed;
	}
	public String getAmountBoxLocation() {
		return amountBoxLocation;
	}
	public void setAmountBoxLocation(String amountBoxLocation) {
		this.amountBoxLocation = amountBoxLocation;
	}
	public Integer getAmountBoxPage() {
		return amountBoxPage;
	}
	public void setAmountBoxPage(Integer amountBoxPage) {
		this.amountBoxPage = amountBoxPage;
	}
	public BigDecimal getEndingBalance() {
		return endingBalance;
	}
	public void setEndingBalance(BigDecimal endingBalance) {
		this.endingBalance = endingBalance;
	}
	public String getEndingBalanceConfidenceRed() {
		return endingBalanceConfidenceRed;
	}
	public void setEndingBalanceConfidenceRed(String endingBalanceConfidenceRed) {
		this.endingBalanceConfidenceRed = endingBalanceConfidenceRed;
	}
	public String getEndingBalanceBoxLocation() {
		return endingBalanceBoxLocation;
	}
	public void setEndingBalanceBoxLocation(String endingBalanceBoxLocation) {
		this.endingBalanceBoxLocation = endingBalanceBoxLocation;
	}
	public Integer getEndingBalanceBoxPage() {
		return endingBalanceBoxPage;
	}
	public void setEndingBalanceBoxPage(Integer endingBalanceBoxPage) {
		this.endingBalanceBoxPage = endingBalanceBoxPage;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getTypeConfidenceRed() {
		return typeConfidenceRed;
	}
	public void setTypeConfidenceRed(String typeConfidenceRed) {
		this.typeConfidenceRed = typeConfidenceRed;
	}
	public String getTypeBoxLocation() {
		return typeBoxLocation;
	}
	public void setTypeBoxLocation(String typeBoxLocation) {
		this.typeBoxLocation = typeBoxLocation;
	}
	public Integer getTypeBoxPage() {
		return typeBoxPage;
	}
	public void setTypeBoxPage(Integer typeBoxPage) {
		this.typeBoxPage = typeBoxPage;
	}
	public String getIsEdited() {
		return isEdited;
	}
	public void setIsEdited(String isEdited) {
		this.isEdited = isEdited;
	}
	public String getIsDeleteOverlap() {
		return isDeleteOverlap;
	}
	public void setIsDeleteOverlap(String isDeleteOverlap) {
		this.isDeleteOverlap = isDeleteOverlap;
	}
	public String getResultDetailId() {
		return resultDetailId;
	}
	public void setResultDetailId(String resultDetailId) {
		this.resultDetailId = resultDetailId;
	} 
	 
}
