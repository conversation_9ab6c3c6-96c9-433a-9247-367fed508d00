package com.adins.bsa.webservices.model.anomaly;

import java.util.List;

import com.adins.bsa.custom.AnomalyGroupBean;
import com.adins.framework.service.base.model.MssResponseType;

public class ListGroupAnomalyResponse extends MssResponseType{
	private static final long serialVersionUID = 1L;
    
    private List<AnomalyGroupBean> anomalies;
	private int page;
	private long totalPage;
	private long totalResult;

    public List<AnomalyGroupBean> getAnomalies() {
        return anomalies;
    }
    public void setAnomalies(List<AnomalyGroupBean> anomalies) {
        this.anomalies = anomalies;
    }
    public int getPage() {
        return page;
    }
    public void setPage(int page) {
        this.page = page;
    }
    public long getTotalPage() {
        return totalPage;
    }
    public void setTotalPage(long totalPage) {
        this.totalPage = totalPage;
    }
    public long getTotalResult() {
        return totalResult;
    }
    public void setTotalResult(long totalResult) {
        this.totalResult = totalResult;
    }
}
