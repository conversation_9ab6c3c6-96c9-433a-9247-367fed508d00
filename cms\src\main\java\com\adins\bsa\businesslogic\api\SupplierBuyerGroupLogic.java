package com.adins.bsa.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.bsa.webservices.model.dashboard.ListSubGroupSupplierBuyerGroupRequest;
import com.adins.bsa.webservices.model.dashboard.ListSubGroupSupplierBuyerGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerSubGroupMembersRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.DeleteSupplierBuyerMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.DeleteSupplierBuyerSubGroupMemberRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.EditSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListMasterSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListMasterSupplierBuyerSubGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupMemberRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupMemberResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupOfMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupOfMainGroupResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface SupplierBuyerGroupLogic {
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	ListSupplierBuyerGroupResponse getList(ListSupplierBuyerGroupRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	ListSubGroupSupplierBuyerGroupResponse getListSubGroup(ListSubGroupSupplierBuyerGroupRequest request, AuditContext audit); 
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType addMainGroup(AddSupplierBuyerMainGroupRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType deleteMainGroup(DeleteSupplierBuyerMainGroupRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType addSubGroup(AddSupplierBuyerSubGroupRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	ListSupplierBuyerSubGroupOfMainGroupResponse getListSubGroupOfMainGroup(ListSupplierBuyerSubGroupOfMainGroupRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	ListMasterSupplierBuyerSubGroupResponse getListMasterSubGroup(ListMasterSupplierBuyerSubGroupRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType deleteSupplierBuyerSubGroup(AddSupplierBuyerSubGroupRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	ListSupplierBuyerSubGroupMemberResponse getListSubGroupMember(ListSupplierBuyerSubGroupMemberRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType deleteSubGroupMember(DeleteSupplierBuyerSubGroupMemberRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType addSupplierBuyerSubGroupMembers(AddSupplierBuyerSubGroupMembersRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType editSupplierBuyerSubGroup(EditSupplierBuyerSubGroupRequest request, AuditContext audit);
}
