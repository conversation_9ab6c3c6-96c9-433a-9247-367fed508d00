package com.adins.bsa.webservices.model.businesstransactiongroup;

import java.util.List;

import com.adins.bsa.custom.NonBusinessTransactionGroupDetailBean;
import com.adins.framework.service.base.model.MssResponseType;

public class ListBusinessTransactionGroupDetailResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<NonBusinessTransactionGroupDetailBean> transactions;
	private int page;
	private long totalPage;
	private long totalResult;
	
	public List<NonBusinessTransactionGroupDetailBean> getTransactions() {
		return transactions;
	}
	public void setTransactions(List<NonBusinessTransactionGroupDetailBean> transactions) {
		this.transactions = transactions;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public long getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}
	public long getTotalResult() {
		return totalResult;
	}
	public void setTotalResult(long totalResult) {
		this.totalResult = totalResult;
	}
	
}
