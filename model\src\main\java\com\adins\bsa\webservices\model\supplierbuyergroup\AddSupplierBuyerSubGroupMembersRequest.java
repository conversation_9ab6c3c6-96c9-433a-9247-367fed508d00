package com.adins.bsa.webservices.model.supplierbuyergroup;

import java.util.List;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class AddSupplierBuyerSubGroupMembersRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	private String subGroupName;
	private List<String> resultDetailIds;
	
	public String getSubGroupName() {
		return subGroupName;
	}
	public void setSubGroupName(String subGroupName) {
		this.subGroupName = subGroupName;
	}
	public List<String> getResultDetailIds() {
		return resultDetailIds;
	}
	public void setResultDetailIds(List<String> resultDetailIds) {
		this.resultDetailIds = resultDetailIds;
	}
	
}
