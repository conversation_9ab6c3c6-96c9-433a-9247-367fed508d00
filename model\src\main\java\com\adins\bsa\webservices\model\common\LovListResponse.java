package com.adins.bsa.webservices.model.common;

import java.io.Serializable;
import java.util.List;

import com.adins.bsa.custom.LovBean;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.ApiModel;

@ApiModel(description = "Objek LOV List Response")
public class LovListResponse extends MssResponseType implements Serializable {
	private static final long serialVersionUID = 1L;
	private List<LovBean> lovList;
	
	public List<LovBean> getLovList() {
		return lovList;
	}

	public void setLovList(List<LovBean> lovList) {
		this.lovList = lovList;
	}

}
