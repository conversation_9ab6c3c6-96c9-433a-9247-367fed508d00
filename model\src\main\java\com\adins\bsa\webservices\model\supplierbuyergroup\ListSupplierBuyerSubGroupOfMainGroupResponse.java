package com.adins.bsa.webservices.model.supplierbuyergroup;

import java.util.List;

import com.adins.bsa.custom.SupplierBuyerSubGroupOfMainGroupBean;
import com.adins.framework.service.base.model.MssResponseType;

public class ListSupplierBuyerSubGroupOfMainGroupResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<SupplierBuyerSubGroupOfMainGroupBean> subGroups;
	
	public List<SupplierBuyerSubGroupOfMainGroupBean> getSubGroups() {
		return subGroups;
	}
	public void setSubGroups(List<SupplierBuyerSubGroupOfMainGroupBean> subGroups) {
		this.subGroups = subGroups;
	}
	
}
