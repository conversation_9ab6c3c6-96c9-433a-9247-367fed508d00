package com.adins.bsa.businesslogic.impl.provider;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import com.adins.am.model.AmMsuser;

public class BsaAuthenticationToken extends UsernamePasswordAuthenticationToken {
	private static final long serialVersionUID = 1L;
	
	private AmMsuser profile;
	private  Map<String, List<String>> mapTenantRole;
	
	public AmMsuser getProfile() {
		return profile;
	}

	public  Map<String, List<String>> getMapTenantRole() {
        return mapTenantRole;
    }

    public BsaAuthenticationToken(Object principal, Object credentials, AmMsuser profile) {
		super(principal, credentials);
		this.profile = profile;
	}

	public BsaAuthenticationToken(Object principal, Object credentials,
			Collection<? extends GrantedAuthority> authorities, AmMsuser profile, Map<String, List<String>> mapTenantRole) {
		super(principal, credentials, authorities);
		this.profile = profile;
		this.mapTenantRole = mapTenantRole;
	}
	
}
