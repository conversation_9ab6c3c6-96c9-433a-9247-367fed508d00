package com.adins.bsa.webservices.model.ocrresult;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;

public class GetListBankStatementTransactionDetailRequest extends GenericDashboardPagingRequest {
	private static final long serialVersionUID = 1L;
	private String fileSourcePath;
	private String type; 
	private String showStatus;
	
	public String getFileSourcePath() {
		return fileSourcePath;
	}
	public void setFileSourcePath(String fileSourcePath) {
		this.fileSourcePath = fileSourcePath;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getShowStatus() {
		return showStatus;
	}
	public void setShowStatus(String showStatus) {
		this.showStatus = showStatus;
	} 
	
	
}

