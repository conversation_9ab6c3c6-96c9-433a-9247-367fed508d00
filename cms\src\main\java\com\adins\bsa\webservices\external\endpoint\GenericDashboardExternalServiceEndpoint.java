package com.adins.bsa.webservices.external.endpoint;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.external.api.DashboardExternalLogic;
import com.adins.bsa.constants.HttpHeaders;
import com.adins.bsa.webservices.external.api.DashboardExternalService;
import com.adins.bsa.webservices.model.external.dashboard.UpdateBankStatementProcessStatusExternalRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/external/dashboard")
@Api(value = "DashboardExternalService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericDashboardExternalServiceEndpoint implements DashboardExternalService {
	
	@Autowired private DashboardExternalLogic dashboardExternalLogic;

	@Override
	@POST
	@Path("/updateBankStatementProcessStatus")
	public MssResponseType updateBankStatementProcessStatus(UpdateBankStatementProcessStatusExternalRequest request) {
		
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		
		AuditContext audit = request.getAudit().toAuditContext();
		return dashboardExternalLogic.updateBankStatementProcessStatus(request, xApiKey, audit);
	}

}
