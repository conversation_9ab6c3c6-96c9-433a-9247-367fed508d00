package com.adins.bsa.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;

@Entity
@Table(name = "tr_dashboard_group_d_temp")
public class TrDashboardGroupDTemp extends ActiveAndUpdateableEntity{
 
    private long idDashboardGroupDTemp;
	private TrDashboardGroupH trDashboardGroupH;
	private AmMsuser amMsuserCreator;
	private MsLov lovFileType;
	private String fileName;
	private String fileSourcePath;
	private Integer totalPages;
    private String isHitl;

    @Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_dashboard_group_d_temp", unique = true, nullable = false)
    public long getIdDashboardGroupDTemp() {
        return idDashboardGroupDTemp;
    }

    public void setIdDashboardGroupDTemp(long idDashboardGroupDTemp) {
        this.idDashboardGroupDTemp = idDashboardGroupDTemp;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_h", nullable = false)
    public TrDashboardGroupH getTrDashboardGroupH() {
        return trDashboardGroupH;
    }

    public void setTrDashboardGroupH(TrDashboardGroupH trDashboardGroupH) {
        this.trDashboardGroupH = trDashboardGroupH;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user_creator", nullable = false)
    public AmMsuser getAmMsuserCreator() {
        return amMsuserCreator;
    }

    public void setAmMsuserCreator(AmMsuser amMsuserCreator) {
        this.amMsuserCreator = amMsuserCreator;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_file_type", nullable = false)
    public MsLov getLovFileType() {
        return lovFileType;
    }

    public void setLovFileType(MsLov loveFileType) {
        this.lovFileType = loveFileType;
    }
    
	@Column(name = "file_name", length = 256)
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Column(name = "file_source_path", length = 256)
    public String getFileSourcePath() {
        return fileSourcePath;
    }

    public void setFileSourcePath(String fileSourcePath) {
        this.fileSourcePath = fileSourcePath;
    }

    @Column(name = "total_pages")
    public Integer getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }

    @Column(name = "is_hitl", length = 1)
    public String getIsHitl() {
        return isHitl;
    }

    public void setIsHitl(String isHitl) {
        this.isHitl = isHitl;
    }
    
}
