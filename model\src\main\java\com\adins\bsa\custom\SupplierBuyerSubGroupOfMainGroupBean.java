package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigInteger;

public class SupplierBuyerSubGroupOfMainGroupBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private BigInteger rowNum;
	private String subGroupName;
	private String mainGroupName;
	private String createDate;
	private String lastUpdated;
	
	public BigInteger getRowNum() {
		return rowNum;
	}
	public void setRowNum(BigInteger rowNum) {
		this.rowNum = rowNum;
	}
	public String getSubGroupName() {
		return subGroupName;
	}
	public void setSubGroupName(String subGroupName) {
		this.subGroupName = subGroupName;
	}
	public String getMainGroupName() {
		return mainGroupName;
	}
	public void setMainGroupName(String mainGroupName) {
		this.mainGroupName = mainGroupName;
	}
	public String getCreateDate() {
		return createDate;
	}
	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}
	public String getLastUpdated() {
		return lastUpdated;
	}
	public void setLastUpdated(String lastUpdated) {
		this.lastUpdated = lastUpdated;
	}
	
}
