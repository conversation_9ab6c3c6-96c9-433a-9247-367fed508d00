package com.adins.bsa.webservices.model.ocrresult;

import java.io.Serializable;
import com.adins.framework.service.base.model.MssRequestType;

public class GetBankStatementTransactionSummaryCalculationRequest extends MssRequestType implements Serializable {
	private static final long serialVersionUID = 1L;
	private String tenantCode;
	private String dashboardName;
	private String fileSourcePath;
	private String period;
	private String openingBalance; 
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getDashboardName() {
		return dashboardName;
	}
	public void setDashboardName(String dashboardName) {
		this.dashboardName = dashboardName;
	} 
	public String getPeriod() {
		return period;
	}
	public void setPeriod(String period) {
		this.period = period;
	}
	public String getOpeningBalance() {
		return openingBalance;
	}
	public void setOpeningBalance(String openingBalance) {
		this.openingBalance = openingBalance;
	}
	public String getFileSourcePath() {
		return fileSourcePath;
	}
	public void setFileSourcePath(String fileSourcePath) {
		this.fileSourcePath = fileSourcePath;
	}
	
	
	
}
