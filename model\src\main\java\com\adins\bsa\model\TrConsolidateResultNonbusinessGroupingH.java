package com.adins.bsa.model;

import java.io.Serializable; 
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "tr_consolidate_result_nonbusiness_grouping_h")
public class TrConsolidateResultNonbusinessGroupingH extends UpdateableEntity implements Serializable{
	
	public static final String PARAM_ID_GROUPING_H = "idGroupingH";
	
	private static final long serialVersionUID = 1L;
	private long idConsolidateResultNonbusinessGroupingH;
	private TrDashboardGroupH trDashboardGroupH; 
	private String groupName;
	private String groupNameEdited;
	private String isUserEdited;
	private String isDeleted;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_consolidate_result_nonbusiness_grouping_h", unique = true, nullable = false)
	public long getIdConsolidateResultNonbusinessGroupingH() {
		return idConsolidateResultNonbusinessGroupingH;
	}
	
	public void setIdConsolidateResultNonbusinessGroupingH(long idConsolidateResultNonbusinessGroupingH) {
		this.idConsolidateResultNonbusinessGroupingH = idConsolidateResultNonbusinessGroupingH;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_h", nullable = false)
	public TrDashboardGroupH getTrDashboardGroupH() {
		return trDashboardGroupH;
	}
	
	public void setTrDashboardGroupH(TrDashboardGroupH trDashboardGroupH) {
		this.trDashboardGroupH = trDashboardGroupH;
	}
	
	@Column(name = "group_name", length = 64)
	public String getGroupName() {
		return this.groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	
	@Column(name = "group_name_edited", length = 64)
	public String getGroupNameEdited() {
		return this.groupNameEdited;
	}

	public void setGroupNameEdited(String groupNameEdited) {
		this.groupNameEdited = groupNameEdited;
	}
	
	@Column(name = "is_user_edited", length = 1)
	public String getIsUserEdited() {
		return this.isUserEdited;
	}

	public void setIsUserEdited(String isUserEdited) {
		this.isUserEdited = isUserEdited;
	}
	
	@Column(name = "is_deleted", length = 1)
	public String getIsDeleted() {
		return this.isDeleted;
	}

	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}

}
