package com.adins.bsa.businesslogic.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.businesslogic.api.CloudStorageLogic;
import com.adins.bsa.businesslogic.api.DashboardLogic;
import com.adins.bsa.businesslogic.api.DataAnalyticsLogic;
import com.adins.bsa.businesslogic.api.FunctionComputeLogic;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.AddDashboardFileBean;
import com.adins.bsa.custom.DashboardAccountNoBean;
import com.adins.bsa.custom.DashboardBankBean;
import com.adins.bsa.custom.DashboardBean;
import com.adins.bsa.custom.DashboardFileBean;
import com.adins.bsa.custom.DashboardPeriodBean;
import com.adins.bsa.custom.NonBusinessTransactionGroupTransactionBean;
import com.adins.bsa.custom.queryfilter.ListDashboardFilter;
import com.adins.bsa.custom.queryfilter.ListTransactionFilter;
import com.adins.bsa.custom.validation.DashboardNameValidationBean;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupDTemp;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;
import com.adins.bsa.util.MssTool;
import com.adins.bsa.util.PdfUtils;
import com.adins.bsa.validator.api.DashboardValidator;
import com.adins.bsa.validator.api.LovValidator;
import com.adins.bsa.validator.api.ObjectRequestValidator;
import com.adins.bsa.validator.api.ObjectValidator;
import com.adins.bsa.validator.api.OcrResultValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.bsa.webservices.model.dashboard.AddDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardAccountNoListResponse;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardBankListResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardConsolidateStatusResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardPeriodListRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardPeriodListResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardWarningStatusResponse;
import com.adins.bsa.webservices.model.dashboard.DeleteDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.ListDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.ListDashboardResponse;
import com.adins.bsa.webservices.model.dataanalytics.DataAnalyticsConsolidateResponse;
import com.adins.bsa.webservices.model.eendigo.OcrBsaRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListTransactionnonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListTransactionnonBusinessTransactionGroupResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

@Component
public class GenericDashboardLogic extends BaseLogic implements DashboardLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericDashboardLogic.class);
	
	@Autowired private DashboardValidator dashboardValidator;
	@Autowired private TenantValidator tenantValidator;
	@Autowired private UserValidator userValidator;
	@Autowired private ObjectValidator objectValidator;
	@Autowired private OcrResultValidator ocrResultValidator;
	@Autowired private LovValidator lovValidator;
	@Autowired private ObjectRequestValidator requestValidator;
	
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private DataAnalyticsLogic dataAnalyticsLogic;
	@Autowired private FunctionComputeLogic functionComputeLogic;

	// To prevent processing concurrent duplicate add dashboard request
	private final Set<DashboardNameValidationBean> dashboardNameSet = ConcurrentHashMap.newKeySet();
	private final Set<DashboardNameValidationBean> dashboardNameForNewStatementSet = ConcurrentHashMap.newKeySet();

	@Override
	public ListDashboardResponse getListDashboard(ListDashboardRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		// Allow empty upload date end when upload date start is filled
		// Forbid empty upload date start when upload date is filled
		if (StringUtils.isNotBlank(request.getUploadDateEnd()) && StringUtils.isBlank(request.getUploadDateStart())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Upload date start"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		Date uploadDateStart = null;
		if (StringUtils.isNotBlank(request.getUploadDateStart())) {
			objectValidator.validateDateFormat(request.getUploadDateStart(), GlobalVal.DATE_FORMAT, "uploadDateStart", audit);
			uploadDateStart = MssTool.formatStringToDate(request.getUploadDateStart() + GlobalVal.SOD_TIME_MILL_SEC, GlobalVal.DATE_TIME_FORMAT_MIL_SEC);
		}
		
		Date uploadDateEnd = null;
		if (StringUtils.isNotBlank(request.getUploadDateEnd())) {
			objectValidator.validateDateFormat(request.getUploadDateEnd(), GlobalVal.DATE_FORMAT, "uploadDateEnd", audit);
			uploadDateEnd = MssTool.formatStringToDate(request.getUploadDateEnd() + GlobalVal.EOD_TIME_MILL_SEC, GlobalVal.DATE_TIME_FORMAT_MIL_SEC);
		}
		
		ListDashboardFilter filter = new ListDashboardFilter();
		filter.setMsTenant(tenant);
		filter.setRequestingUser(requestingUser);
		filter.setDashboardName(request.getDashboardName());
		filter.setFilterCurrentUserOnly("1".equals(request.getFilterCurrentUser()));
		filter.setUploadDateStart(uploadDateStart);
		filter.setUploadDateEnd(uploadDateEnd);
		filter.setMin(min);
		filter.setMax(max);
		
		List<DashboardBean> dashboards = daoFactory.getDashboardGroupDao().getListDashboard(filter);
		long totalData = daoFactory.getDashboardGroupDao().countListDashboard(filter);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		ListDashboardResponse response = new ListDashboardResponse();
		response.setDashboards(dashboards);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public MssResponseType deleteDashboard(DeleteDashboardRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		if (!"1".equals(dashboardGroupH.getIsActive())) {
			throw new CommonException(getMessage("businesslogic.global.objectnotfound1",
					new String[] {request.getDashboardName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		MsLov lovProcessStatus = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_STATUS, GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING);
		long pendingBankStatementCount = daoFactory.getDashboardGroupDao().countDashboardGroupD(dashboardGroupH, lovProcessStatus);
		if (pendingBankStatementCount > 0) {
			throw new CommonException(getMessage(GlobalKey.MSG_OCR_RESULT_STILL_PROCESSED, null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		daoFactory.getCommonDao().executeDeleteDashboard(dashboardGroupH, audit);
		
		return new MssResponseType();
	}
	
	private List<AddDashboardFileBean> validateAddDashboardDuplicateFilename(AddDashboardRequest request, AuditContext audit) {
		
		List<AddDashboardFileBean> addDashboardFileBeans = new ArrayList<>();
		
		Set<String> filenameToValidate = new HashSet<>();
		for (DashboardFileBean file : request.getFiles()) {
			if (!filenameToValidate.add(file.getActualFilename())) {
				throw new CommonException(getMessage("businesslogic.global.duplicateobject", new String[] {file.getActualFilename()}, audit), ReasonCommon.DUPLICATE_OBJECT);
			}
			
			byte[] originalFile = cloudStorageLogic.getTemporaryDashboardFile(file.getOssFilename());
			byte[] unlockedFile = null;
			String ext = this.getFileExtension(file.getOssFilename());
			if (StringUtils.equalsIgnoreCase(ext, "PDF")){
				try {
					unlockedFile = PdfUtils.removePassword(originalFile, file.getPassword());
				} catch (Exception e) {
					throw new CommonException(e.getLocalizedMessage(), ReasonCommon.INVALID_VALUE, e);
				}
			}
			
			AddDashboardFileBean bean = new AddDashboardFileBean();
			bean.setActualFilename(file.getActualFilename());
			bean.setOssFilename(file.getOssFilename());
			bean.setPassword(file.getPassword());
			bean.setDocumentToUpload(unlockedFile);
			addDashboardFileBeans.add(bean);
		}
		
		return addDashboardFileBeans;
	}
	
	private List<AddDashboardFileBean> validateAddBankStatementDuplicateFilename(TrDashboardGroupH dashboardGroupH, AddDashboardRequest request, AuditContext audit) {
		
		List<AddDashboardFileBean> addDashboardFileBeans = new ArrayList<>();
		
		Set<String> filenameToValidate = new HashSet<>();
		for (DashboardFileBean file : request.getFiles()) {
			if (!filenameToValidate.add(file.getActualFilename())) {
				throw new CommonException(getMessage("businesslogic.global.duplicateobject", new String[] {file.getActualFilename()}, audit), ReasonCommon.DUPLICATE_OBJECT);
			}
			
			TrDashboardGroupD groupD = daoFactory.getDashboardGroupDao().getDashboardGroupDByFilename(dashboardGroupH, file.getActualFilename());
			if (null != groupD) {
				throw new CommonException(getMessage("businesslogic.global.objectalreadysaved", new String[] {file.getActualFilename(), dashboardGroupH.getDashboardGroupName()}, audit), ReasonCommon.DUPLICATE_OBJECT);
			}
			
			byte[] originalFile = cloudStorageLogic.getTemporaryDashboardFile(file.getOssFilename());
			byte[] unlockedFile = null;
			String ext = this.getFileExtension(file.getOssFilename());
			if (StringUtils.equalsIgnoreCase(ext, "PDF")){
				try {
					unlockedFile = PdfUtils.removePassword(originalFile, file.getPassword());
				} catch (Exception e) {
					throw new CommonException(e.getLocalizedMessage(), ReasonCommon.INVALID_VALUE, e);
				}
			}
			
			AddDashboardFileBean bean = new AddDashboardFileBean();
			bean.setActualFilename(file.getActualFilename());
			bean.setOssFilename(file.getOssFilename());
			bean.setPassword(file.getPassword());
			bean.setDocumentToUpload(unlockedFile);
			addDashboardFileBeans.add(bean);
		}
		
		return addDashboardFileBeans;
	}

	private void validateAddDashboardConcurrently(DashboardNameValidationBean bean, AuditContext audit) {
		LOG.info("Checking dashboard name: {}, tenant: {}", bean.getDashboardName(), bean.getTenantCode());
		LOG.info("Dashboard name set size: {} (before validation)", dashboardNameSet.size());
		if (!dashboardNameSet.add(bean)) {
			throw new CommonException(getMessage("businesslogic.global.currentlyprocessed", new String[] {bean.getDashboardName()}, audit), ReasonCommon.NAME_ALREADY_USED);
		}
		
	}

	@Override
	public MssResponseType addDashboard(AddDashboardRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = daoFactory.getDashboardGroupDao().getDashboardGroupHNewTrx(request.getDashboardName(), tenant);
		if (null != dashboardGroupH) {
			throw new CommonException(getMessage("businesslogic.global.namealreadyused", new String[] {request.getDashboardName()}, audit), ReasonCommon.NAME_ALREADY_USED);
		}
		
		List<AddDashboardFileBean> addDashboardFileBeans = validateAddDashboardDuplicateFilename(request, audit);

		DashboardNameValidationBean nameValidationBean = new DashboardNameValidationBean();
		nameValidationBean.setDashboardName(request.getDashboardName());
		nameValidationBean.setTenantCode(request.getTenantCode());
		validateAddDashboardConcurrently(nameValidationBean, audit);

		Date currentTime = new Date();
		
		dashboardGroupH = new TrDashboardGroupH();
		dashboardGroupH.setMsTenant(tenant);
		dashboardGroupH.setAmMsuserCreator(requestingUser);
		dashboardGroupH.setDashboardGroupName(request.getDashboardName());
		dashboardGroupH.setTotalFiles(request.getFiles().size());
		dashboardGroupH.setIsConsolidating("1".equals(request.getIsAutoConsolidate()) ? "1" : "0");
		dashboardGroupH.setIsActive("1");
		dashboardGroupH.setUsrCrt(audit.getCallerId());
		dashboardGroupH.setDtmCrt(currentTime);
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(currentTime);
		dashboardGroupH.setIsAutoConsolidate("1".equals(request.getIsAutoConsolidate()) ? "1" : "0");
		daoFactory.getDashboardGroupDao().insertDashboardHNewTrx(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = new TrDashboardGroupLastUpdateDate();
		dashboardLastUpdate.setTrDashboardGroupH(dashboardGroupH);
		dashboardLastUpdate.setUsrCrt(audit.getCallerId());
		dashboardLastUpdate.setDtmCrt(currentTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		dashboardLastUpdate.setDtmUpd(currentTime);
		daoFactory.getDashboardGroupDao().insertDashboardGroupLastUpdateDateNewTrx(dashboardLastUpdate);
		
		List<TrDashboardGroupDTemp> pdfTemps = new ArrayList<>();
		List<TrDashboardGroupDTemp> imgTemps = new ArrayList<>();
		
		for (AddDashboardFileBean fileBean : addDashboardFileBeans) {

			TrDashboardGroupDTemp dashboardGroupDTemp = new TrDashboardGroupDTemp();
			dashboardGroupDTemp.setTrDashboardGroupH(dashboardGroupH);
			dashboardGroupDTemp.setAmMsuserCreator(requestingUser);
			
			String ext = this.getFileExtension(fileBean.getActualFilename());
			dashboardGroupDTemp.setFileName(fileBean.getActualFilename());
			dashboardGroupDTemp.setFileSourcePath(fileBean.getOssFilename());
			dashboardGroupDTemp.setIsActive("1");
			dashboardGroupDTemp.setUsrCrt(audit.getCallerId());
			dashboardGroupDTemp.setDtmCrt(new Date());
			if (StringUtils.equalsIgnoreCase("PDF",ext)){
				MsLov lovFileType = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_DASHBOARD_FILE_TYPE, GlobalVal.CODE_LOV_FILE_TYPE_PDF);
				dashboardGroupDTemp.setLovFileType(lovFileType);
				dashboardGroupDTemp.setTotalPages(0);
				pdfTemps.add(dashboardGroupDTemp);
			}else if (StringUtils.equalsAnyIgnoreCase(ext, "PNG", "JPG", "JPEG")){
				MsLov lovFileType = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_DASHBOARD_FILE_TYPE, GlobalVal.CODE_LOV_FILE_TYPE_IMAGE);
				dashboardGroupDTemp.setLovFileType(lovFileType);
				dashboardGroupDTemp.setTotalPages(1);
				imgTemps.add(dashboardGroupDTemp);
			}
			dashboardGroupDTemp.setIsHitl("1".equals(request.getIsHitl()) ? "1" : "0");
			daoFactory.getDashboardGroupDao().insertDashboardDTempNewTrx(dashboardGroupDTemp);
			
		}
		
		for (TrDashboardGroupDTemp pdfTemp : pdfTemps) {
			processOcrPdfFile(dashboardGroupH, pdfTemp, tenant, requestingUser, audit);
		}
		
		if (!imgTemps.isEmpty()) {
			Integer imageTotalPages = imgTemps.size();
			String imageOssFilename = imgTemps.get(0).getFileSourcePath();
			String imageOssFilePath = String.format(GlobalVal.OSS_DASHBOARD_DIR_FORMAT, imageOssFilename);
			String imageFileType = imgTemps.get(0).getLovFileType().getCode();
			String imageActualFilename = imgTemps.get(0).getFileName();
			String statusMessage = triggerOcrBsa(tenant, imageOssFilename, imageOssFilePath, imageTotalPages, imageFileType);
			if (StringUtils.isNotBlank(statusMessage)) {
				handleTriggerOcrFailure(dashboardGroupH, imageActualFilename, imageOssFilename, statusMessage, requestingUser, audit);
			}
		}
		dashboardNameSet.remove(nameValidationBean);
		LOG.info("Dashboard name set size: {} (after business logic process)", dashboardNameSet.size());
		return new MssResponseType();
	}
	
	/**
	 * @return empty string if trigger is successful. Else, return error message.
	 */
	private String triggerOcrBsa(MsTenant tenant, String ossFilename, String ossFilePath, Integer totalPages, String fileType) {
		
		OcrBsaRequest request = new OcrBsaRequest();
		request.setTenantCode(tenant.getTenantCode());
		request.setKey(tenant.getApiKey());
		request.setOssFile(ossFilename);
		request.setOssPath(ossFilePath);
		request.setTotalPages(totalPages);
		request.setFileType(fileType);
		
		try {
			functionComputeLogic.invokeAsyncOcrBsa(request);
			return StringUtils.EMPTY;
		} catch (Exception e) {
			LOG.error("Tenant {}, trigger OCR BSA error: {}", tenant.getTenantName(), e.getLocalizedMessage(), e);
			return e.getLocalizedMessage();
		}
	}

	@Override
	public DashboardBankListResponse getDashboardBankList(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		List<DashboardBankBean> banks = daoFactory.getDashboardGroupDao().getDashboardBankList(dashboardGroupH);
		
		DashboardBankListResponse response = new DashboardBankListResponse();
		response.setBanks(banks);
		return response;
	}

	@Override
	public DashboardAccountNoListResponse getDashboardAccountNoList(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(user, dashboardGroupH, audit);
		
		List<DashboardAccountNoBean> accountNos = daoFactory.getDashboardGroupDao().getDashboardAccountNoList(dashboardGroupH);
		
		DashboardAccountNoListResponse response = new DashboardAccountNoListResponse();
		response.setAccountNos(accountNos);
		return response;
	}

	@Override
	public DashboardPeriodListResponse getDashboardPeriodList(DashboardPeriodListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		userValidator.validateUserDashboardAccess(user, dashboardGroupH, audit);
		
		if (StringUtils.isBlank(request.getAccountNo())) {
			DashboardPeriodListResponse response = new DashboardPeriodListResponse();
			response.setPeriods(new ArrayList<>());
			return response;
		}
		
		long ocrResultCount = daoFactory.getOcrResultDao().countOcrResultByDashboardHAndAccountNo(dashboardGroupH, request.getAccountNo());
		if (ocrResultCount == 0) {
			throw new CommonException(getMessage("businesslogic.global.objectnotfound2",
					new String[] {request.getAccountNo(), dashboardGroupH.getDashboardGroupName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		List<DashboardPeriodBean> periods = daoFactory.getDashboardGroupDao().getDashboardPeriodList(dashboardGroupH, request.getAccountNo());
		
		DashboardPeriodListResponse response = new DashboardPeriodListResponse();
		response.setPeriods(periods);
		return response;
	}

	private void validateAddNewBankStatementConcurrently(DashboardNameValidationBean bean, AuditContext audit) {
		LOG.info("Checking add new bank statement for dashboard name: {}, tenant: {}", bean.getDashboardName(), bean.getTenantCode());
		LOG.info("Dashboard name for new bank statement set size: {} (before validation)", dashboardNameSet.size());
		if (!dashboardNameForNewStatementSet.add(bean)) {
			throw new CommonException(getMessage("businesslogic.global.currentlyprocessed", new String[] {bean.getDashboardName()}, audit), ReasonCommon.NAME_ALREADY_USED);
		}
		
	}

	@Override
	public MssResponseType addNewBankStatement(AddDashboardRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(user, dashboardGroupH, audit);
		
		List<AddDashboardFileBean> addDashboardFileBeans = validateAddBankStatementDuplicateFilename(dashboardGroupH, request, audit);		
		List<TrDashboardGroupDTemp> pdfTemps = new ArrayList<>();
		List<TrDashboardGroupDTemp> imgTemps = new ArrayList<>();
		Date currentTime = new Date();

		DashboardNameValidationBean nameValidationBean = new DashboardNameValidationBean();
		nameValidationBean.setDashboardName(request.getDashboardName());
		nameValidationBean.setTenantCode(request.getTenantCode());
		validateAddNewBankStatementConcurrently(nameValidationBean, audit);
		
		for (AddDashboardFileBean fileBean : addDashboardFileBeans) {
			
			TrDashboardGroupDTemp dashboardGroupDTemp = new TrDashboardGroupDTemp();
			dashboardGroupDTemp.setTrDashboardGroupH(dashboardGroupH);
			dashboardGroupDTemp.setAmMsuserCreator(user);
			
			String ext = this.getFileExtension(fileBean.getActualFilename());
			dashboardGroupDTemp.setFileName(fileBean.getActualFilename());
			dashboardGroupDTemp.setFileSourcePath(fileBean.getOssFilename());
			dashboardGroupDTemp.setIsActive("1");
			dashboardGroupDTemp.setUsrCrt(audit.getCallerId());
			dashboardGroupDTemp.setDtmCrt(new Date());
			if (StringUtils.equalsIgnoreCase("PDF",ext)){
				MsLov lovFileType = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_DASHBOARD_FILE_TYPE, GlobalVal.CODE_LOV_FILE_TYPE_PDF);
				dashboardGroupDTemp.setLovFileType(lovFileType);
				dashboardGroupDTemp.setTotalPages(0);
				pdfTemps.add(dashboardGroupDTemp);
			}else if (StringUtils.equalsAnyIgnoreCase(ext, "PNG", "JPG", "JPEG")){
				MsLov lovFileType = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_DASHBOARD_FILE_TYPE, GlobalVal.CODE_LOV_FILE_TYPE_IMAGE);
				dashboardGroupDTemp.setLovFileType(lovFileType);
				dashboardGroupDTemp.setTotalPages(1);
				imgTemps.add(dashboardGroupDTemp);
			}
			dashboardGroupDTemp.setIsHitl("1".equals(request.getIsHitl()) ? "1" : "0");
			daoFactory.getDashboardGroupDao().insertDashboardDTempNewTrx(dashboardGroupDTemp);
			
		}
		
		for (TrDashboardGroupDTemp pdfTemp : pdfTemps) {
			processOcrPdfFile(dashboardGroupH, pdfTemp, tenant, user, audit);
		}
		
		if (!imgTemps.isEmpty()) {
			Integer imageTotalPages = imgTemps.size();
			String imageOssFilename = imgTemps.get(0).getFileSourcePath();
			String imageOssFilePath = String.format(GlobalVal.OSS_DASHBOARD_DIR_FORMAT, imageOssFilename);
			String imageFileType = imgTemps.get(0).getLovFileType().getCode();
			String imageActualFilename = imgTemps.get(0).getFileName();
			String statusMessage = triggerOcrBsa(tenant, imageOssFilename, imageOssFilePath, imageTotalPages, imageFileType);
			if (StringUtils.isNotBlank(statusMessage)) {
				handleTriggerOcrFailure(dashboardGroupH, imageActualFilename, imageOssFilename, statusMessage, user, audit);
			}
		}
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(currentTime);
		dashboardGroupH.setIsAutoConsolidate("1".equals(request.getIsAutoConsolidate()) ? "1" : "0");
		dashboardGroupH.setIsConsolidating("1".equals(request.getIsAutoConsolidate()) ? "1" : "0");
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdateNewTrx(dashboardGroupH);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		dashboardLastUpdate.setDtmUpd(currentTime);
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDateNewTrx(dashboardLastUpdate);
		
		dashboardNameForNewStatementSet.remove(nameValidationBean);
		LOG.info("Dashboard name for new bank statement set size: {} (after business logic process)", dashboardNameForNewStatementSet.size());

		return new MssResponseType();
	}
	
	private void validateGetListTransactionnonBusinessTransactionGroupRequest(ListTransactionnonBusinessTransactionGroupRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		if (StringUtils.isBlank(request.getCategory()) && StringUtils.isBlank(request.getPeriod())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Category/Periode"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (StringUtils.isNotBlank(request.getCategory())) {
			lovValidator.getLovByGroupAndCode(GlobalVal.LOV_GROUP_CATEGORY, request.getCategory(), audit);
		}
		
		if (StringUtils.isNotBlank(request.getPeriod())) {
			objectValidator.validateDateFormatter(request.getPeriod() , GlobalVal.DATE_FORMAT_PERIOD, "Periode", audit);
		}
		
		if (StringUtils.isNotBlank(request.getAmountMoreThan()) && Long.parseLong(request.getAmountMoreThan()) < 0) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_POSITIVE_NUMBER_ONLY, new String[] {"Amount More Than"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (StringUtils.isNotBlank(request.getAmountLessThan()) && Long.parseLong(request.getAmountLessThan()) < 0) {			
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_POSITIVE_NUMBER_ONLY, new String[] {"Amount Less Than"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (StringUtils.isNotBlank(request.getAmountMoreThan()) && StringUtils.isNotBlank(request.getAmountLessThan())) {
			Long lowerLimit = Long.parseLong(request.getAmountMoreThan());
			Long upperLimit = Long.parseLong(request.getAmountLessThan());
			
			if (lowerLimit > upperLimit) {
				throw new CommonException(getMessage("businesslogic.global.cannotbegreater", new String[] {"'Amount >='", "'Amount <='"}, audit), ReasonCommon.INVALID_VALUE);
			}
		}
	}

	
	@Override
	public ListTransactionnonBusinessTransactionGroupResponse getListTransactionnonBusinessTransactionGroup(ListTransactionnonBusinessTransactionGroupRequest request, AuditContext audit) {
		
		List<String> listFormSource = new ArrayList<>();
		listFormSource.add(GlobalVal.FORM_SOURCE_ANOMALY);
		listFormSource.add(GlobalVal.FORM_SOURCE_NONBUSINESS_TRANSACTION);
		listFormSource.add(GlobalVal.FORM_SOURCE_SUPPLIER_BUYER);
		listFormSource.add(GlobalVal.FORM_SOURCE_CIRCULAR);
		listFormSource.add(GlobalVal.FORM_SOURCE_BUSINESS);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		if (!listFormSource.contains(request.getFormSource())) {
			ListTransactionnonBusinessTransactionGroupResponse response = new ListTransactionnonBusinessTransactionGroupResponse();
			response.setListTransactions(Collections.emptyList());
			return response;
		}
		
		ocrResultValidator.validateAccountNoOnDashboard(dashboardGroupH, request.getAccountNo(), audit);
		validateGetListTransactionnonBusinessTransactionGroupRequest(request, audit);
		
		ListTransactionFilter filter = new ListTransactionFilter();
		filter.setDashboardGroupH(dashboardGroupH);
		filter.setAccountNo(request.getAccountNo());
		filter.setCategory(request.getCategory());
		filter.setType(request.getType());
		filter.setAmountMoreThan(request.getAmountMoreThan());
		filter.setAmountLessThan(request.getAmountLessThan());
		filter.setPeriod(request.getPeriod());
		filter.setDescription(request.getDescription());
		List<NonBusinessTransactionGroupTransactionBean> listItem =  daoFactory.getOcrResultDao().getListTransactionNonBusinessGroup(filter, request.getFormSource());
		
		ListTransactionnonBusinessTransactionGroupResponse response = new ListTransactionnonBusinessTransactionGroupResponse();
		response.setListTransactions(listItem);
		return response;
	}

	@Override
	public MssResponseType startConsolidate(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(user, dashboardGroupH, audit);
		
		if ("1".equals(dashboardGroupH.getIsConsolidating())) {
			throw new CommonException(getMessage("businesslogic.dashboard.currentlyconsolidating", new String[] {dashboardGroupH.getDashboardGroupName()}, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		String message = triggerConsolidate(dashboardGroupH, audit);
		if (StringUtils.isNotBlank(message)) {
			throw new CommonException(message, ReasonCommon.INVALID_CONDITION);
		}
		
		dashboardGroupH.setIsConsolidating("1");
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(new Date());
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		return new MssResponseType();
	}
	
	/**
	 * @return empty string if trigger is successful. Else, return an error message.
	 */
	private String triggerConsolidate(TrDashboardGroupH dashboardGroupH, AuditContext audit) {
		try {
			DataAnalyticsConsolidateResponse response = dataAnalyticsLogic.consolidateDashboard(dashboardGroupH, audit);
			return "SUCCESS".equals(response.getStatus()) ? StringUtils.EMPTY : response.getMessage();
		} catch (Exception e) {
			LOG.error("Tenant {}, Consolidate failed with error: {}", dashboardGroupH.getMsTenant().getTenantName(), e.getLocalizedMessage(), e);
			return e.getLocalizedMessage();
		}
		
	}

	@Override
	public DashboardConsolidateStatusResponse getConsolidateStatus(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = daoFactory.getDashboardGroupDao().getDashboardGroupHNewTrx(request.getDashboardName(), tenant);
		userValidator.validateUserDashboardAccess(user, dashboardGroupH, audit);
		
		DashboardConsolidateStatusResponse response = new DashboardConsolidateStatusResponse();
		response.setConsolidateStatus(getConsolidateStatus(dashboardGroupH));
		return response;
	}
	
	private String getConsolidateStatus(TrDashboardGroupH dashboardGroupH) {
		
		MsLov lovProcessStatus = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_STATUS, GlobalVal.CODE_LOV_PROCESS_TYPE_COMPLETE);
		long consolidatedBankStatement = daoFactory.getDashboardGroupDao().countDashboardGroupD(dashboardGroupH, lovProcessStatus);
		
		if (dashboardGroupH.getConsolidateDate() == null) {	
			return consolidatedBankStatement > 0 ? GlobalVal.CONSO_STATUS_CAN_CONSOLIDATE : GlobalVal.CONSO_STATUS_CANNOT_CONSOLIDATE;
		}
		
		return consolidatedBankStatement > 0 ? GlobalVal.CONSO_STATUS_CAN_CONSOLIDATE : GlobalVal.CONSO_STATUS_CONSOLIDATE_COMPLETE;
		
	}

	@Override
	public DashboardWarningStatusResponse getDashboardWarningStatus(GenericDashboardDropdownListRequest request, AuditContext audit) {
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		boolean canAccess = userValidator.canUserAccessDashboard(requestingUser, dashboardGroupH, audit);
		if (!canAccess) {
			return new DashboardWarningStatusResponse();
		}
		
		long highRedPercentageCount = daoFactory.getDashboardGroupDao().countDashboardGroupDWithHighRedPercentage(dashboardGroupH);
		long highRiskAnomalyCount = daoFactory.getAnomalyDao().countHighRiskAnomaly(dashboardGroupH);
		long mediumRiskAnomalyCount = daoFactory.getAnomalyDao().countMediumRiskAnomaly(dashboardGroupH);
		BigDecimal greatestCircularRatio = daoFactory.getDashboardGroupDao().getDashboardGreatestCircularRatio(dashboardGroupH);
		BigDecimal circularRatioThreshold = BigDecimal.valueOf(25);
		
		String ocrResultWarningStatus = highRedPercentageCount > 0 ? "1" : "0";
		String circularWarningStatus = greatestCircularRatio.compareTo(circularRatioThreshold) > -1 ? "1" : "0";
		String anomalyWarningStatus = highRiskAnomalyCount != 0 || mediumRiskAnomalyCount != 0 ? "1" : "0";
		String anomalySeverity = null;
		if (highRiskAnomalyCount > 0) {
			anomalySeverity = "HIGH";
		}  else if (mediumRiskAnomalyCount > 0) {
			anomalySeverity = "MEDIUM";
		}
		
		DashboardWarningStatusResponse response = new DashboardWarningStatusResponse();
		response.setOcrResultWarningStatus(ocrResultWarningStatus);
		response.setCircularWarningStatus(circularWarningStatus);
		response.setAnomalyWarningStatus(anomalyWarningStatus);
		response.setAnomalyWarningSeverity(anomalySeverity);
		return response;
	}

	private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toUpperCase();
        } else {
            return "";
        }
    }

	private void processOcrPdfFile(TrDashboardGroupH dashboardGroupH, TrDashboardGroupDTemp groupDTemp, MsTenant tenant, AmMsuser requestingUser, AuditContext audit) {
		String ossFilename = groupDTemp.getFileSourcePath();
		String ossFilePath = String.format(GlobalVal.OSS_DASHBOARD_DIR_FORMAT, ossFilename);
		Integer totalPages = groupDTemp.getTotalPages();
		String fileType = groupDTemp.getLovFileType().getCode();
	
		String statusMessage = triggerOcrBsa(tenant, ossFilename, ossFilePath, totalPages, fileType);
		if (StringUtils.isNotBlank(statusMessage)) {
			handleTriggerOcrFailure(dashboardGroupH, groupDTemp.getFileName(), groupDTemp.getFileSourcePath(), statusMessage, requestingUser, audit);
		}
	}
	
	private void handleTriggerOcrFailure(TrDashboardGroupH dashboardGroupH, String fileName, String fileSourcePath, String statusMessage, AmMsuser requestingUser, AuditContext audit) {
		MsLov failStatusLov = daoFactory.getLovDao()
			.getLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_STATUS, GlobalVal.CODE_LOV_PROCESS_TYPE_FAILED);
	
		TrDashboardGroupD dashboardGroupD = insertToDashboardGroupD(dashboardGroupH, fileName, fileSourcePath, failStatusLov, statusMessage, requestingUser, audit);
		daoFactory.getDashboardGroupDao().insertDashboardDNewTrx(dashboardGroupD);
	}
	
	private TrDashboardGroupD insertToDashboardGroupD(TrDashboardGroupH dashboardGroupH, String fileName, String fileSourcePath, MsLov failStatusLov, String statusMessage, AmMsuser requestingUser, AuditContext audit) {
		TrDashboardGroupD dashboardGroupD = new TrDashboardGroupD();
		dashboardGroupD.setTrDashboardGroupH(dashboardGroupH);
		dashboardGroupD.setAmMsuserCreator(requestingUser);
		dashboardGroupD.setFileName(fileName);
		dashboardGroupD.setFileSourcePath(fileSourcePath);
		dashboardGroupD.setLovProcessStatus(failStatusLov);
		dashboardGroupD.setProcessResultMessage(StringUtils.left(statusMessage, 128));
		dashboardGroupD.setIsInConsolidatedResult("0");
		dashboardGroupD.setIsEditedAfterConsolidated("0");
		dashboardGroupD.setIsActive("1");
		dashboardGroupD.setUsrUpd(audit.getCallerId());
		dashboardGroupD.setDtmUpd(new Date());
		return dashboardGroupD;
	}
}
