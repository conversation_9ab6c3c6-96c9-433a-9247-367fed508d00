package com.adins.bsa.businesslogic.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.businesslogic.api.CloudStorageLogic;
import com.adins.bsa.businesslogic.api.InsightsLogic;
import com.adins.bsa.businesslogic.api.OssLogic;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.GeneralCashFlowDetailBean;
import com.adins.bsa.custom.InsightsChartBean;
import com.adins.bsa.custom.OcrResultBean;
import com.adins.bsa.custom.SupplierBuyerCashFlowDetailBean;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;
import com.adins.bsa.util.MssTool;
import com.adins.bsa.validator.api.DashboardValidator;
import com.adins.bsa.validator.api.ObjectRequestValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;
import com.adins.bsa.webservices.model.insights.ConsolidateDocumentDownloadUrlRequest;
import com.adins.bsa.webservices.model.insights.ConsolidateDocumentDownloadUrlResponse;
import com.adins.bsa.webservices.model.insights.ConsolidatedBankStatementListResponse;
import com.adins.bsa.webservices.model.insights.EditDashboardNameRequest;
import com.adins.bsa.webservices.model.insights.GeneralBodyCardDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralCircularChartDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralDailyAnalysisDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralMonthlyCashFlowAnalysisDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsGeneralCircularHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsGeneralHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsInformationResponse;
import com.adins.bsa.webservices.model.insights.InsightsSupplierBuyerHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerGpmWcrLrResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerMonthlyCashflowAndGrowthRateResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerTopFiveChartResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

@Component
public class GenericInsightsLogic extends BaseLogic implements InsightsLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericInsightsLogic.class);
	
	@Autowired private DashboardValidator dashboardValidator;
	@Autowired private TenantValidator tenantValidator;
	@Autowired private UserValidator userValidator;
	@Autowired private ObjectRequestValidator objectRequestValidator;
	
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private OssLogic ossLogic;
	
	private static final String CONST_NET_CASH = "Net Cash";

	@Override
	public InsightsInformationResponse getBasicInformation(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		String lastUpdate = MssTool.formatDateToStringIn(dashboardGroupH.getDtmUpd(), GlobalVal.DATE_TIME_FORMAT_MIN_IN);
		String lastConsolidate = MssTool.formatDateToStringIn(dashboardGroupH.getConsolidateDate(), GlobalVal.DATE_TIME_FORMAT_MIN_IN);
		boolean isEdited = isDashboardEditedAfterConsolidate(dashboardGroupH);
		
		InsightsInformationResponse response = new InsightsInformationResponse();
		response.setDashboardName(dashboardGroupH.getDashboardGroupName());
		response.setCreatedBy(dashboardGroupH.getAmMsuserCreator().getFullName());
		response.setLastUpdate(StringUtils.isBlank(lastUpdate) ? "-" : lastUpdate);
		response.setLastConsolidate(StringUtils.isBlank(lastConsolidate) ? "-" : lastConsolidate);
		response.setIsDataChangesAfterConsolidate(isEdited ? "1" : "0");
		response.setIsEditable(user.getIdMsUser() == dashboardGroupH.getAmMsuserCreator().getIdMsUser() ? "1" : "0");
		return response;
	}
	
	private boolean isDashboardEditedAfterConsolidate(TrDashboardGroupH dashboardGroupH) {
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		
		if (dashboardLastUpdate.getConsolidateDate() == null) {
			return false;
		}
		
		Date ocrResultLastUpdDate = dashboardLastUpdate.getOcrResultLastUpdDate();
		Date supplierBuyerLastUpdDate = dashboardLastUpdate.getSupplierBuyerLastUpdDate();
		Date nonbusinessLastUpdDate = dashboardLastUpdate.getNonbusinessLastUpdDate();
		Date anomalyDate = dashboardLastUpdate.getAnomalyLastUpdDate();
		Date circularDate = dashboardLastUpdate.getCircularLastUpdDate();
		Date businessLastUpdDate = dashboardLastUpdate.getBusinessLastUpdDate();
		
		List<Date> groupedDate = Arrays.asList(ocrResultLastUpdDate, supplierBuyerLastUpdDate, nonbusinessLastUpdDate, anomalyDate, circularDate, businessLastUpdDate);
		Date latestDate = groupedDate.stream()
				.filter(Objects::nonNull)
				.max(Date::compareTo)
				.orElse(null);
		
		if (latestDate == null) {
			return false;
		}
		
		return latestDate.after(dashboardLastUpdate.getConsolidateDate());
	}

	@Override
	public ConsolidatedBankStatementListResponse getConsolidatedBankStatements(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		if (null == dashboardGroupH.getConsolidateDate()) {
			throw new CommonException(getMessage("businesslogic.dashboard.notyetconsolidated", new String[] {dashboardGroupH.getDashboardGroupName()}, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		List<OcrResultBean> results = daoFactory.getDashboardGroupDao().getConsolidatedBankStatements(dashboardGroupH);
		
		ConsolidatedBankStatementListResponse response = new ConsolidatedBankStatementListResponse();
		response.setBankStatements(results);
		return response;
	}

	@Override
	public MssResponseType editDashboardName(EditDashboardNameRequest request, AuditContext audit) {
		
		objectRequestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		if (StringUtils.isBlank(request.getNewDashboardName())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"New dashboard name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (request.getNewDashboardName().equals(request.getDashboardName())) {
			return new MssResponseType();
		}
		
		TrDashboardGroupH newDashboardGroupH = daoFactory.getDashboardGroupDao().getDashboardGroupH(request.getNewDashboardName(), tenant);
		if (null != newDashboardGroupH) {
			throw new CommonException(getMessage("businesslogic.global.namealreadyused", new String[] {request.getNewDashboardName()}, audit), ReasonCommon.DUPLICATE_OBJECT);
		}
		
		String oldName = dashboardGroupH.getDashboardGroupName();
		String newName = request.getNewDashboardName();
		
		dashboardGroupH.setDashboardGroupName(request.getNewDashboardName());
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(new Date());
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		// Rename pdf
		byte[] pdfDocument = cloudStorageLogic.getConsolidatePdf(tenant, oldName);
		cloudStorageLogic.deleteConsolidatePdf(tenant, oldName);
		cloudStorageLogic.storeConsolidatePdf(tenant, newName, pdfDocument);
		
		// Rename xlsx
		byte[] xlsxDocument = cloudStorageLogic.getConsolidateXlsx(tenant, oldName);
		cloudStorageLogic.deleteConsolidateXlsx(tenant, oldName);
		cloudStorageLogic.storeConsolidateXlsx(tenant, newName, xlsxDocument);
		
		return new MssResponseType();
	}

	@Override
	public ConsolidateDocumentDownloadUrlResponse getConsolidateDocumentDownloadUrl(ConsolidateDocumentDownloadUrlRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		if (!GlobalVal.DOC_TYPE_PDF.equals(request.getDocumentType()) && !GlobalVal.DOC_TYPE_EXCEL.equals(request.getDocumentType())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MUST_BE_FILLED_WITH, new String[] {"Document type", "PDF / EXCEL"}, audit), ReasonCommon.INVALID_VALUE);
		}
		
		String ossPathFormat = GlobalVal.DOC_TYPE_PDF.equals(request.getDocumentType()) ? GlobalVal.OSS_INSIGHTS_PDF_FORMAT : GlobalVal.OSS_INSIGHTS_XLSX_FORMAT;
		String pathName = String.format(ossPathFormat, tenant.getTenantCode(), dashboardGroupH.getDashboardGroupName());
		LOG.info("About to download {}", pathName);
		if (!ossLogic.isFileExist(pathName)) {
			throw new CommonException(getMessage("businesslogic.global.objectnotfound1", new String[] {"File"}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		String downloadUrl = ossLogic.generateDownloadSignedUrl(pathName);
		
		ConsolidateDocumentDownloadUrlResponse response = new ConsolidateDocumentDownloadUrlResponse();
		response.setUrl(downloadUrl);
		return response;
	}

	@Override
	public InsightsSupplierBuyerHeaderDataResponse getSupplierBuyerHeaderData(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		Object[] headerData = daoFactory.getDashboardGroupDao().getSupplierBuyerHeaderData(dashboardGroupH);
		
		InsightsSupplierBuyerHeaderDataResponse response = new InsightsSupplierBuyerHeaderDataResponse();
		response.setSupplierAmount((BigDecimal) headerData[0]);
		response.setBuyerAmount((BigDecimal) headerData[1]);
		response.setSupplierTrxCount((Integer) headerData[2]);
		response.setBuyerTrxCount((Integer) headerData[3]);
		return response;
	}

	@Override
	public SupplierBuyerMonthlyCashflowAndGrowthRateResponse getSupplierBuyerMonthlyCashflowAndGrowthRate(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		List<Object> netCashData = new ArrayList<>();
		List<Object> cashInData = new ArrayList<>();
		List<Object> cashOutData = new ArrayList<>();
		List<Object> growthRateData = new ArrayList<>();
		List<String> labels = new ArrayList<>();
		
		// Chart data
		List<Map<String, Object>> chartResults = daoFactory.getDashboardGroupDao().getSupplierBuyerCashFlowAndGrowthRate(dashboardGroupH);
		List<SupplierBuyerCashFlowDetailBean> cashFlowDetails = daoFactory.getDashboardGroupDao().getSupplierBuyerCashFlowDetails(dashboardGroupH);
		
		for (Map<String, Object> result : chartResults) {
			labels.add((String) result.get("d0"));
			netCashData.add(result.get("d1"));
			cashInData.add(result.get("d2"));
			cashOutData.add(result.get("d3"));
			growthRateData.add(result.get("d4"));
		}
		
		InsightsChartBean netCash = new InsightsChartBean();
		netCash.setLabel(CONST_NET_CASH);
		netCash.setData(netCashData);
		
		InsightsChartBean cashIn = new InsightsChartBean();
		cashIn.setLabel("Buyer (Cash In)");
		cashIn.setData(cashInData);
		
		InsightsChartBean cashOut = new InsightsChartBean();
		cashOut.setLabel("Supplier (Cash Out)");
		cashOut.setData(cashOutData);
		
		InsightsChartBean growthRate = new InsightsChartBean();
		growthRate.setLabel("Growth Rate");
		growthRate.setData(growthRateData);
		
		List<InsightsChartBean> cashFlowChartData = new ArrayList<>();
		cashFlowChartData.add(netCash);
		cashFlowChartData.add(cashIn);
		cashFlowChartData.add(cashOut);
		
		List<InsightsChartBean> growthRateChartData = new ArrayList<>();
		growthRateChartData.add(netCash);
		growthRateChartData.add(growthRate);
		
		SupplierBuyerMonthlyCashflowAndGrowthRateResponse response = new SupplierBuyerMonthlyCashflowAndGrowthRateResponse();
		response.setLabels(labels);
		response.setCashFlow(cashFlowChartData);
		response.setGrowthRate(growthRateChartData);
		response.setCashFlowDetails(cashFlowDetails);
		return response;
	}

	@Override
	public SupplierBuyerGpmWcrLrResponse getSupplierBuyerGpmWcrLr(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		List<String> labels = new ArrayList<>();
		List<Object> profitMarginData = new ArrayList<>();
		List<Object> regressionLineData = new ArrayList<>();
		List<Object> workingCapitalRatioData = new ArrayList<>();
		List<Object> workingCapitalRatioRegressionLineData = new ArrayList<>();
		List<Object> liquidityRatioData = new ArrayList<>();
		
		List<Map<String, Object>> chartData = daoFactory.getDashboardGroupDao().getSupplierBuyerGpmWcrLr(dashboardGroupH);
		for (Map<String, Object> result : chartData) {
			labels.add((String) result.get("d0"));
			profitMarginData.add(result.get("d1"));
			regressionLineData.add(result.get("d2"));
			workingCapitalRatioData.add(result.get("d3"));
			workingCapitalRatioRegressionLineData.add(result.get("d4"));
			liquidityRatioData.add(result.get("d5"));
		}
		
		InsightsChartBean profitMargin = new InsightsChartBean("Profit Margin (%)", profitMarginData);
		InsightsChartBean regressionLine = new InsightsChartBean("Regression Line", regressionLineData);
		InsightsChartBean workingCapitalRatio = new InsightsChartBean("Working Capital Ratio", workingCapitalRatioData);
		InsightsChartBean workingCapitalRatioRegressionLine = new InsightsChartBean("Regression Line", workingCapitalRatioRegressionLineData);
		InsightsChartBean liquidityRatio = new InsightsChartBean("Liquidity Ratio", liquidityRatioData);
		
		List<InsightsChartBean> grossProfitMarginChart = new ArrayList<>();
		grossProfitMarginChart.add(regressionLine);
		grossProfitMarginChart.add(profitMargin);
		
		List<InsightsChartBean> workingCapitalRatioChart = new ArrayList<>();
		workingCapitalRatioChart.add(workingCapitalRatioRegressionLine);
		workingCapitalRatioChart.add(workingCapitalRatio);
		
		List<InsightsChartBean> liquidityRatioChart = new ArrayList<>();
		liquidityRatioChart.add(liquidityRatio);
		
		SupplierBuyerGpmWcrLrResponse response = new SupplierBuyerGpmWcrLrResponse();
		response.setLabels(labels);
		response.setGrossProfitMargin(grossProfitMarginChart);
		response.setWorkingCapitalRatio(workingCapitalRatioChart);
		response.setLiquidityRatio(liquidityRatioChart);
		return response;
	}
	
	private void setTopFiveBuyerData(SupplierBuyerTopFiveChartResponse response, TrDashboardGroupH dashboardGroupH) {
		List<String> buyerByAmountLabels = new ArrayList<>();
		List<Object> buyerByAmountData = new ArrayList<>();
		List<Object> buyerByAmountBarChartDetails = new ArrayList<>();
		
		List<String> buyerByFreqLabels = new ArrayList<>();
		List<Object> buyerByFreqData = new ArrayList<>();
		List<Object> buyerByFreqBarChartDetails = new ArrayList<>();
		
		List<Map<String, Object>> topBuyerData = daoFactory.getDashboardGroupDao().getTopFiveBuyer(dashboardGroupH);
		for (Map<String, Object> buyer : topBuyerData) {
			buyerByAmountLabels.add((String) buyer.get("d0"));
			buyerByAmountData.add(buyer.get("d1"));
			buyerByAmountBarChartDetails.add(buyer.get("d2"));
			buyerByFreqLabels.add((String) buyer.get("d3"));
			buyerByFreqBarChartDetails.add(buyer.get("d4"));
			buyerByFreqData.add(buyer.get("d5"));
		}
		
		InsightsChartBean buyerByAmountBarChartData = new InsightsChartBean();
		buyerByAmountBarChartData.setLabel("Total Amount");
		buyerByAmountBarChartData.setData(buyerByAmountData);
		List<InsightsChartBean> buyerByAmountBarChart = new ArrayList<>();
		buyerByAmountBarChart.add(buyerByAmountBarChartData);
		
		InsightsChartBean buyerByFreqBarChartData = new InsightsChartBean();
		buyerByFreqBarChartData.setLabel("Frequency");
		buyerByFreqBarChartData.setData(buyerByFreqData);
		List<InsightsChartBean> buyerByFreqBarChart = new ArrayList<>();
		buyerByFreqBarChart.add(buyerByFreqBarChartData);
		
		response.setTopBuyerByAmountLabels(buyerByAmountLabels);
		response.setTopBuyerByAmountPieChartData(buyerByAmountData);
		response.setTopBuyerByAmountBarChartData(buyerByAmountBarChart);
		response.setTopBuyerByAmountBarChartDataDetails(buyerByAmountBarChartDetails);
		
		response.setTopBuyerByFreqLabels(buyerByFreqLabels);
		response.setTopBuyerByFreqPieChartData(buyerByFreqData);
		response.setTopBuyerByFreqBarChartData(buyerByFreqBarChart);
		response.setTopBuyerByFreqBarChartDataDetails(buyerByFreqBarChartDetails);
	}
	
	private void setTopFiveSupplierData(SupplierBuyerTopFiveChartResponse response, TrDashboardGroupH dashboardGroupH) {
		
		// Preparing top 5 supplier by amount & by frequency
		List<String> supplierByAmountLabels = new ArrayList<>();
		List<Object> supplierByAmountData = new ArrayList<>();
		List<Object> supplierByAmountBarChartDetails = new ArrayList<>();
		
		List<String> supplierByFreqLabels = new ArrayList<>();
		List<Object> supplierByFreqData = new ArrayList<>();
		List<Object> supplierByFreqBarChartDetails = new ArrayList<>();
		
		List<Map<String, Object>> supplierData = daoFactory.getDashboardGroupDao().getTopFiveSupplier(dashboardGroupH);
		for (Map<String, Object> supplier : supplierData) {
			supplierByAmountLabels.add((String) supplier.get("d0"));
			supplierByAmountData.add(supplier.get("d1"));
			supplierByAmountBarChartDetails.add(supplier.get("d2"));
			supplierByFreqLabels.add((String) supplier.get("d3"));
			supplierByFreqBarChartDetails.add(supplier.get("d4"));
			supplierByFreqData.add(supplier.get("d5"));
		}
		
		InsightsChartBean supplierByAmountBarChartData = new InsightsChartBean();
		supplierByAmountBarChartData.setLabel("Total Amount");
		supplierByAmountBarChartData.setData(supplierByAmountData);
		List<InsightsChartBean> supplierByAmountBarChart = new ArrayList<>();
		supplierByAmountBarChart.add(supplierByAmountBarChartData);
		
		InsightsChartBean supplierByFreqBarChartData = new InsightsChartBean();
		supplierByFreqBarChartData.setLabel("Frequency");
		supplierByFreqBarChartData.setData(supplierByFreqData);
		List<InsightsChartBean> supplierByFreqBarChart = new ArrayList<>();
		supplierByFreqBarChart.add(supplierByFreqBarChartData);
		
		response.setTopSupplierByAmountLabels(supplierByAmountLabels);
		response.setTopSupplierByAmountPieChartData(supplierByAmountData);
		response.setTopSupplierByAmountBarChartData(supplierByAmountBarChart);
		response.setTopSupplierByAmountBarChartDataDetails(supplierByAmountBarChartDetails);
		
		response.setTopSupplierByFreqLabels(supplierByFreqLabels);
		response.setTopSupplierByFreqPieChartData(supplierByFreqData);
		response.setTopSupplierByFreqBarChartData(supplierByFreqBarChart);
		response.setTopSupplierByFreqBarChartDataDetails(supplierByFreqBarChartDetails);
	}

	@Override
	public SupplierBuyerTopFiveChartResponse getSupplierBuyerTopFiveChartData(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		SupplierBuyerTopFiveChartResponse response = new SupplierBuyerTopFiveChartResponse();
		setTopFiveSupplierData(response, dashboardGroupH);
		setTopFiveBuyerData(response, dashboardGroupH);
		return response;
	}

	@Override
	public InsightsGeneralHeaderDataResponse getGeneralInsightsHeaderData(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		Object[] headerData = daoFactory.getDashboardGroupDao().getGeneralInsightsHeaderData(dashboardGroupH);
		
		InsightsGeneralHeaderDataResponse response = new InsightsGeneralHeaderDataResponse();
		response.setOpeningBalance((BigDecimal) headerData[0]);
		response.setEndingbalance((BigDecimal) headerData[1]);
		response.setOverallCredit((BigDecimal) headerData[2]);
		response.setOverallDebit((BigDecimal) headerData[3]);
		response.setCreditCount((BigInteger) headerData[4]);
		response.setDebitCount((BigInteger) headerData[5]);
		return response;
	}

	@Override
	public GeneralMonthlyCashFlowAnalysisDataResponse getGeneralMonthlyCashFlowAnalysisData(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		List<String> labels = new ArrayList<>();
		
		// Monthly Cash Flow Analysis data
		List<Object> netCashData = new ArrayList<>();
		List<Object> cashFlowCashInData = new ArrayList<>();
		List<Object> cashFlowCashOutData = new ArrayList<>();
		List<Object> endingBalanceData = new ArrayList<>();
		
		// Monthly Cash Flow Analysis hover data
		List<GeneralCashFlowDetailBean> cashFlowDetails = new ArrayList<>();
		
		// Transaction Type Count Analysis data
		List<Object> creditCountData = new ArrayList<>();
		List<Object> debitCountData = new ArrayList<>();
		
		List<Map<String, Object>> chartData = daoFactory.getDashboardGroupDao().getGeneralInsightCashFlowAnalysis(dashboardGroupH);
		
		for (Map<String, Object> periodData : chartData) {
			// Set labels
			labels.add((String) periodData.get("d0"));
			
			// Set monthly cash flow analysis data
			netCashData.add(periodData.get("d1"));
			cashFlowCashInData.add(periodData.get("d2"));
			cashFlowCashOutData.add(((BigDecimal) periodData.get("d3")).negate()); // to return negative number
			endingBalanceData.add(periodData.get("d4"));
			
			// Set monthly cash flow analysis hover data
			GeneralCashFlowDetailBean detail = new GeneralCashFlowDetailBean();
			detail.setPeriod((String) periodData.get("d0"));
			detail.setNetCash((BigDecimal) periodData.get("d1"));
			detail.setTotalCreditAmount((BigDecimal) periodData.get("d5"));
			detail.setAverageCreditAmount((BigDecimal) periodData.get("d6"));
			detail.setFrequencyCreditDays((BigInteger) periodData.get("d7"));
			detail.setTotalDebitAmount((BigDecimal) periodData.get("d8"));
			detail.setAverageDebitAmount((BigDecimal) periodData.get("d9"));
			detail.setFrequencyDebitDays((BigInteger) periodData.get("d10"));
			detail.setHighestBalance((BigDecimal) periodData.get("d11"));
			detail.setLowestBalance((BigDecimal) periodData.get("d12"));
			detail.setDailyAverageBalance((BigDecimal) periodData.get("d13"));
			detail.setEndingBalance((BigDecimal) periodData.get("d4"));
			cashFlowDetails.add(detail);
			
			// Set transaction type count analysis data
			creditCountData.add(periodData.get("d14"));
			debitCountData.add(periodData.get("d15"));
		}
		
		InsightsChartBean netCash = new InsightsChartBean();
		netCash.setLabel(CONST_NET_CASH);
		netCash.setData(netCashData);
		
		InsightsChartBean cashFlowcashIn = new InsightsChartBean();
		cashFlowcashIn.setLabel("Cash In");
		cashFlowcashIn.setData(cashFlowCashInData);
		
		InsightsChartBean cashFlowCashOut = new InsightsChartBean();
		cashFlowCashOut.setLabel("Cash Out");
		cashFlowCashOut.setData(cashFlowCashOutData);
		
		InsightsChartBean endingBalance = new InsightsChartBean();
		endingBalance.setLabel("Ending Balance");
		endingBalance.setData(endingBalanceData);
		
		List<InsightsChartBean> cashFlowChart = new ArrayList<>();
		cashFlowChart.add(netCash);
		cashFlowChart.add(endingBalance);
		cashFlowChart.add(cashFlowcashIn);
		cashFlowChart.add(cashFlowCashOut);
		
		InsightsChartBean creditCount = new InsightsChartBean();
		creditCount.setLabel("Credit");
		creditCount.setData(creditCountData);
		
		InsightsChartBean debitCount = new InsightsChartBean();
		debitCount.setLabel("Debit");
		debitCount.setData(debitCountData);
		
		List<InsightsChartBean> transactionCountChart = new ArrayList<>();
		transactionCountChart.add(creditCount);
		transactionCountChart.add(debitCount);
		
		GeneralMonthlyCashFlowAnalysisDataResponse response = new GeneralMonthlyCashFlowAnalysisDataResponse();
		response.setLabels(labels);
		response.setCashFlow(cashFlowChart);
		response.setCashFlowDetails(cashFlowDetails);
		response.setTransactionCount(transactionCountChart);
		return response;
	}

	@Override
	public GeneralBodyCardDataResponse getGeneralBodyCardData(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		Object[] result = daoFactory.getDashboardGroupDao().getGeneralBodyCardData(dashboardGroupH);
		
		GeneralBodyCardDataResponse response = new GeneralBodyCardDataResponse();
		response.setHigestBalance((BigDecimal) result[0]);
		response.setHighestBalanceDate((String) result[1]);
		response.setLowestBalance((BigDecimal) result[2]);
		response.setLowestBalanceDate((String) result[3]);
		response.setMonthlyAverageBalance((BigDecimal) result[4]);
		response.setDailyAverageBalance((BigDecimal) result [5]);
		response.setGrowthRate((BigDecimal) result[6]);
		return response;
	}

	@Override
	public GeneralDailyAnalysisDataResponse getGeneralDailyAnalysisData(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		List<String> labels = new ArrayList<>();
		
		// Ending balance analysis data
		List<Object> endingBalanceData = new ArrayList<>();
		List<Object> weeklyMovingAverageData = new ArrayList<>();
		
		// Ending balance analysis hover data
		List<Map<String, Object>> endingBalanceAnalysisDetails = new ArrayList<>();
		
		// Daily cash flow analysis data
		List<Object> netCashData = new ArrayList<>();
		List<Object> cashInData = new ArrayList<>();
		List<Object> cashOutData = new ArrayList<>();
		
		// Daily cash flow analysis hover data
		List<Map<String, Object>> dailyCashFlowAnalysisDetails = new ArrayList<>();
		
		List<Map<String, Object>> results = daoFactory.getDashboardGroupDao().getGeneralDailyAnalysisData(dashboardGroupH);
		
		for (Map<String, Object> result : results) {
			// Set labels
			labels.add((String) result.get("d0"));
			
			// Set ending balance analysis data
			endingBalanceData.add(result.get("d1"));
			weeklyMovingAverageData.add(result.get("d2"));
			
			// Set ending balance analysis hover data
			Map<String, Object> endingBalanceAnalysisDetail = new HashMap<>();
			endingBalanceAnalysisDetail.put("date", result.get("d0"));
			endingBalanceAnalysisDetail.put("endingBalance", result.get("d1"));
			endingBalanceAnalysisDetail.put("sevenDaysMovingAvg", result.get("d2"));
			endingBalanceAnalysisDetails.add(endingBalanceAnalysisDetail);
			
			// Set daily cash flow analysis data
			netCashData.add(result.get("d3"));
			cashInData.add(result.get("d4"));
			cashOutData.add(((BigDecimal) result.get("d5")).negate());
			
			// Set daily cash flow analysis hover data
			Map<String, Object> dailyCashFlowAnalysisDetail = new HashMap<>();
			dailyCashFlowAnalysisDetail.put("date", result.get("d0"));
			dailyCashFlowAnalysisDetail.put("totalCreditAmount", result.get("d4"));
			dailyCashFlowAnalysisDetail.put("totalCreditCount", result.get("d6"));
			dailyCashFlowAnalysisDetail.put("totalDebitAmount", result.get("d5"));
			dailyCashFlowAnalysisDetail.put("totalDebitCount", result.get("d7"));
			dailyCashFlowAnalysisDetail.put("netCash", result.get("d3"));
			dailyCashFlowAnalysisDetail.put("endingBalance", result.get("d1"));
			dailyCashFlowAnalysisDetails.add(dailyCashFlowAnalysisDetail);
		}
		
		InsightsChartBean endingBalance = new InsightsChartBean();
		endingBalance.setLabel("Ending Balance");
		endingBalance.setData(endingBalanceData);
		
		InsightsChartBean weeklyMovingAverage = new InsightsChartBean();
		weeklyMovingAverage.setLabel("Weekly Moving Average");
		weeklyMovingAverage.setData(weeklyMovingAverageData);
		
		List<InsightsChartBean> endingBalanceChart = new ArrayList<>();
		endingBalanceChart.add(endingBalance);
		endingBalanceChart.add(weeklyMovingAverage);
		
		InsightsChartBean netCash = new InsightsChartBean();
		netCash.setLabel(CONST_NET_CASH);
		netCash.setData(netCashData);
		
		InsightsChartBean cashIn = new InsightsChartBean();
		cashIn.setLabel("Cash In");
		cashIn.setData(cashInData);
		
		InsightsChartBean cashOut = new InsightsChartBean();
		cashOut.setLabel("Cash Out");
		cashOut.setData(cashOutData);
		
		List<InsightsChartBean> dailyCashFlowChart = new ArrayList<>();
		dailyCashFlowChart.add(netCash);
		dailyCashFlowChart.add(endingBalance);
		dailyCashFlowChart.add(cashIn);
		dailyCashFlowChart.add(cashOut);
		
		GeneralDailyAnalysisDataResponse response = new GeneralDailyAnalysisDataResponse();
		response.setLabels(labels);
		response.setEndingBalance(endingBalanceChart);
		response.setEndingBalanceDetails(endingBalanceAnalysisDetails);
		response.setCashFlow(dailyCashFlowChart);
		response.setCashFlowDetails(dailyCashFlowAnalysisDetails);
		return response;
	}

	@Override
	public InsightsGeneralCircularHeaderDataResponse getGeneralCircularHeaderData(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		Object[] result = daoFactory.getDashboardGroupDao().getGeneralInsightsCircularHeaderData(dashboardGroupH);
		
		InsightsGeneralCircularHeaderDataResponse response = new InsightsGeneralCircularHeaderDataResponse();
		response.setRealCircularCount((Integer) result[0]);
		response.setCircularCount((Integer) result[1]);
		response.setCircularCountRatio((BigDecimal) result[2]);
		response.setRealCircularAmount((BigDecimal) result[3]);
		response.setCircularAmount((BigDecimal) result[4]);
		response.setCircularAmountRatio((BigDecimal) result[5]);
		return response;
	}

	@Override
	public GeneralCircularChartDataResponse getGeneralCircularChartData(GenericDashboardDropdownListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		
		List<String> labels = new ArrayList<>();
		
		// Amount comparison data
		List<Object> circularAmountData = new ArrayList<>();
		List<Object> totalAmountData = new ArrayList<>();
		
		// Circular percentage data
		List<Object> percentageData = new ArrayList<>();
		
		// Circular percentage data detail
		List<Map<String, Object>> percentageDetails = new ArrayList<>();
		
		List<Map<String, Object>> results = daoFactory.getDashboardGroupDao().getGeneralInsightsCircularChartData(dashboardGroupH);
		
		for (Map<String, Object> result : results) {
			
			// Set labels
			labels.add((String) result.get("d0"));
			
			// Set amount comparison data
			circularAmountData.add(result.get("d1"));
			totalAmountData.add(result.get("d2"));
			
			// Set circular percentage data
			percentageData.add(result.get("d3"));
			
			// Set circular percentage data details
			Map<String, Object> detail = new HashMap<>();
			detail.put("period", result.get("d0"));
			detail.put("circularAmount", result.get("d1"));
			detail.put("totalAmount", result.get("d2"));
			percentageDetails.add(detail);
		}
		
		InsightsChartBean circularAmount = new InsightsChartBean();
		circularAmount.setLabel("Circular Transaction Amount");
		circularAmount.setData(circularAmountData);
		
		InsightsChartBean totalAmount = new InsightsChartBean();
		totalAmount.setLabel("Total Transaction Amount");
		totalAmount.setData(totalAmountData);
		
		List<InsightsChartBean> amountComparison = new ArrayList<>();
		amountComparison.add(circularAmount);
		amountComparison.add(totalAmount);
		
		InsightsChartBean percentage = new InsightsChartBean();
		percentage.setLabel("Percentage");
		percentage.setData(percentageData);
		
		List<InsightsChartBean> circularPercentage = new ArrayList<>();
		circularPercentage.add(percentage);
		
		GeneralCircularChartDataResponse response = new GeneralCircularChartDataResponse();
		response.setLabels(labels);
		response.setAmountComparison(amountComparison);
		response.setCircularPercentage(circularPercentage);
		response.setCircularPercentageDetails(percentageDetails);
		return response;
	}

}
