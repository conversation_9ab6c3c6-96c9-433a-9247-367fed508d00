package com.adins.bsa.validator.impl;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;

import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.model.TrOcrResultSummaryPeriod;
import com.adins.bsa.util.MssTool;
import com.adins.bsa.validator.api.OcrResultValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericOcrResultValidator extends BaseLogic implements OcrResultValidator {

	@Override
	public void validateAccountNoOnDashboard(TrDashboardGroupH trDashboardGroupH, String accountNo, AuditContext audit) {
		
		if (StringUtils.isBlank(accountNo)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Account No"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		long countDashboardAccountNo = daoFactory.getOcrResultDao().countOcrResultByDashboardHAndAccountNo(trDashboardGroupH, accountNo);
		if (countDashboardAccountNo == 0) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_2,
					new String[] {accountNo, trDashboardGroupH.getDashboardGroupName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
	}

	@Override
	public TrOcrResultDetail getOcrResultDetail(TrDashboardGroupH dashboardGroupH, String resultDetailId, AuditContext audit) {
		if (StringUtils.isBlank(resultDetailId)) {
			throw new CommonException(getMessage(resultDetailId, new String[] {"Result detail ID"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrOcrResultDetail detail = daoFactory.getOcrResultDao().getOcrResultDetail(dashboardGroupH, resultDetailId);
		if (null == detail) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_OBJ_NOT_FOUND, new String[] {"Result ID", resultDetailId}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return detail;
	}

	@Override
	public TrOcrResultSummaryPeriod getOcrResultSummaryPeriod(TrDashboardGroupD dashboardGroupD, String period, AuditContext audit) {
		
		if (StringUtils.isBlank(period)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Period"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
			sdf.setLenient(false);
			sdf.parse(period);
		} catch (Exception e) {
			throw new CommonException(getMessage("businesslogic.global.dateformat", new String[] {"Period", GlobalVal.DATE_FORMAT}, audit), ReasonCommon.INVALID_FORMAT);
		}
		
		Date periodDate = MssTool.formatStringToDate(period + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
		TrOcrResultSummaryPeriod summaryPeriod = daoFactory.getOcrResultDao().getOcrResultSummaryPeriod(dashboardGroupD, periodDate);
		if (null == summaryPeriod) {
			String objectName = "Summary for period " + period;
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {objectName}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return summaryPeriod;
	}

	@Override
	public TrOcrResultDetail getOcrResultDetail(TrDashboardGroupD dashboardGroupD, String resultDetailId, AuditContext audit) {
		
		if (StringUtils.isBlank(resultDetailId)) {
			throw new CommonException(getMessage(resultDetailId, new String[] {"Result detail ID"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrOcrResultDetail ocrResultDetail = daoFactory.getOcrResultDao().getOcrResultDetail(dashboardGroupD, resultDetailId);
		if (null == ocrResultDetail) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_2, new String[] {"Result ID", dashboardGroupD.getFileName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return ocrResultDetail;
	}

}
