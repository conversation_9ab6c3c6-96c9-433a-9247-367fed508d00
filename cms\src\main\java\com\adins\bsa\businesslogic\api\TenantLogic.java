package com.adins.bsa.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import com.adins.bsa.webservices.model.tenant.AddTenantRequest;
import com.adins.bsa.webservices.model.tenant.AddTenantUserRequest;
import com.adins.bsa.webservices.model.tenant.TenantListRequest;
import com.adins.bsa.webservices.model.tenant.TenantListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface TenantLogic {

	@RolesAllowed({"ROLE_TENANT"})
	MssResponseType addTenant(AddTenantRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_TENANT"})
	MssResponseType addTenantUser(AddTenantUserRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_TENANT"})
	TenantListResponse getTenantList(TenantListRequest request, AuditContext audit);
	
}
