package com.adins.bsa.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;

@Entity
@Table(name = "tr_dashboard_group_h")
public class TrDashboardGroupH extends ActiveAndUpdateableEntity implements Serializable {
	
	public static final String PARAM_ID_DASHBOARD_GROUP_H = "idDashboardGroupH";
	public static final String PARAM_DASHBOARD_GROUP_H = "dashboardGroupH";
	public static final String PARAM_DASHBOARD_NAME = "dashboardName";
	
	private static final long serialVersionUID = 1L;
	private long idDashboardGroupH;
	private MsTenant msTenant;
	private AmMsuser amMsuserCreator;
	private String dashboardGroupName;
	private Integer totalFiles;
	private Date consolidateDate;
	private String isConsolidating;
	private String isAutoConsolidate;
	
	@Column(name = "is_auto_consolidate", length = 1)
	public String getIsAutoConsolidate() {
		return isAutoConsolidate;
	}

	public void setIsAutoConsolidate(String isAutoConsolidate) {
		this.isAutoConsolidate = isAutoConsolidate;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_dashboard_group_h", unique = true, nullable = false)
	public long getIdDashboardGroupH() {
		return idDashboardGroupH;
	}
	
	public void setIdDashboardGroupH(long idDashboardGroupH) {
		this.idDashboardGroupH = idDashboardGroupH;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return msTenant;
	}
	
	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user_creator", nullable = false)
	public AmMsuser getAmMsuserCreator() {
		return amMsuserCreator;
	}
	
	public void setAmMsuserCreator(AmMsuser amMsuserCreator) {
		this.amMsuserCreator = amMsuserCreator;
	}
	
	@Column(name = "dashboard_group_name", length = 64)
	public String getDashboardGroupName() {
		return dashboardGroupName;
	}
	
	public void setDashboardGroupName(String dashboardGroupName) {
		this.dashboardGroupName = dashboardGroupName;
	}
	
	@Column(name = "total_files")
	public Integer getTotalFiles() {
		return totalFiles;
	}
	
	public void setTotalFiles(Integer totalFiles) {
		this.totalFiles = totalFiles;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "consolidate_date")
	public Date getConsolidateDate() {
		return consolidateDate;
	}
	
	public void setConsolidateDate(Date consolidateDate) {
		this.consolidateDate = consolidateDate;
	}

	@Column(name = "is_consolidating", length = 1)
	public String getIsConsolidating() {
		return isConsolidating;
	}

	public void setIsConsolidating(String isConsolidating) {
		this.isConsolidating = isConsolidating;
	}
	
}
