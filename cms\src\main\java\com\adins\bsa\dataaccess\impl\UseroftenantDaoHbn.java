package com.adins.bsa.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.dataaccess.api.UseroftenantDao;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.MsUseroftenant;

@Component
@Transactional
public class UseroftenantDaoHbn extends BaseDaoHbn implements UseroftenantDao {

    @Override
    @Transactional(readOnly = true)
    public MsUseroftenant getUseroftenantReadOnly(AmMsuser user, MsTenant tenant) {
        Map<String, Object> params = new HashMap<>();
        params.put("user", user);
        params.put("tenant", tenant);

        return managerDAO.selectOne(
            "from MsUseroftenant uot "
            + "where uot.amMsuser = :user "
            + "and uot.msTenant = :tenant ", params);
    }
    
}
