package com.adins.bsa.webservices.model.insights;

import java.util.List;
import java.util.Map;

import com.adins.bsa.custom.InsightsChartBean;
import com.adins.framework.service.base.model.MssResponseType;

public class GeneralCircularChartDataResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<String> labels;
	private List<InsightsChartBean> amountComparison;
	private List<InsightsChartBean> circularPercentage;
	private transient List<Map<String, Object>> circularPercentageDetails;
	
	public List<String> getLabels() {
		return labels;
	}
	public void setLabels(List<String> labels) {
		this.labels = labels;
	}
	public List<InsightsChartBean> getAmountComparison() {
		return amountComparison;
	}
	public void setAmountComparison(List<InsightsChartBean> amountComparison) {
		this.amountComparison = amountComparison;
	}
	public List<InsightsChartBean> getCircularPercentage() {
		return circularPercentage;
	}
	public void setCircularPercentage(List<InsightsChartBean> circularPercentage) {
		this.circularPercentage = circularPercentage;
	}
	public List<Map<String, Object>> getCircularPercentageDetails() {
		return circularPercentageDetails;
	}
	public void setCircularPercentageDetails(List<Map<String, Object>> circularPercentageDetails) {
		this.circularPercentageDetails = circularPercentageDetails;
	}
	
}
