package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.TenantLogic;
import com.adins.bsa.webservices.frontend.api.TenantService;
import com.adins.bsa.webservices.model.tenant.AddTenantRequest;
import com.adins.bsa.webservices.model.tenant.AddTenantUserRequest;
import com.adins.bsa.webservices.model.tenant.TenantListRequest;
import com.adins.bsa.webservices.model.tenant.TenantListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/tenant")
@Api(value = "TenantService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericTenantServiceEndpoint implements TenantService {

	@Autowired private TenantLogic tenantLogic;

	@Override
	@POST
	@Path("/s/add")
	public MssResponseType addTenant(AddTenantRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.addTenant(request, audit);
	}

	@Override
	@POST
	@Path("/s/addUser")
	public MssResponseType addTenantUser(AddTenantUserRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.addTenantUser(request, audit);
	}

	@Override
	@POST
	@Path("/s/list")
	public TenantListResponse getTenantList(TenantListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getTenantList(request, audit);
	}
}
