<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />
    <include resource="org/springframework/boot/logging/logback/file-appender.xml" />

	<property name="LOG_FILE" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}/}bsa-cms.log}"/>

	<appender name="FILETRACEDATA" class="ch.qos.logback.core.rolling.RollingFileAppender">
	    <File>${LOG_PATH}/bsa-ws-tracedata.log</File>
	    <encoder>
	      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %level:[%thread] \(%line\)%logger{0}.%method: %message%n</pattern>
	    </encoder>
	    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
	      <FileNamePattern>${LOG_PATH}/%d{yyyy-MM-dd, aux}/bsa-ws-tracedata-%d{yyyy-MM-dd-HH}.%i.log.gz</FileNamePattern>
	      <maxFileSize>${LOG_FILE_MAX_SIZE:-100MB}</maxFileSize>
	      <maxHistory>${LOG_FILE_MAX_HISTORY:-1000}</maxHistory>
	      <totalSizeCap>${LOG_FILE_TOTAL_SIZE_CAP:-5GB}</totalSizeCap>
	    </rollingPolicy>
	</appender>
	
	<logger name="org.apache.cxf.services" additivity="false" level="INFO">
	    <appender-ref ref="FILETRACEDATA"/>
	</logger>

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>        
</configuration>