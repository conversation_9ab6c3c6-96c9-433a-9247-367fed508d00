package com.adins.bsa;

import java.util.concurrent.Executor;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import com.ulisesbocchio.jasyptspringboot.environment.StandardEncryptableEnvironment;

@SpringBootApplication
@EnableEncryptableProperties 
@EnableScheduling
@EnableAspectJAutoProxy
@EnableAsync
@ImportResource({"classpath:spring/*-context.xml"})
@ComponentScan(basePackages={"com.adins.am.businesslogic", "com.adins.bsa"})
public class BsaApplication {

	public static void main(String[] args) {
		new SpringApplicationBuilder()
			.environment(new StandardEncryptableEnvironment())
			.sources(BsaApplication.class).run(args);
	}

	@Bean
	public Executor taskExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(5);
		executor.setMaxPoolSize(20);
		executor.setQueueCapacity(100);
		executor.setThreadNamePrefix("TaskExec-");
		executor.initialize();
		return executor;
	}
}
