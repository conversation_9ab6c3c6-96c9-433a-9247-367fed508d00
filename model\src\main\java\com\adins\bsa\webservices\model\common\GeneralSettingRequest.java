package com.adins.bsa.webservices.model.common;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.framework.service.base.model.MssRequestType;

public class GeneralSettingRequest extends MssRequestType {
	
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("General setting code")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 80)
	private String generalSettingCode;

	public String getGeneralSettingCode() {
		return generalSettingCode;
	}

	public void setGeneralSettingCode(String generalSettingCode) {
		this.generalSettingCode = generalSettingCode;
	}
	
}
