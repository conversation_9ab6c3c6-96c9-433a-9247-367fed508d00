package com.adins.bsa.validator.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.model.TrConsolidateResultBusinessGroupingD;
import com.adins.bsa.model.TrConsolidateResultBusinessGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.validator.api.BusinessGroupingValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericBusinessGroupingValidator extends BaseLogic implements BusinessGroupingValidator {

	@Override
	public TrConsolidateResultBusinessGroupingH validateGetGroupingHeader(TrDashboardGroupH dashboardGroupH, String groupName, boolean objectMustExist, AuditContext audit) {
		
		if (StringUtils.isBlank(groupName)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Group name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrConsolidateResultBusinessGroupingH groupingH = daoFactory.getBusinessTransactionGroupDao().getBusinessGroupingH(dashboardGroupH, groupName);
		if (null == groupingH && objectMustExist) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {groupName}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return groupingH;
	}

	@Override
	public TrConsolidateResultBusinessGroupingD validateGetGroupingDetail(TrConsolidateResultBusinessGroupingH groupingH, String resultDetailId, boolean objectMustExist, AuditContext audit) {
		
		if (StringUtils.isBlank(resultDetailId)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Result detail ID"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrConsolidateResultBusinessGroupingD groupingD = daoFactory.getBusinessTransactionGroupDao().getBusinessGroupingD(groupingH, resultDetailId);
		if (null == groupingD && objectMustExist) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {"Group detail"}, audit) , ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return groupingD;
	}

}
