<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd        
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">
		
	<bean id="responseCodeAspect" class="com.adins.framework.service.base.aspect.ResponseCodeAdvice" />
	<bean id="mobileBase" class="com.adins.framework.service.base.MobileEncryptionBase" abstract="true">
      <property name="encryptResponse" value="${mobile.encrypt:false}" />
      <property name="decryptRequest" value="${mobile.decrypt:false}" />
    </bean>
    <bean id="defaultMobileBase" class="com.adins.framework.service.base.DefaultMobileEncryption" parent="mobileBase" />
    <bean id="mobileAspect" class="com.adins.framework.service.base.aspect.MobileAdvice">
    	<property name="encryptionHelper" ref="defaultMobileBase" />
    </bean>
	
	<aop:config>
	  <aop:aspect id="modelResponseCode" ref="responseCodeAspect">
	    <aop:pointcut expression="execution(public * com.adins.bsa.webservices.*.endpoint..*(..))" id="projectEndpointWsCall1" />
	    <aop:around method="invoke" pointcut-ref="projectEndpointWsCall1" />
	  </aop:aspect>
	  <aop:aspect id="stringResponseCode" ref="mobileAspect">
	    <aop:pointcut expression="execution(public * com.adins.bsa.webservices.*.endpoint..*(..))" id="projectEndpointWsCall2" />
	    <aop:around method="invoke" pointcut-ref="projectEndpointWsCall2" />
	  </aop:aspect>
	</aop:config>
	
</beans>