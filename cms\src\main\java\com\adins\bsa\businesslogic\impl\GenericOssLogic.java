package com.adins.bsa.businesslogic.impl;

import java.nio.charset.StandardCharsets;
import java.util.Date;

import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.bsa.businesslogic.api.OssLogic;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.webservices.model.aliyun.OssSignatureResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.service.base.model.MssRequestType;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;

@Component
public class GenericOssLogic extends BaseLogic implements OssLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericOssLogic.class);
	
	@Autowired private OSS ossClient;
	
	@Value("${alicloud.access-key}") private String accessKey;
	@Value("${alicloud.oss.endpoint.bucket}") private String bucketEndpoint;
	@Value("${alicloud.oss.bucket}") private String bucket;
	@Value("${alicloud.oss.folder.directupload}") private String directUploadFolder;

	@Override
	public OssSignatureResponse generateSignature(MssRequestType request) {
		
		Date expireDate = DateUtils.addMinutes(new Date(), getSignatureLifetime());
		
		PolicyConditions policyConditions = new PolicyConditions();
		policyConditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, getFileMaxSize());
		policyConditions.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, directUploadFolder);
		
		String postPolicy = ossClient.generatePostPolicy(expireDate, policyConditions);
		
		try {
			byte[] policyByteArray = postPolicy.getBytes(StandardCharsets.UTF_8);
			String encodedPolicy = BinaryUtil.toBase64String(policyByteArray);
			String postSignature = ossClient.calculatePostSignature(postPolicy);
			
			OssSignatureResponse response = new OssSignatureResponse();
			response.setOssAccessKeyId(accessKey);
			response.setPolicy(encodedPolicy);
			response.setSignature(postSignature);
			response.setDir(directUploadFolder);
			response.setHost(bucketEndpoint);
			return response;
			
		} catch (Exception e) {
			throw new CommonException(e.getLocalizedMessage(), ReasonCommon.UNKNOWN, e);
		}
		
	}
	
	private long getFileMaxSize() {
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_OSS_DIRECT_UPLOAD_MAX_SIZE);
		if (null == gs) {
			return 10485760L; // 10MB (1024*1024*10)
		}
		
		try {
			return Long.parseLong(gs.getGsValue());
		} catch (Exception e) {
			LOG.error("Failed to parse OSS_DIRECT_UPLOAD_MAX_SIZE");
			return 10485760L; // 10MB (1024*1024*10)
		}
	}
	
	/**
	 * @return value in minute(s)
	 */
	private int getSignatureLifetime() {
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_OSS_SIGNATURE_LIFETIME);
		if (null == gs) {
			return 5;
		}
		
		try {
			return Integer.parseInt(gs.getGsValue());
		} catch (Exception e) {
			LOG.error("Failed to parse OSS_SIGNATURE_LIFETIME");
			return 5;
		}
	}

	@Override
	public String generateDownloadSignedUrl(String filePath) {
		
		Date expireDate = DateUtils.addMinutes(new Date(), getSignatureLifetime());
		
		GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucket, filePath, HttpMethod.GET);
		request.setExpiration(expireDate);
		
		return ossClient.generatePresignedUrl(request).toString();
	}

	@Override
	public boolean isFileExist(String filePath) {
		return ossClient.doesObjectExist(bucket, filePath);
	}

}
