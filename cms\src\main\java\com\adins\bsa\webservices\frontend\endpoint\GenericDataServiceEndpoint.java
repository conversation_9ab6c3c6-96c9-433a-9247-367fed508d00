package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.CommonLogic;
import com.adins.bsa.webservices.frontend.api.DataService;
import com.adins.bsa.webservices.model.common.GeneralSettingRequest;
import com.adins.bsa.webservices.model.common.GeneralSettingResponse;
import com.adins.bsa.webservices.model.common.LovListRequest;
import com.adins.bsa.webservices.model.common.LovListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/data")
@Api(value = "DataService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericDataServiceEndpoint implements DataService {

	@Autowired private CommonLogic commonLogic;
	
	@Override
	@POST
	@Path("/lov")
	public LovListResponse getLovList(LovListRequest request) {
		return this.commonLogic.getLovByGroupAndConstraint(request);
	}

	@Override
	@POST
	@Path("/s/generalSetting")
	public GeneralSettingResponse getGeneralSetting(GeneralSettingRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return commonLogic.getGeneralSetting(request, audit);
	}
	
}
