package com.adins.bsa.businesslogic.api;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.bsa.webservices.model.anomaly.AddAnomaliesRequest;
import com.adins.bsa.webservices.model.anomaly.DeleteAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.DeleteGroupAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyMetadataRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyMetadataResponse;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyResponse;
import com.adins.bsa.webservices.model.anomaly.ListGroupAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListGroupAnomalyResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface AnomalyLogic {
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	ListAnomalyResponse getList(ListAnomalyRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType addAnomalies(AddAnomaliesRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType deleteAnomaly(DeleteAnomalyRequest request, AuditContext audit);

	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	ListGroupAnomalyResponse getListGroupAnomaly(ListGroupAnomalyRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType deleteGroupAnomaly(DeleteGroupAnomalyRequest request, AuditContext audit);

	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	ListAnomalyMetadataResponse getListAnomalyMetadata(ListAnomalyMetadataRequest request, AuditContext audit);
}
