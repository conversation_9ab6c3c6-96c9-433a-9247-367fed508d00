package com.adins.bsa.webservices.model.dashboard;

import com.adins.framework.service.base.model.MssResponseType;

public class DashboardWarningStatusResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private String ocrResultWarningStatus;
	private String circularWarningStatus;
	private String anomalyWarningStatus;
	private String anomalyWarningSeverity;
	
	public String getOcrResultWarningStatus() {
		return ocrResultWarningStatus;
	}
	public void setOcrResultWarningStatus(String ocrResultWarningStatus) {
		this.ocrResultWarningStatus = ocrResultWarningStatus;
	}
	public String getCircularWarningStatus() {
		return circularWarningStatus;
	}
	public void setCircularWarningStatus(String circularWarningStatus) {
		this.circularWarningStatus = circularWarningStatus;
	}
	public String getAnomalyWarningStatus() {
		return anomalyWarningStatus;
	}
	public void setAnomalyWarningStatus(String anomalyWarningStatus) {
		this.anomalyWarningStatus = anomalyWarningStatus;
	}
	public String getAnomalyWarningSeverity() {
		return anomalyWarningSeverity;
	}
	public void setAnomalyWarningSeverity(String anomalyWarningSeverity) {
		this.anomalyWarningSeverity = anomalyWarningSeverity;
	}
	
}
