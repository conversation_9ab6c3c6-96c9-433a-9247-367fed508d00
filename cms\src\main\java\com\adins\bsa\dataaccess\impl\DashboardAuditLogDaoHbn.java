package com.adins.bsa.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.bsa.dataaccess.api.DashboardAuditLogDao;
import com.adins.bsa.model.TrDashboardAuditLog;

@Component
@Transactional
public class DashboardAuditLogDaoHbn extends BaseDaoHbn implements DashboardAuditLogDao {

    @Override
    public void insertDashboardAuditLog(TrDashboardAuditLog auditLog) {
        managerDAO.insert(auditLog);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void insertDashboardAuditLogNewTrx(TrDashboardAuditLog auditLog) {
        managerDAO.insert(auditLog);
    }
    
}
