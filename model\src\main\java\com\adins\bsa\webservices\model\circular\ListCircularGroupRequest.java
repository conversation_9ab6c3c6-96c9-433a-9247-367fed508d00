package com.adins.bsa.webservices.model.circular;

import com.adins.bsa.annotations.StringValue;
import com.adins.bsa.annotations.enums.ValueType;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;

public class ListCircularGroupRequest extends GenericDashboardPagingRequest {
	
	private static final long serialVersionUID = 1L;
	
	@StringValue(ValueType.NUMERIC)
	private String accountNo;
	
	@StringValue(ValueType.NUMERIC)
	private String transactionWindow;
	
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public String getTransactionWindow() {
		return transactionWindow;
	}
	public void setTransactionWindow(String transactionWindow) {
		this.transactionWindow = transactionWindow;
	}
	
	
}
