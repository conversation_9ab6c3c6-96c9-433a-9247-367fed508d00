package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

public class AnomalyBean implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private BigInteger no;
	private String file;
	private String accountNo;
	private String accountNoBoxLocation;
	private Integer accountNoBoxPage;
	private String date;
	private String dateBoxLocation;
	private Integer dateBoxPage; 
	private String description;
	private String descriptionBoxLocation; 
	private Integer descriptionBoxPage; 
	private BigDecimal amount;
	private String amountBoxLocation;
	private Integer amountBoxPage;
	private String type;
	private String typeBoxLocation; 
	private Integer typeBoxPage; 
	private String anomalyId;
	private String risk;
	private BigDecimal endingBalance;
	private String endingBalanceBoxLocation;
	private Integer endingBalanceBoxPage;
	private String fileSourcePath;
	
	public BigInteger getNo() {
		return no;
	}
	public void setNo(BigInteger no) {
		this.no = no;
	}
	public String getFile() {
		return file;
	}
	public void setFile(String file) {
		this.file = file;
	}
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public String getAccountNoBoxLocation() {
		return accountNoBoxLocation;
	}
	public void setAccountNoBoxLocation(String accountNoBoxLocation) {
		this.accountNoBoxLocation = accountNoBoxLocation;
	}
	public Integer getAccountNoBoxPage() {
		return accountNoBoxPage;
	}
	public void setAccountNoBoxPage(Integer accountNoBoxPage) {
		this.accountNoBoxPage = accountNoBoxPage;
	}
	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public BigDecimal getAmount() {
		return amount;
	}
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getAnomalyId() {
		return anomalyId;
	}
	public void setAnomalyId(String anomalyId) {
		this.anomalyId = anomalyId;
	}
	public String getRisk() {
		return risk;
	}
	public void setRisk(String risk) {
		this.risk = risk;
	}
	public BigDecimal getEndingBalance() {
		return endingBalance;
	}
	public void setEndingBalance(BigDecimal endingBalance) {
		this.endingBalance = endingBalance;
	}
	public String getDateBoxLocation() {
		return dateBoxLocation;
	}
	public void setDateBoxLocation(String dateBoxLocation) {
		this.dateBoxLocation = dateBoxLocation;
	}
	public Integer getDateBoxPage() {
		return dateBoxPage;
	}
	public void setDateBoxPage(Integer dateBoxPage) {
		this.dateBoxPage = dateBoxPage;
	}
	public String getDescriptionBoxLocation() {
		return descriptionBoxLocation;
	}
	public void setDescriptionBoxLocation(String descriptionBoxLocation) {
		this.descriptionBoxLocation = descriptionBoxLocation;
	}
	public Integer getDescriptionBoxPage() {
		return descriptionBoxPage;
	}
	public void setDescriptionBoxPage(Integer descriptionBoxPage) {
		this.descriptionBoxPage = descriptionBoxPage;
	}
	public String getAmountBoxLocation() {
		return amountBoxLocation;
	}
	public void setAmountBoxLocation(String amountBoxLocation) {
		this.amountBoxLocation = amountBoxLocation;
	}
	public Integer getAmountBoxPage() {
		return amountBoxPage;
	}
	public void setAmountBoxPage(Integer amountBoxPage) {
		this.amountBoxPage = amountBoxPage;
	}
	public String getEndingBalanceBoxLocation() {
		return endingBalanceBoxLocation;
	}
	public void setEndingBalanceBoxLocation(String endingBalanceBoxLocation) {
		this.endingBalanceBoxLocation = endingBalanceBoxLocation;
	}
	public Integer getEndingBalanceBoxPage() {
		return endingBalanceBoxPage;
	}
	public void setEndingBalanceBoxPage(Integer endingBalanceBoxPage) {
		this.endingBalanceBoxPage = endingBalanceBoxPage;
	}
	public String getTypeBoxLocation() {
		return typeBoxLocation;
	}
	public void setTypeBoxLocation(String typeBoxLocation) {
		this.typeBoxLocation = typeBoxLocation;
	}
	public Integer getTypeBoxPage() {
		return typeBoxPage;
	}
	public void setTypeBoxPage(Integer typeBoxPage) {
		this.typeBoxPage = typeBoxPage;
	}
	public String getFileSourcePath() {
		return fileSourcePath;
	}
	public void setFileSourcePath(String fileSourcePath) {
		this.fileSourcePath = fileSourcePath;
	}
	
}
