
package com.adins.bsa.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.businesslogic.api.BusinessTransactionGroupLogic;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.custom.NonBusinessTransactionGroupBean;
import com.adins.bsa.custom.NonBusinessTransactionGroupDetailBean;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrConsolidateResultBusinessGroupingD;
import com.adins.bsa.model.TrConsolidateResultBusinessGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.validator.api.BusinessGroupingValidator;
import com.adins.bsa.validator.api.DashboardValidator;
import com.adins.bsa.validator.api.ObjectRequestValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.bsa.webservices.model.businesstransactiongroup.ListBusinessTransactionGroupDetailResponse;
import com.adins.bsa.webservices.model.businesstransactiongroup.ListBusinessTransactionGroupResponse;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailRequest;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

@Component
public class GenericBusinessTransactionGroupLogic extends BaseLogic implements BusinessTransactionGroupLogic {

	@Autowired private BusinessGroupingValidator businessGroupingValidator;
	@Autowired private DashboardValidator dashboardValidator;
	@Autowired private TenantValidator tenantValidator;
	@Autowired private UserValidator userValidator;
	@Autowired private ObjectRequestValidator requestValidator;

	@Override
	public ListBusinessTransactionGroupResponse getListBusinessTransactionGroup(GenericDashboardPagingRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		List<NonBusinessTransactionGroupBean> listGroup = daoFactory.getBusinessTransactionGroupDao().getListBusinessTransactionGroup(dashboardGroupH, min, max);
		long totalData = daoFactory.getBusinessTransactionGroupDao().countListBusinessTransactionGroup(dashboardGroupH);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		ListBusinessTransactionGroupResponse response = new ListBusinessTransactionGroupResponse();
		response.setListBusinessTransactionGroup(listGroup);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public MssResponseType addBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultBusinessGroupingH businessGroupingH = daoFactory.getBusinessTransactionGroupDao().getBusinessGroupingH(dashboardGroupH, request.getGroupName());
		if (null != businessGroupingH) {
			throw new CommonException(getMessage("businesslogic.global.namealreadyused", new String[] {request.getGroupName()}, audit), ReasonCommon.NAME_ALREADY_USED);
		}
		
		Date currentTime = new Date();
		
		TrConsolidateResultBusinessGroupingH newGroupingH = new TrConsolidateResultBusinessGroupingH();
		newGroupingH.setTrDashboardGroupH(dashboardGroupH);
		newGroupingH.setGroupName(request.getGroupName());
		newGroupingH.setIsUserEdited("1");
		newGroupingH.setIsDeleted("0");
		newGroupingH.setUsrCrt(audit.getCallerId());
		newGroupingH.setDtmCrt(currentTime);
		newGroupingH.setUsrUpd(audit.getCallerId());
		newGroupingH.setDtmUpd(currentTime);
		daoFactory.getBusinessTransactionGroupDao().insertBusinessGroupingH(newGroupingH);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(currentTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setBusinessLastUpdDate(currentTime);
		dashboardLastUpdate.setDtmUpd(currentTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public MssResponseType deleteBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultBusinessGroupingH businessGroupingH = daoFactory.getBusinessTransactionGroupDao().getBusinessGroupingH(dashboardGroupH, request.getGroupName());
		if (null == businessGroupingH) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {request.getGroupName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		Date updateTime = new Date();
		
		if ("1".equals(businessGroupingH.getIsUserEdited())) {
			daoFactory.getBusinessTransactionGroupDao().deleteBusinessGroupingD(businessGroupingH);
			daoFactory.getBusinessTransactionGroupDao().deleteBusinessGroupingH(businessGroupingH);
		} else {
			daoFactory.getBusinessTransactionGroupDao().deleteBusinessGroupingD(businessGroupingH, "1");
			daoFactory.getBusinessTransactionGroupDao().updateBusinessGroupingDIsDeleted(businessGroupingH, "0", "1", audit);
			
			businessGroupingH.setIsUserEdited("1");
			businessGroupingH.setIsDeleted("1");
			businessGroupingH.setUsrUpd(audit.getCallerId());
			businessGroupingH.setDtmUpd(updateTime);
			daoFactory.getBusinessTransactionGroupDao().updateBusinessGroupingH(businessGroupingH);
		}
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setBusinessLastUpdDate(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		dashboardLastUpdate.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public ListBusinessTransactionGroupDetailResponse getListBusinessTransactionGroupDetail(ListNonBusinessTransactionGroupDetailRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultBusinessGroupingH businessGroupingH = businessGroupingValidator.validateGetGroupingHeader(dashboardGroupH, request.getGroupName(), true, audit);
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		List<NonBusinessTransactionGroupDetailBean> result = daoFactory.getBusinessTransactionGroupDao().getListBusinessTransactionGroupDetail(businessGroupingH, min, max);
		long totalData = daoFactory.getBusinessTransactionGroupDao().countListBusinessTransactionGroupDetail(businessGroupingH);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		ListBusinessTransactionGroupDetailResponse response = new ListBusinessTransactionGroupDetailResponse();
		response.setTransactions(result);
		response.setPage(request.getPage());
		response.setTotalResult(totalData);
		response.setTotalPage(totalPage);
		return response;
	}

	@Override
	public MssResponseType addBusinessTransactionGroupDetail(AddNonBusinessTransactionGroupDetailRequest request, AuditContext audit) {
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultBusinessGroupingH businessGroupingH = businessGroupingValidator.validateGetGroupingHeader(dashboardGroupH, request.getGroupName(), true, audit);
		List<TrOcrResultDetail> ocrResultDetails = validateResultDetailIdForInsert(dashboardGroupH, request.getResultDetailIds(), audit);
		
		Date createTime = new Date();
		
		for (TrOcrResultDetail ocrResultDetail : ocrResultDetails) {
			TrConsolidateResultBusinessGroupingD groupingD = new TrConsolidateResultBusinessGroupingD();
			groupingD.setTrConsolidateResultBusinessGroupingH(businessGroupingH);
			groupingD.setTrOcrResultDetail(ocrResultDetail);
			groupingD.setIsUserEdited("1");
			groupingD.setIsDeleted("0");
			groupingD.setUsrCrt(audit.getCallerId());
			groupingD.setDtmCrt(createTime);
			groupingD.setUsrUpd(audit.getCallerId());
			groupingD.setDtmUpd(createTime);
			daoFactory.getBusinessTransactionGroupDao().insertBusinessGroupingD(groupingD);
		}
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(createTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setBusinessLastUpdDate(createTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		dashboardLastUpdate.setDtmUpd(createTime);
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}
	
	private List<TrOcrResultDetail> validateResultDetailIdForInsert(TrDashboardGroupH dashboardGroupH, List<String> resultDetailIds, AuditContext audit) {
		
		List<TrOcrResultDetail> details = new ArrayList<>();
		
		for (String resultDetailId : resultDetailIds) {
			
			TrOcrResultDetail ocrResultDetail = daoFactory.getOcrResultDao().getOcrResultDetail(dashboardGroupH, resultDetailId);
			if (null == ocrResultDetail) {
				throw new CommonException(getMessage("businesslogic.global.objectnotfound2",
						new String[] {"Transaction " + resultDetailId, dashboardGroupH.getDashboardGroupName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
			}
			
			TrConsolidateResultBusinessGroupingD groupingD = daoFactory.getBusinessTransactionGroupDao().getBusinessGroupingD(ocrResultDetail);
			if (null != groupingD) {
				throw new CommonException(getMessage("businesslogic.global.objectalreadysaved",
						new String[] {"Transaction " + resultDetailId, groupingD.getTrConsolidateResultBusinessGroupingH().getGroupName()}, audit), ReasonCommon.DUPLICATE_OBJECT);
			}
			
			details.add(ocrResultDetail);
		}
		
		return details;
	}

	@Override
	public MssResponseType deleteBusinessTransactionGroupDetail(DeleteNonBusinessTransactionGroupDetailRequest request, AuditContext audit) {
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultBusinessGroupingH businessGroupingH = businessGroupingValidator.validateGetGroupingHeader(dashboardGroupH, request.getGroupName(), true, audit);
		TrConsolidateResultBusinessGroupingD businessGroupingD = businessGroupingValidator.validateGetGroupingDetail(businessGroupingH, request.getDetailId(), true, audit);
		
		Date updateTime = new Date();
		
		if ("1".equals(businessGroupingD.getIsUserEdited())) {
			daoFactory.getBusinessTransactionGroupDao().deleteBusinessGroupingD(businessGroupingD);
		} else {
			businessGroupingD.setIsUserEdited("1");
			businessGroupingD.setIsDeleted("1");
			businessGroupingD.setUsrUpd(audit.getCallerId());
			businessGroupingD.setDtmUpd(updateTime);
			daoFactory.getBusinessTransactionGroupDao().updateBusinessGroupingD(businessGroupingD);
		}
		
		businessGroupingH.setDtmUpd(updateTime);
		businessGroupingH.setUsrUpd(audit.getCallerId());
		daoFactory.getBusinessTransactionGroupDao().updateBusinessGroupingH(businessGroupingH);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setBusinessLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

}
