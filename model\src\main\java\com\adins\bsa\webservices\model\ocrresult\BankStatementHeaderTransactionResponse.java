package com.adins.bsa.webservices.model.ocrresult;

import java.io.Serializable;
import java.util.List;

import com.adins.bsa.custom.BankStatementHeaderInfoBean;
import com.adins.framework.service.base.model.MssResponseType; 

public class BankStatementHeaderTransactionResponse extends MssResponseType implements Serializable {  
	private static final long serialVersionUID = 1L;
	private String accountName;
	private String accountNo;  
	private String bankOffice;
	private String currency;
	private String address; 
	private String isEdited; 
	private List<BankStatementHeaderInfoBean> accountNameInfo;
	private List<BankStatementHeaderInfoBean> accountNoInfo;
	private List<BankStatementHeaderInfoBean> bankOfficeInfo;
	private List<BankStatementHeaderInfoBean> currencyInfo;
	private List<BankStatementHeaderInfoBean> addressInfo;
	
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	} 
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public String getBankOffice() {
		return bankOffice;
	}
	public void setBankOffice(String bankOffice) {
		this.bankOffice = bankOffice;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	
	public List<BankStatementHeaderInfoBean> getAccountNameInfo() {
		return accountNameInfo;
	}
	public void setAccountNameInfo(List<BankStatementHeaderInfoBean> accountNameInfo) {
		this.accountNameInfo = accountNameInfo;
	}
	public List<BankStatementHeaderInfoBean> getAccountNoInfo() {
		return accountNoInfo;
	}
	public void setAccountNoInfo(List<BankStatementHeaderInfoBean> accountNoInfo) {
		this.accountNoInfo = accountNoInfo;
	}
	public List<BankStatementHeaderInfoBean> getBankOfficeInfo() {
		return bankOfficeInfo;
	}
	public void setBankOfficeInfo(List<BankStatementHeaderInfoBean> bankOfficeInfo) {
		this.bankOfficeInfo = bankOfficeInfo;
	}
	public List<BankStatementHeaderInfoBean> getCurrencyInfo() {
		return currencyInfo;
	}
	public void setCurrencyInfo(List<BankStatementHeaderInfoBean> currencyInfo) {
		this.currencyInfo = currencyInfo;
	}
	public List<BankStatementHeaderInfoBean> getAddressInfo() {
		return addressInfo;
	}
	public void setAddressInfo(List<BankStatementHeaderInfoBean> addressInfo) {
		this.addressInfo = addressInfo;
	}
	public String getIsEdited() {
		return isEdited;
	}
	public void setIsEdited(String isEdited) {
		this.isEdited = isEdited;
	}  
}
