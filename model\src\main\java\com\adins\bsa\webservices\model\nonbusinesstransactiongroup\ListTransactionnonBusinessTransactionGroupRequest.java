package com.adins.bsa.webservices.model.nonbusinesstransactiongroup;

import com.adins.bsa.annotations.StringValue;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.bsa.annotations.enums.ValueType;
import com.adins.framework.service.base.model.MssRequestType;

public class ListTransactionnonBusinessTransactionGroupRequest extends MssRequestType {
	private static final long serialVersionUID = 1L;
	private String dashboardName;
	private String tenantCode;
	private String groupName;
	private String category;
	private String accountNo;
	private String type;
	private String period;
	private String description;
	private String formSource;
	
	@ValidationObjectName("Amount More Than")
	@StringValue(ValueType.NUMERIC)
	private String amountMoreThan;
	
	@ValidationObjectName("Amount Less Than")
	@StringValue(ValueType.NUMERIC)
	private String amountLessThan;

	public String getDashboardName() {
		return dashboardName;
	}

	public void setDashboardName(String dashboardName) {
		this.dashboardName = dashboardName;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getPeriod() {
		return period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getFormSource() {
		return formSource;
	}

	public void setFormSource(String formSource) {
		this.formSource = formSource;
	}

	public String getAmountMoreThan() {
		return amountMoreThan;
	}

	public void setAmountMoreThan(String amountMoreThan) {
		this.amountMoreThan = amountMoreThan;
	}

	public String getAmountLessThan() {
		return amountLessThan;
	}

	public void setAmountLessThan(String amountLessThan) {
		this.amountLessThan = amountLessThan;
	}
	
}
