package com.adins.bsa.webservices.model.ocrresult;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class SaveBankStatementHeaderRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("File source path")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 256)
	private String fileSourcePath;
	
	@ValidationObjectName("Account name")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 256)
	private String accountName;
	
	@ValidationObjectName("Account no")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 64)
	private String accountNo;
	
	@ValidationObjectName("Bank office")
	@StringLength(maxValue = 64)
	private String bankOffice;
	
	@ValidationObjectName("Currency")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 32)
	private String currency;
	
	@ValidationObjectName("Address")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 256)
	private String address;
	
	public String getFileSourcePath() {
		return fileSourcePath;
	}
	public void setFileSourcePath(String fileSourcePath) {
		this.fileSourcePath = fileSourcePath;
	}
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public String getBankOffice() {
		return bankOffice;
	}
	public void setBankOffice(String bankOffice) {
		this.bankOffice = bankOffice;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}

}
