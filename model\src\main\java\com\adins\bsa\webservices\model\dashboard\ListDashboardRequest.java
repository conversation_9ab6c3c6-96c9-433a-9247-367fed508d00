package com.adins.bsa.webservices.model.dashboard;

public class ListDashboardRe<PERSON> extends GenericDashboardPagingRequest {
	
	private static final long serialVersionUID = 1L;
	private String filterCurrentUser;
	private String uploadDateStart;
	private String uploadDateEnd;
	
	public String getFilterCurrentUser() {
		return filterCurrentUser;
	}
	public void setFilterCurrentUser(String filterCurrentUser) {
		this.filterCurrentUser = filterCurrentUser;
	}
	public String getUploadDateStart() {
		return uploadDateStart;
	}
	public void setUploadDateStart(String uploadDateStart) {
		this.uploadDateStart = uploadDateStart;
	}
	public String getUploadDateEnd() {
		return uploadDateEnd;
	}
	public void setUploadDateEnd(String uploadDateEnd) {
		this.uploadDateEnd = uploadDateEnd;
	}
	
}
