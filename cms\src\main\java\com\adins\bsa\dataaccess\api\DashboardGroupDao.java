package com.adins.bsa.dataaccess.api;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.adins.bsa.custom.DashboardAccountNoBean;
import com.adins.bsa.custom.DashboardBankBean;
import com.adins.bsa.custom.DashboardBean;
import com.adins.bsa.custom.DashboardPeriodBean;
import com.adins.bsa.custom.OcrResultBean;
import com.adins.bsa.custom.SupplierBuyerCashFlowDetailBean;
import com.adins.bsa.custom.queryfilter.ListDashboardFilter;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupDTemp;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;

public interface DashboardGroupDao {
	
	// tr_dashboard_group_h
	
	void insertDashboardH(TrDashboardGroupH dashboardGroupH);
	void insertDashboardHNewTrx(TrDashboardGroupH dashboardGroupH);
	void updateDashboardH(TrDashboardGroupH dashboardGroupH);
	void updateDashboardHNewTrx(TrDashboardGroupH dashboardGroupH);
	
	TrDashboardGroupH getDashboardGroupH(String dashboardName, MsTenant tenant);
	TrDashboardGroupH getDashboardGroupHNewTrx(String dashboardName, MsTenant tenant);
	
	List<DashboardBean> getListDashboard(ListDashboardFilter filter);
	long countListDashboard(ListDashboardFilter filter);
	
	// tr_dashboard_group_d
	void insertDashboardD(TrDashboardGroupD dashboardGroupD);
	void insertDashboardDNewTrx(TrDashboardGroupD dashboardGroupD);
	
	void updateDashboardD(TrDashboardGroupD dashboardGroupD);
	void updateDashboardDNewTrx(TrDashboardGroupD dashboardGroupD);
	
	long countDashboardGroupD(TrDashboardGroupH dashboardGroupH, MsLov lovProcessStatus);
	long countDashboardGroupDWithHighRedPercentage(TrDashboardGroupH dashboardGroupH);
	
	TrDashboardGroupD getDashboardGroupDByFileSourcePath(String dashboardName, String fileSourcePath);
	TrDashboardGroupD getDashboardGroupD(TrDashboardGroupH dashboardGroupH, String fileSourcePath);
	TrDashboardGroupD getDashboardGroupDNewTrx(TrDashboardGroupH dashboardGroupH, String fileSourcePath);
	TrDashboardGroupD getDashboardGroupD(MsTenant tenant, String fileSourcePath);
	TrDashboardGroupD getDashboardGroupDByFilename(TrDashboardGroupH dashboardGroupH, String filename);
	
	List<OcrResultBean> getConsolidatedBankStatements(TrDashboardGroupH dashboardGroupH);
	
	// tr_dashboard_group_d_temp
	void insertDashboardDTempNewTrx(TrDashboardGroupDTemp dashboardGroupDTemp);

	// tr_dashboard_group_last_update_date
	void insertDashboardGroupLastUpdateDate(TrDashboardGroupLastUpdateDate dashboardGroupLastUpdateDate);
	void insertDashboardGroupLastUpdateDateNewTrx(TrDashboardGroupLastUpdateDate dashboardGroupLastUpdateDate);
	void updateDashboardGroupLastUpdateDate(TrDashboardGroupLastUpdateDate dashboardGroupLastUpdateDate);
	void updateDashboardGroupLastUpdateDateNewTrx(TrDashboardGroupLastUpdateDate dashboardGroupLastUpdateDate);
	
	TrDashboardGroupLastUpdateDate getDashboardLastUpdate(TrDashboardGroupH dashboardGroupH);
	TrDashboardGroupLastUpdateDate getDashboardLastUpdateNewTrx(TrDashboardGroupH dashboardGroupH);
	
	// Insights - Supplier Buyer - tr_consolidate_result_dashboard_overall
	Object[] getSupplierBuyerHeaderData(TrDashboardGroupH dashboardGroupH);
	List<Map<String, Object>> getSupplierBuyerCashFlowAndGrowthRate(TrDashboardGroupH dashboardGroupH);
	
	// Insights - Supplier Buyer - Gross Profit Margin, Working Capital Ratio, Liquidity Ratio
	List<Map<String, Object>> getSupplierBuyerGpmWcrLr(TrDashboardGroupH dashboardGroupH);
	
	// Insights - Supplier Buyer - Top 5 Supplier & Buyer
	List<Map<String, Object>> getTopFiveSupplier(TrDashboardGroupH dashboardGroupH);
	List<Map<String, Object>> getTopFiveBuyer(TrDashboardGroupH dashboardGroupH);
	
	List<SupplierBuyerCashFlowDetailBean> getSupplierBuyerCashFlowDetails(TrDashboardGroupH dashboardGroupH);
	
	// Insights - General
	Object[] getGeneralInsightsHeaderData(TrDashboardGroupH dashboardGroupH);
	List<Map<String, Object>> getGeneralInsightCashFlowAnalysis(TrDashboardGroupH dashboardGroupH);
	Object[] getGeneralBodyCardData(TrDashboardGroupH dashboardGroupH);
	List<Map<String, Object>> getGeneralDailyAnalysisData(TrDashboardGroupH dashboardGroupH);
	Object[] getGeneralInsightsCircularHeaderData(TrDashboardGroupH dashboardGroupH);
	List<Map<String, Object>> getGeneralInsightsCircularChartData(TrDashboardGroupH dashboardGroupH);
	BigDecimal getDashboardGreatestCircularRatio(TrDashboardGroupH dashboardGroupH);
	
	// List for dropdown based on dashboard
	List<DashboardBankBean> getDashboardBankList(TrDashboardGroupH dashboardGroupH);
	List<DashboardAccountNoBean> getDashboardAccountNoList(TrDashboardGroupH dashboardGroupH);
	List<DashboardPeriodBean> getDashboardPeriodList(TrDashboardGroupH dashboardGroupH, String accountNo);
	
	// For load test data setup
	List<Map<String, Object>> getLatestDashboardNames(MsTenant tenant, String isActive, int limitData);
	List<Map<String, Object>> getLatestDeletableBankStatements(MsTenant tenant, int limitData);
	void flagDashboardGroupAsCompleted(MsTenant tenant, String isActive, int limitData);
	
	
}
