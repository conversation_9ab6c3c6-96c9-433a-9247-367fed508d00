package com.adins.bsa.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "tr_ocr_result_detail")
public class TrOcrResultDetail extends UpdateableEntity {
	
	public static final String PARAM_RESULT_DETAIL_ID = "resultDetailId";
	
	private long idOcrResultDetail;
	private TrDashboardGroupD trDashboardGroupD;
	private TrOcrResultHeader trOcrResultHeader;
	private String resultDetailId;
	
	private Date transactionDate;
	private Double transactionDateConfidence;
	private String transactionDateBoxLocation;
	private Integer transactionDateBoxPage;
	
	private BigDecimal transactionAmount;
	private Double transactionAmountConfidence;
	private String transactionAmountBoxLocation;
	private Integer transactionAmountBoxPage;
	
	private String transactionDesc;
	private Double transactionDescConfidence;
	private String transactionDescBoxLocation;
	private Integer transactionDescBoxPage;
	
	private BigDecimal transactionEndingBalance;
	private Double transactionEndingBalanceConfidence;
	private String transactionEndingBalaceBoxLocation;
	private Integer transactionEndingBalanceBoxPage;
	
	private String transactionType;
	private Double transactionTypeConfidence;
	private String transactionTypeBoxLocation;	
	private Integer transactionTypeBoxPage;
	
	private String isEdited;
	private String isDeleted;
	private String isDeleteOverlap;
	private MsLov lovCategory;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ocr_result_detail", unique = true, nullable = false)
	public long getIdOcrResultDetail() {
		return idOcrResultDetail;
	}
	
	public void setIdOcrResultDetail(long idOcrResultDetail) {
		this.idOcrResultDetail = idOcrResultDetail;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_d", nullable = false)
	public TrDashboardGroupD getTrDashboardGroupD() {
		return trDashboardGroupD;
	}
	
	public void setTrDashboardGroupD(TrDashboardGroupD trDashboardGroupD) {
		this.trDashboardGroupD = trDashboardGroupD;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ocr_result_header", nullable = false)
	public TrOcrResultHeader getTrOcrResultHeader() {
		return trOcrResultHeader;
	}
	
	public void setTrOcrResultHeader(TrOcrResultHeader trOcrResultHeader) {
		this.trOcrResultHeader = trOcrResultHeader;
	}
	
	@Column(name = "result_detail_id", length = 64)
	public String getResultDetailId() {
		return resultDetailId;
	}
	
	public void setResultDetailId(String resultDetailId) {
		this.resultDetailId = resultDetailId;
	}
	
	@Column(name = "transaction_date")
	public Date getTransactionDate() {
		return transactionDate;
	}
	
	public void setTransactionDate(Date transactionDate) {
		this.transactionDate = transactionDate;
	}
	
	@Column(name = "transaction_date_confidence")
	public Double getTransactionDateConfidence() {
		return transactionDateConfidence;
	}
	
	public void setTransactionDateConfidence(Double transactionDateConfidence) {
		this.transactionDateConfidence = transactionDateConfidence;
	}
	
	@Column(name = "transaction_date_box_location", length = 64)
	public String getTransactionDateBoxLocation() {
		return transactionDateBoxLocation;
	}
	
	public void setTransactionDateBoxLocation(String transactionDateBoxLocation) {
		this.transactionDateBoxLocation = transactionDateBoxLocation;
	}
	
	@Column(name = "transaction_date_box_page")
	public Integer getTransactionDateBoxPage() {
		return transactionDateBoxPage;
	}
	
	public void setTransactionDateBoxPage(Integer transactionDateBoxPage) {
		this.transactionDateBoxPage = transactionDateBoxPage;
	}
	
	@Column(name = "transaction_amount")
	public BigDecimal getTransactionAmount() {
		return transactionAmount;
	}
	
	public void setTransactionAmount(BigDecimal transactionAmount) {
		this.transactionAmount = transactionAmount;
	}
	
	@Column(name = "transaction_amount_confidence")
	public Double getTransactionAmountConfidence() {
		return transactionAmountConfidence;
	}
	
	public void setTransactionAmountConfidence(Double transactionAmountConfidence) {
		this.transactionAmountConfidence = transactionAmountConfidence;
	}
	
	@Column(name = "transaction_amount_box_location", length = 64)
	public String getTransactionAmountBoxLocation() {
		return transactionAmountBoxLocation;
	}
	
	public void setTransactionAmountBoxLocation(String transactionAmountBoxLocation) {
		this.transactionAmountBoxLocation = transactionAmountBoxLocation;
	}
	
	@Column(name = "transaction_amount_box_page")
	public Integer getTransactionAmountBoxPage() {
		return transactionAmountBoxPage;
	}
	
	public void setTransactionAmountBoxPage(Integer transactionAmountBoxPage) {
		this.transactionAmountBoxPage = transactionAmountBoxPage;
	}
	
	@Column(name = "transaction_desc", length = 64)
	public String getTransactionDesc() {
		return transactionDesc;
	}
	
	public void setTransactionDesc(String transactionDesc) {
		this.transactionDesc = transactionDesc;
	}
	
	@Column(name = "transaction_desc_confidence")
	public Double getTransactionDescConfidence() {
		return transactionDescConfidence;
	}
	
	public void setTransactionDescConfidence(Double transactionDescConfidence) {
		this.transactionDescConfidence = transactionDescConfidence;
	}
	
	@Column(name = "transaction_desc_box_location", length = 64)
	public String getTransactionDescBoxLocation() {
		return transactionDescBoxLocation;
	}
	
	public void setTransactionDescBoxLocation(String transactionDescBoxLocation) {
		this.transactionDescBoxLocation = transactionDescBoxLocation;
	}
	
	@Column(name = "transaction_desc_box_page")
	public Integer getTransactionDescBoxPage() {
		return transactionDescBoxPage;
	}
	
	public void setTransactionDescBoxPage(Integer transactionDescBoxPage) {
		this.transactionDescBoxPage = transactionDescBoxPage;
	}
	
	@Column(name = "transaction_ending_balance")
	public BigDecimal getTransactionEndingBalance() {
		return transactionEndingBalance;
	}
	
	public void setTransactionEndingBalance(BigDecimal transactionEndingBalance) {
		this.transactionEndingBalance = transactionEndingBalance;
	}
	
	@Column(name = "transaction_ending_balance_confidence")
	public Double getTransactionEndingBalanceConfidence() {
		return transactionEndingBalanceConfidence;
	}
	
	public void setTransactionEndingBalanceConfidence(Double transactionEndingBalanceConfidence) {
		this.transactionEndingBalanceConfidence = transactionEndingBalanceConfidence;
	}
	
	@Column(name = "transaction_ending_balace_box_location")
	public String getTransactionEndingBalaceBoxLocation() {
		return transactionEndingBalaceBoxLocation;
	}
	
	public void setTransactionEndingBalaceBoxLocation(String transactionEndingBalaceBoxLocation) {
		this.transactionEndingBalaceBoxLocation = transactionEndingBalaceBoxLocation;
	}
	
	@Column(name = "transaction_ending_balance_box_page")
	public Integer getTransactionEndingBalanceBoxPage() {
		return transactionEndingBalanceBoxPage;
	}
	
	public void setTransactionEndingBalanceBoxPage(Integer transactionEndingBalanceBoxPage) {
		this.transactionEndingBalanceBoxPage = transactionEndingBalanceBoxPage;
	}
	
	@Column(name = "transaction_type", length = 16)
	public String getTransactionType() {
		return transactionType;
	}
	
	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}
	
	@Column(name = "transaction_type_confidence")
	public Double getTransactionTypeConfidence() {
		return transactionTypeConfidence;
	}
	
	public void setTransactionTypeConfidence(Double transactionTypeConfidence) {
		this.transactionTypeConfidence = transactionTypeConfidence;
	}
	
	@Column(name = "transaction_type_box_location", length = 64)
	public String getTransactionTypeBoxLocation() {
		return transactionTypeBoxLocation;
	}
	
	public void setTransactionTypeBoxLocation(String transactionTypeBoxLocation) {
		this.transactionTypeBoxLocation = transactionTypeBoxLocation;
	}
	
	@Column(name = "transaction_type_box_page")
	public Integer getTransactionTypeBoxPage() {
		return transactionTypeBoxPage;
	}
	
	public void setTransactionTypeBoxPage(Integer transactionTypeBoxPage) {
		this.transactionTypeBoxPage = transactionTypeBoxPage;
	}
	
	@Column(name = "is_edited", length = 1)
	public String getIsEdited() {
		return isEdited;
	}
	
	public void setIsEdited(String isEdited) {
		this.isEdited = isEdited;
	}
	
	@Column(name = "is_deleted", length = 1)
	public String getIsDeleted() {
		return isDeleted;
	}
	
	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}
	
	@Column(name = "is_delete_overlap", length = 1)
	public String getIsDeleteOverlap() {
		return isDeleteOverlap;
	}
	
	public void setIsDeleteOverlap(String isDeleteOverlap) {
		this.isDeleteOverlap = isDeleteOverlap;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_category", nullable = false)
	public MsLov getLovCategory() {
		return lovCategory;
	}
	public void setLovCategory(MsLov lovCategory) {
		this.lovCategory = lovCategory;
	}
	
}
