package com.adins.bsa.validator.impl;

import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.enums.DashboardAction;
import com.adins.bsa.constants.enums.DashboardFeature;
import com.adins.bsa.model.MsDashboardTab;
import com.adins.bsa.model.MsDashboardTabOfRole;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.MsUseroftenant;
import com.adins.bsa.validator.api.RoleValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericRoleValidator extends BaseLogic implements RoleValidator {

    @Override
    public void validateFeaturePermission(<PERSON>Ms<PERSON> user, MsTenant tenant, String roleCode, DashboardFeature feature, DashboardAction action, AuditContext audit) {
        
        if (null == action) {
            return;
        }

        AmMsrole role = daoFactory.getRoleDao().getRole(tenant, roleCode);
        if (null == role || !"1".equals(role.getIsActive()) || "1".equals(role.getIsDeleted())) {
            throw new CommonException(getMessage(GlobalKey.MSG_COMMON_FORBIDDEN, null, audit), ReasonCommon.FORBIDDEN_ACCESS);
        }

        MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUseroftenantReadOnly(user, tenant);
        if (null == useroftenant) {
            throw new CommonException(getMessage(GlobalKey.MSG_COMMON_FORBIDDEN, null, audit), ReasonCommon.FORBIDDEN_ACCESS);
        }

        boolean canAddDashboard = "1".equals(role.getCreateDashboardAccess());
        if (DashboardFeature.DASHBOARD == feature && DashboardAction.ADD == action && !canAddDashboard) {
            throw new CommonException(getMessage(GlobalKey.MSG_COMMON_FORBIDDEN, null, audit), ReasonCommon.FORBIDDEN_ACCESS);
        }

        boolean canConsolidate = "1".equals(role.getConsolidateDashboardAccess());
        if (DashboardFeature.CONSOLIDATE == feature && DashboardAction.ADD == action && !canConsolidate) {
            throw new CommonException(getMessage(GlobalKey.MSG_COMMON_FORBIDDEN, null, audit), ReasonCommon.FORBIDDEN_ACCESS);
        }

        MsDashboardTab dashboardTab = daoFactory.getDashboardTabDao().getDashboardTab(feature.toString());
        MsDashboardTabOfRole dashboardTabOfRole = daoFactory.getDashboardTabDao().getDashboardTabOfRoleReadOnly(role, dashboardTab);
        if (null == dashboardTabOfRole) {
            throw new CommonException(getMessage(GlobalKey.MSG_COMMON_FORBIDDEN, null, audit), ReasonCommon.FORBIDDEN_ACCESS);
        }

        boolean canAdd = "1".equals(dashboardTabOfRole.getAddBankStatementDetailAccess());
        if (DashboardAction.ADD == action && !canAdd) {
            throw new CommonException(getMessage(GlobalKey.MSG_COMMON_FORBIDDEN, null, audit), ReasonCommon.FORBIDDEN_ACCESS);
        }
        
        boolean canEdit = "1".equals(dashboardTabOfRole.getEditBankStatementDetailAccess());
        if (DashboardAction.EDIT == action && !canEdit) {
            throw new CommonException(getMessage(GlobalKey.MSG_COMMON_FORBIDDEN, null, audit), ReasonCommon.FORBIDDEN_ACCESS);
        }

        boolean canDelete = "1".equals(dashboardTabOfRole.getDeleteBankStatementDetailAccess());
        if (DashboardAction.DELETE == action && !canDelete) {
            throw new CommonException(getMessage(GlobalKey.MSG_COMMON_FORBIDDEN, null, audit), ReasonCommon.FORBIDDEN_ACCESS);
        }

    }
    
}
