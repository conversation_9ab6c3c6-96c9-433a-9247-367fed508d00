package com.adins.bsa.webservices.model.insights;

import com.adins.framework.service.base.model.MssResponseType;

public class InsightsInformationResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private String dashboardName;
	private String createdBy;
	private String lastUpdate;
	private String lastConsolidate;
	private String isDataChangesAfterConsolidate;
	private String isEditable;
	
	public String getDashboardName() {
		return dashboardName;
	}
	public void setDashboardName(String dashboardName) {
		this.dashboardName = dashboardName;
	}
	public String getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}
	public String getLastUpdate() {
		return lastUpdate;
	}
	public void setLastUpdate(String lastUpdate) {
		this.lastUpdate = lastUpdate;
	}
	public String getLastConsolidate() {
		return lastConsolidate;
	}
	public void setLastConsolidate(String lastConsolidate) {
		this.lastConsolidate = lastConsolidate;
	}
	public String getIsDataChangesAfterConsolidate() {
		return isDataChangesAfterConsolidate;
	}
	public void setIsDataChangesAfterConsolidate(String isDataChangesAfterConsolidate) {
		this.isDataChangesAfterConsolidate = isDataChangesAfterConsolidate;
	}
	public String getIsEditable() {
		return isEditable;
	}
	public void setIsEditable(String isEditable) {
		this.isEditable = isEditable;
	}
	
}
