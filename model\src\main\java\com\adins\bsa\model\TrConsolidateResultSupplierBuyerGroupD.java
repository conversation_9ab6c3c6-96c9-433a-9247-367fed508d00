package com.adins.bsa.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "tr_consolidate_result_supplier_buyer_group_d")
public class TrConsolidateResultSupplierBuyerGroupD extends UpdateableEntity implements Serializable{
	
	public static final String PARAM_ID_GROUP_D = "idGroupD";
	
	private static final long serialVersionUID = 1L;
	private long idConsolidateResultNonbusinessGroupingD;
	private TrConsolidateResultSupplierBuyerGroupH trConsolidateResultSupplierBuyerGroupH;
	private TrDashboardGroupH trDashboardGroupH;
	private String subGroupName;
	private String isUserEdited;
	private String isDeleted;
	
	public TrConsolidateResultSupplierBuyerGroupD() {}
	
	public TrConsolidateResultSupplierBuyerGroupD(long idConsolidateResultNonbusinessGroupingD,
			TrConsolidateResultSupplierBuyerGroupH trConsolidateResultSupplierBuyerGroupH,
			TrDashboardGroupH trDashboardGroupH, String subGroupName, String isUserEdited, String isDeleted) {
		super();
		this.idConsolidateResultNonbusinessGroupingD = idConsolidateResultNonbusinessGroupingD;
		this.trConsolidateResultSupplierBuyerGroupH = trConsolidateResultSupplierBuyerGroupH;
		this.trDashboardGroupH = trDashboardGroupH;
		this.subGroupName = subGroupName;
		this.isUserEdited = isUserEdited;
		this.isDeleted = isDeleted;
	}


	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_consolidate_result_supplier_buyer_group_d", unique = true, nullable = false)
	public long getIdConsolidateResultNonbusinessGroupingD() {
		return idConsolidateResultNonbusinessGroupingD;
	}

	public void setIdConsolidateResultNonbusinessGroupingD(long idConsolidateResultNonbusinessGroupingD) {
		this.idConsolidateResultNonbusinessGroupingD = idConsolidateResultNonbusinessGroupingD;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_consolidate_result_supplier_buyer_group_h", nullable = true)
	public TrConsolidateResultSupplierBuyerGroupH getTrConsolidateResultSupplierBuyerGroupH() {
		return trConsolidateResultSupplierBuyerGroupH;
	}

	public void setTrConsolidateResultSupplierBuyerGroupH(
			TrConsolidateResultSupplierBuyerGroupH trConsolidateResultSupplierBuyerGroupH) {
		this.trConsolidateResultSupplierBuyerGroupH = trConsolidateResultSupplierBuyerGroupH;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_h", nullable = false)
	public TrDashboardGroupH getTrDashboardGroupH() {
		return trDashboardGroupH;
	}

	public void setTrDashboardGroupH(TrDashboardGroupH trDashboardGroupH) {
		this.trDashboardGroupH = trDashboardGroupH;
	}

	@Column(name = "sub_group_name", length = 64)
	public String getSubGroupName() {
		return subGroupName;
	}

	public void setSubGroupName(String subGroupName) {
		this.subGroupName = subGroupName;
	}

	@Column(name = "is_user_edited", length = 1)
	public String getIsUserEdited() {
		return isUserEdited;
	}

	public void setIsUserEdited(String isUserEdited) {
		this.isUserEdited = isUserEdited;
	}

	@Column(name = "is_deleted", length = 1)
	public String getIsDeleted() {
		return isDeleted;
	}
	
	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}
	
}
