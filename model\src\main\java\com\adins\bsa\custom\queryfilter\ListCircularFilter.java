package com.adins.bsa.custom.queryfilter;

import com.adins.bsa.model.TrDashboardGroupH;

public class ListCircularFilter {
	
	private TrDashboardGroupH dashboardGroupH;
	private String accountNo;
	private Integer transactionWindow;
	private int min;
	private int max;
	
	public TrDashboardGroupH getDashboardGroupH() {
		return dashboardGroupH;
	}
	public void setDashboardGroupH(TrDashboardGroupH dashboardGroupH) {
		this.dashboardGroupH = dashboardGroupH;
	}
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public Integer getTransactionWindow() {
		return transactionWindow;
	}
	public void setTransactionWindow(Integer transactionWindow) {
		this.transactionWindow = transactionWindow;
	}
	public int getMin() {
		return min;
	}
	public void setMin(int min) {
		this.min = min;
	}
	public int getMax() {
		return max;
	}
	public void setMax(int max) {
		this.max = max;
	}
	
}
