package com.adins.bsa.model;
// Generated 09-Sep-2021 22:49:32 by Hibernate Tools 5.2.12.Final

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.UpdateableEntity;

/**
 * MsUseroftenant generated by hbm2java
 */
@Entity
@Table(name = "ms_useroftenant")
public class MsUseroftenant extends UpdateableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	private long idMsUseroftenant;
	private AmMsuser amMsuser;
	private MsTenant msTenant;
	private String canViewTenantDocument;

	public MsUseroftenant() {
	}

	public MsUseroftenant(long idMsUseroftenant, AmMsuser amMsuser, MsTenant msTenant, String usrCrt, Date dtmCrt) {
		this.idMsUseroftenant = idMsUseroftenant;
		this.amMsuser = amMsuser;
		this.msTenant = msTenant;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
	}

	public MsUseroftenant(long idMsUseroftenant, AmMsuser amMsuser, MsTenant msTenant, String usrCrt, Date dtmCrt,
			String usrUpd, Date dtmUpd) {
		this.idMsUseroftenant = idMsUseroftenant;
		this.amMsuser = amMsuser;
		this.msTenant = msTenant;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_useroftenant", unique = true, nullable = false)
	public long getIdMsUseroftenant() {
		return this.idMsUseroftenant;
	}

	public void setIdMsUseroftenant(long idMsUseroftenant) {
		this.idMsUseroftenant = idMsUseroftenant;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@Column(name = "can_view_tenant_document", length = 1)
	public String getCanViewTenantDocument() {
		return canViewTenantDocument;
	}

	public void setCanViewTenantDocument(String canViewTenantDocument) {
		this.canViewTenantDocument = canViewTenantDocument;
	}
	
	

}
