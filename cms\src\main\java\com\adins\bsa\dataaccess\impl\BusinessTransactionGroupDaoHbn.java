package com.adins.bsa.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.NonBusinessTransactionGroupBean;
import com.adins.bsa.custom.NonBusinessTransactionGroupDetailBean;
import com.adins.bsa.dataaccess.api.BusinessTransactionGroupDao;
import com.adins.bsa.model.TrConsolidateResultBusinessGroupingD;
import com.adins.bsa.model.TrConsolidateResultBusinessGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
@Transactional
public class BusinessTransactionGroupDaoHbn extends BaseDaoHbn implements BusinessTransactionGroupDao {

	@Override
	@Transactional(readOnly = true)
	public List<NonBusinessTransactionGroupBean> getListBusinessTransactionGroup(TrDashboardGroupH dashboardGroupH, int min, int max) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		params.put(GlobalVal.QUERY_PARAM_MIN, min);
		params.put(GlobalVal.QUERY_PARAM_MAX, max);
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by bgh.dtm_upd desc) as \"rowNum\", ")
				.append("coalesce(bgh.group_name_edited, bgh.group_name) as \"groupName\", ")
				.append("to_char(bgh.dtm_crt, 'DD/MM/YYYY HH24:MI') as \"createdDate\", ")
				.append("coalesce(to_char(bgh.dtm_upd, 'DD/MM/YYYY HH24:MI'), '-')  as \"lastUpdated\" ")
				.append("from tr_consolidate_result_business_grouping_h bgh ")
				.append("where 1=1 ")
				.append("and bgh.id_dashboard_group_h = :idDashboardGroupH ")
				.append("and is_deleted = '0' ")
			.append(") ")
			.append("select * ")
			.append("from cte ")
			.append("where \"rowNum\" between :min and :max ");
		
		return managerDAO.selectForListString(NonBusinessTransactionGroupBean.class, query.toString(), params, null);
	}

	@Override
	@Transactional(readOnly = true)
	public long countListBusinessTransactionGroup(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_consolidate_result_business_grouping_h bgh ")
			.append("where 1=1 ")
			.append("and bgh.id_dashboard_group_h = :idDashboardGroupH ")
			.append("and bgh.is_deleted = '0' ");
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		
		return result.longValue();
	}

	@Override
	public void insertBusinessGroupingH(TrConsolidateResultBusinessGroupingH businessGroupingH) {
		managerDAO.insert(businessGroupingH);
	}

	@Override
	public TrConsolidateResultBusinessGroupingH getBusinessGroupingH(TrDashboardGroupH dashboardGroupH, String groupName) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_DASHBOARD_GROUP_H, dashboardGroupH);
		params.put("groupName", groupName);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultBusinessGroupingH bgh "
				+ "where bgh.trDashboardGroupH = :dashboardGroupH "
				+ "and bgh.groupName = :groupName "
				+ "and bgh.isDeleted = '0' ", params);
	}

	@Override
	public void deleteBusinessGroupingH(TrConsolidateResultBusinessGroupingH businessGroupingH) {
		managerDAO.delete(businessGroupingH);
	}

	@Override
	public void deleteBusinessGroupingD(TrConsolidateResultBusinessGroupingH businessGroupingH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultBusinessGroupingH.PARAM_ID_BUSINESS_GROUPING_H, businessGroupingH.getIdConsolidateResultBusinessGroupingH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("delete from tr_consolidate_result_business_grouping_d ")
			.append("where id_consolidate_result_business_grouping_h = :idBusinessGroupingH ");
		
		managerDAO.deleteNativeString(query.toString(), params);
	}

	@Override
	public void deleteBusinessGroupingD(TrConsolidateResultBusinessGroupingH businessGroupingH, String isUserEdited) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultBusinessGroupingH.PARAM_ID_BUSINESS_GROUPING_H, businessGroupingH.getIdConsolidateResultBusinessGroupingH());
		params.put("isUserEdited", isUserEdited);
		
		StringBuilder query = new StringBuilder();
		query
			.append("delete from tr_consolidate_result_business_grouping_d ")
			.append("where id_consolidate_result_business_grouping_h = :idBusinessGroupingH ")
			.append("and is_user_edited = :isUserEdited ");
		
		managerDAO.deleteNativeString(query.toString(), params);
	}

	@Override
	public void updateBusinessGroupingDIsDeleted(TrConsolidateResultBusinessGroupingH businessGroupingH, String isUserEdited, String isDeleted, AuditContext audit) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultBusinessGroupingH.PARAM_ID_BUSINESS_GROUPING_H, businessGroupingH.getIdConsolidateResultBusinessGroupingH());
		params.put("isUserEdited", isUserEdited);
		params.put("isDeleted", isDeleted);
		params.put("usrUpd", audit.getCallerId());
		
		StringBuilder query = new StringBuilder();
		query
			.append("update tr_consolidate_result_business_grouping_d ")
			.append("set is_deleted = :isDeleted , ")
			.append("is_user_edited = '1', ")
			.append("usr_upd = :usrUpd , ")
			.append("dtm_upd = now() ")
			.append("where id_consolidate_result_business_grouping_h = :idBusinessGroupingH ")
			.append("and is_user_edited = :isUserEdited ");
		
		managerDAO.updateNativeString(query.toString(), params);
	}

	@Override
	public void updateBusinessGroupingH(TrConsolidateResultBusinessGroupingH businessGroupingH) {
		managerDAO.update(businessGroupingH);
	}

	@Override
	public List<NonBusinessTransactionGroupDetailBean> getListBusinessTransactionGroupDetail(TrConsolidateResultBusinessGroupingH groupingH, int min, int max) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultBusinessGroupingH.PARAM_ID_BUSINESS_GROUPING_H, groupingH.getIdConsolidateResultBusinessGroupingH());
		params.put(GlobalVal.QUERY_PARAM_MIN, min);
		params.put(GlobalVal.QUERY_PARAM_MAX, max);
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by ord.transaction_date asc) as row, dgd.file_name as file, ")
				.append("ord.result_detail_id as \"detailId\", ")
				.append("orh.account_number as \"accountNo\", to_char(ord.transaction_date, 'DD/MM/YYYY') as date, ")
				.append("ord.transaction_desc as description, ord.transaction_amount as amount, ord.transaction_type as type ")
				.append("from tr_consolidate_result_business_grouping_d bgd ")
				.append("join tr_ocr_result_detail ord on bgd.id_ocr_result_detail = ord.id_ocr_result_detail ")
				.append("join tr_dashboard_group_d dgd on ord.id_dashboard_group_d = dgd.id_dashboard_group_d ")
				.append("join tr_ocr_result_header orh on ord.id_ocr_result_header = orh.id_ocr_result_header ")
				.append("where bgd.id_consolidate_result_business_grouping_h = :idBusinessGroupingH ")
				.append("and bgd.is_deleted = '0' ")
			.append(") ")
			.append("select * from cte  ")
			.append("where row between :min and :max ");
		
		return managerDAO.selectForListString(NonBusinessTransactionGroupDetailBean.class, query.toString(), params, null);
	}

	@Override
	public long countListBusinessTransactionGroupDetail(TrConsolidateResultBusinessGroupingH groupingH) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultBusinessGroupingH.PARAM_ID_BUSINESS_GROUPING_H, groupingH.getIdConsolidateResultBusinessGroupingH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_consolidate_result_business_grouping_d bgd ")
			.append("where bgd.id_consolidate_result_business_grouping_h = :idBusinessGroupingH ")
			.append("and bgd.is_deleted = '0' ");
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		
		return result.longValue();
	}

	@Override
	public TrConsolidateResultBusinessGroupingD getBusinessGroupingD(TrOcrResultDetail ocrResultDetail) {
		Map<String, Object> params = new HashMap<>();
		params.put("ocrResultDetail", ocrResultDetail);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultBusinessGroupingD bgd "
				+ "join fetch bgd.trConsolidateResultBusinessGroupingH bgh "
				+ "where bgd.trOcrResultDetail = :ocrResultDetail "
				+ "and bgd.isDeleted = '0' ", params);
	}

	@Override
	public void insertBusinessGroupingD(TrConsolidateResultBusinessGroupingD groupingD) {
		managerDAO.insert(groupingD);
	}

	@Override
	public TrConsolidateResultBusinessGroupingD getBusinessGroupingD(TrConsolidateResultBusinessGroupingH groupingH, String resultDetailId) {
		Map<String, Object> params = new HashMap<>();
		params.put("groupingH", groupingH);
		params.put(TrOcrResultDetail.PARAM_RESULT_DETAIL_ID, resultDetailId);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultBusinessGroupingD bgd "
				+ "join fetch bgd.trConsolidateResultBusinessGroupingH bgh "
				+ "join fetch bgd.trOcrResultDetail ord "
				+ "where bgd.trConsolidateResultBusinessGroupingH = :groupingH "
				+ "and ord.resultDetailId = :resultDetailId "
				+ "and bgd.isDeleted = '0' ", params);
	}

	@Override
	public void updateBusinessGroupingD(TrConsolidateResultBusinessGroupingD groupingD) {
		managerDAO.update(groupingD);
	}

	@Override
	public void deleteBusinessGroupingD(TrConsolidateResultBusinessGroupingD businessGroupingD) {
		managerDAO.delete(businessGroupingD);
	}

}
