package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

public class GeneralCashFlowDetailBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private String period;
	private BigDecimal netCash;
	private BigDecimal totalCreditAmount;
	private BigDecimal averageCreditAmount;
	private BigInteger frequencyCreditDays;
	private BigDecimal totalDebitAmount;
	private BigDecimal averageDebitAmount;
	private BigInteger frequencyDebitDays;
	private BigDecimal highestBalance;
	private BigDecimal lowestBalance;
	private BigDecimal dailyAverageBalance;
	private BigDecimal endingBalance;
	
	public String getPeriod() {
		return period;
	}
	public void setPeriod(String period) {
		this.period = period;
	}
	public BigDecimal getNetCash() {
		return netCash;
	}
	public void setNetCash(BigDecimal netCash) {
		this.netCash = netCash;
	}
	public BigDecimal getTotalCreditAmount() {
		return totalCreditAmount;
	}
	public void setTotalCreditAmount(BigDecimal totalCreditAmount) {
		this.totalCreditAmount = totalCreditAmount;
	}
	public BigDecimal getAverageCreditAmount() {
		return averageCreditAmount;
	}
	public void setAverageCreditAmount(BigDecimal averageCreditAmount) {
		this.averageCreditAmount = averageCreditAmount;
	}
	public BigInteger getFrequencyCreditDays() {
		return frequencyCreditDays;
	}
	public void setFrequencyCreditDays(BigInteger frequencyCreditDays) {
		this.frequencyCreditDays = frequencyCreditDays;
	}
	public BigDecimal getTotalDebitAmount() {
		return totalDebitAmount;
	}
	public void setTotalDebitAmount(BigDecimal totalDebitAmount) {
		this.totalDebitAmount = totalDebitAmount;
	}
	public BigDecimal getAverageDebitAmount() {
		return averageDebitAmount;
	}
	public void setAverageDebitAmount(BigDecimal averageDebitAmount) {
		this.averageDebitAmount = averageDebitAmount;
	}
	public BigInteger getFrequencyDebitDays() {
		return frequencyDebitDays;
	}
	public void setFrequencyDebitDays(BigInteger frequencyDebitDays) {
		this.frequencyDebitDays = frequencyDebitDays;
	}
	public BigDecimal getHighestBalance() {
		return highestBalance;
	}
	public void setHighestBalance(BigDecimal highestBalance) {
		this.highestBalance = highestBalance;
	}
	public BigDecimal getLowestBalance() {
		return lowestBalance;
	}
	public void setLowestBalance(BigDecimal lowestBalance) {
		this.lowestBalance = lowestBalance;
	}
	public BigDecimal getDailyAverageBalance() {
		return dailyAverageBalance;
	}
	public void setDailyAverageBalance(BigDecimal dailyAverageBalance) {
		this.dailyAverageBalance = dailyAverageBalance;
	}
	public BigDecimal getEndingBalance() {
		return endingBalance;
	}
	public void setEndingBalance(BigDecimal endingBalance) {
		this.endingBalance = endingBalance;
	}
	
}
