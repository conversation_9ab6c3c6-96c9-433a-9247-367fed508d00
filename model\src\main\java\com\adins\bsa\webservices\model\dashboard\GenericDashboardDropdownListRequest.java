package com.adins.bsa.webservices.model.dashboard;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.framework.service.base.model.MssRequestType;

public class GenericDashboardDropdownListRequest extends MssRequestType {
	
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("Dashboard name")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 64)
	private String dashboardName;
	
	@ValidationObjectName("Tenant code")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 20)
	private String tenantCode;
	
	private String isAutoConsolidate;
	private String isHitl;

	public String getIsAutoConsolidate() {
		return isAutoConsolidate;
	}

	public void setIsAutoConsolidate(String isAutoConsolidate) {
		this.isAutoConsolidate = isAutoConsolidate;
	}

	public String getDashboardName() {
		return dashboardName;
	}

	public void setDashboardName(String dashboardName) {
		this.dashboardName = dashboardName;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public String getIsHitl() {
		return isHitl;
	}

	public void setIsHitl(String isHitl) {
		this.isHitl = isHitl;
	}

}
