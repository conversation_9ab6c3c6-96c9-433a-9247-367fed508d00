package com.adins.bsa.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.encryption.InvalidPasswordException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PdfUtils {
	
	private static final Logger LOG = LoggerFactory.getLogger(PdfUtils.class);

	private PdfUtils() {
		throw new IllegalStateException("Utility class");
	}
	
	public static byte[] removePassword(byte[] originalFile, String password) throws IOException {
		
		LOG.info("Load PDF without password");
		try (PDDocument document = PDDocument.load(originalFile)) {
			
			if (!document.isEncrypted()) {
				return originalFile;
			}
			
		} catch (InvalidPasswordException e) {
			LOG.warn("PDF requires a password, trying to load PDF with password");
		}
		
		LOG.info("Load PDF with password");
		try (PDDocument document = PDDocument.load(originalFile, password)) {
			document.setAllSecurityToBeRemoved(true);
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			document.save(outputStream);
			return outputStream.toByteArray();
		}
		
	}
}
