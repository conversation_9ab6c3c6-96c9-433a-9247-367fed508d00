package com.adins.bsa.webservices.model.dashboard;

import java.util.List;

import com.adins.bsa.custom.DashboardAccountNoBean;
import com.adins.framework.service.base.model.MssResponseType;

public class DashboardAccountNoListResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<DashboardAccountNoBean> accountNos;

	public List<DashboardAccountNoBean> getAccountNos() {
		return accountNos;
	}

	public void setAccountNos(List<DashboardAccountNoBean> accountNos) {
		this.accountNos = accountNos;
	}
	
}
