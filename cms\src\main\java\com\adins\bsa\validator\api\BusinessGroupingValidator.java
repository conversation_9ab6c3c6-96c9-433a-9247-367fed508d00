package com.adins.bsa.validator.api;

import com.adins.bsa.model.TrConsolidateResultBusinessGroupingD;
import com.adins.bsa.model.TrConsolidateResultBusinessGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface BusinessGroupingValidator {
	
	TrConsolidateResultBusinessGroupingH validateGetGroupingHeader(TrDashboardGroupH dashboardGroupH, String groupName, boolean objectMustExist, AuditContext audit);
	TrConsolidateResultBusinessGroupingD validateGetGroupingDetail(TrConsolidateResultBusinessGroupingH groupingH, String resultDetailId, boolean objectMustExist, AuditContext audit);
}
