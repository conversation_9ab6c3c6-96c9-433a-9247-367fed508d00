package com.adins.bsa.validator.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupD;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupDMember;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.validator.api.SupplierBuyerGroupValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
@Component
public class GenericSupplierBuyerGroupValidator extends BaseLogic implements SupplierBuyerGroupValidator {
	
	@Override
	public TrConsolidateResultSupplierBuyerGroupH getConsolidateResultSupplierBuyerGroupH(TrDashboardGroupH trDashboardGroupH, String groupName, boolean objectMustExists, AuditContext audit) {
		if (StringUtils.isBlank(groupName)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Main group name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrConsolidateResultSupplierBuyerGroupH trConsolidateResultSupplierBuyerGroupH = daoFactory.getSupplierBuyerDao().getTrConsolidateResultSupplierBuyerGroupH(trDashboardGroupH, groupName);
		if (null == trConsolidateResultSupplierBuyerGroupH && objectMustExists) {
			throw new CommonException(getMessage("businesslogic.global.objectnotfound1",
					new String[] {"Main Group", groupName}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return trConsolidateResultSupplierBuyerGroupH;
	}

	@Override
	public TrConsolidateResultSupplierBuyerGroupD getGroupDetail(TrDashboardGroupH dashboardGroupH, String subGroupName, boolean objectMustExists, AuditContext audit) {
		
		if (StringUtils.isBlank(subGroupName)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Sub group name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrConsolidateResultSupplierBuyerGroupD sbGroupD = daoFactory.getSupplierBuyerDao().getTrConsolidateResultSupplierBuyerGroupD(dashboardGroupH, subGroupName);
		if (null == sbGroupD && objectMustExists) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_OBJ_NOT_FOUND, new String[] {"Sub group", subGroupName}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return sbGroupD;
	}

	@Override
	public TrConsolidateResultSupplierBuyerGroupDMember getGroupDetailMember(TrConsolidateResultSupplierBuyerGroupD groupD, String resultDetailId, boolean objectMustExists, AuditContext audit) {
		
		if (StringUtils.isBlank(resultDetailId)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Result detail ID"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrConsolidateResultSupplierBuyerGroupDMember groupMember = daoFactory.getSupplierBuyerDao().getGroupDetailMember(groupD, resultDetailId);
		if (null == groupMember) {
			throw new CommonException(getMessage("businesslogic.global.objectnotfound1", new String[] {"Sub group member"}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return groupMember;
	}
}
