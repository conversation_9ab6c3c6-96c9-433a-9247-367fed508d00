package com.adins.bsa.custom;

import java.io.Serializable;

public class BankStatementHeaderInfoBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private Double confidence;
	private String boxLocation;  
	private Integer boxPage; 
	
	public String getBoxLocation() {
		return boxLocation;
	}
	public void setBoxLocation(String boxLocation) {
		this.boxLocation = boxLocation;
	}
	public Double getConfidence() {
		return confidence;
	}
	public void setConfidence(Double confidence) {
		this.confidence = confidence;
	}
	public Integer getBoxPage() {
		return boxPage;
	}
	public void setBoxPage(Integer boxPage) {
		this.boxPage = boxPage;
	} 

}
