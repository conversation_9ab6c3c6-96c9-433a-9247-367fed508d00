package com.adins.bsa.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.transaction.Transactional;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.businesslogic.api.SupplierBuyerGroupLogic;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.EditSupplierBuyerSubGroupBean;
import com.adins.bsa.custom.EditSupplierBuyerSubGroupRequestBean;
import com.adins.bsa.custom.SupplierBuyerGroupBean;
import com.adins.bsa.custom.SupplierBuyerSubGroupMemberBean;
import com.adins.bsa.custom.SupplierBuyerSubGroupOfMainGroupBean;
import com.adins.bsa.custom.queryfilter.ListMasterSubGroupFilter;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupD;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupDMember;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.validator.api.DashboardValidator;
import com.adins.bsa.validator.api.SupplierBuyerGroupValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.bsa.webservices.model.dashboard.ListSubGroupSupplierBuyerGroupRequest;
import com.adins.bsa.webservices.model.dashboard.ListSubGroupSupplierBuyerGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerSubGroupMembersRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.DeleteSupplierBuyerMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.DeleteSupplierBuyerSubGroupMemberRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.EditSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListMasterSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListMasterSupplierBuyerSubGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupMemberRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupMemberResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupOfMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupOfMainGroupResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

@Component
@Transactional
public class GenericSupplierBuyerGroupLogic extends BaseLogic implements SupplierBuyerGroupLogic {
	
	private static final String CONST_RESULT_DETAIL_ID = "Result detail ID";

	@Autowired TenantValidator tenantValidator;
	@Autowired UserValidator userValidator;
	@Autowired DashboardValidator dashboardValidator;
	@Autowired SupplierBuyerGroupValidator supplierBuyerGroupValidator;
	
	@Override
	public ListSupplierBuyerGroupResponse getList(ListSupplierBuyerGroupRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		if (StringUtils.isNotBlank(request.getType()) && !GlobalVal.GROUP_TYPE_BUYER.equals(request.getType()) && !GlobalVal.GROUP_TYPE_SUPPLIER.equals(request.getType())
		&& !GlobalVal.GROUP_TYPE_RELATED_PARTIES.equals(request.getType())) {
			throw new CommonException(getMessage(GlobalKey.MSG_SUPPLIER_BUYER_INVALID_TYPE, null, audit), ReasonCommon.INVALID_VALUE);
		}
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		long totalData = daoFactory.getSupplierBuyerDao().countList(request.getType(), dashboardGroupH.getIdDashboardGroupH());
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		List<SupplierBuyerGroupBean> groups = daoFactory.getSupplierBuyerDao().getList(request.getType(), dashboardGroupH.getIdDashboardGroupH(), min, max);
		
		ListSupplierBuyerGroupResponse response = new ListSupplierBuyerGroupResponse();
		response.setGroups(groups);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public ListSubGroupSupplierBuyerGroupResponse getListSubGroup(ListSubGroupSupplierBuyerGroupRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(user, dashboardGroupH, audit);
		
		if (StringUtils.isBlank(request.getMainGroupName())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Main Group Name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrConsolidateResultSupplierBuyerGroupH trConsolidateResultSupplierBuyerGroupH = supplierBuyerGroupValidator.getConsolidateResultSupplierBuyerGroupH(dashboardGroupH, request.getMainGroupName(), true, audit);
		List<SupplierBuyerSubGroupOfMainGroupBean> listSubGroup = daoFactory.getSupplierBuyerDao().getListSubGroupByMainGroup(dashboardGroupH, trConsolidateResultSupplierBuyerGroupH);
		
		ListSubGroupSupplierBuyerGroupResponse response = new ListSubGroupSupplierBuyerGroupResponse();
		response.setListSubGroupName(listSubGroup);
		return response;
	}

	@Override
	public MssResponseType addMainGroup(AddSupplierBuyerMainGroupRequest request, AuditContext audit) {
		
		if (StringUtils.isBlank(request.getMainGroupName())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Main group name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (!GlobalVal.GROUP_TYPE_SUPPLIER.equals(request.getMainGroupType()) && !GlobalVal.GROUP_TYPE_BUYER.equals(request.getMainGroupType()) && !GlobalVal.GROUP_TYPE_RELATED_PARTIES.equals(request.getMainGroupType())) {
			throw new CommonException(getMessage(GlobalKey.MSG_SUPPLIER_BUYER_INVALID_TYPE, null, audit), ReasonCommon.INVALID_VALUE);
		}
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultSupplierBuyerGroupH sbGroupH = supplierBuyerGroupValidator.getConsolidateResultSupplierBuyerGroupH(dashboardGroupH, request.getMainGroupName(), false, audit);
		if (sbGroupH != null) {
			throw new CommonException(getMessage("businesslogic.global.namealreadyused", new String[] {request.getMainGroupName()}, audit), ReasonCommon.NAME_ALREADY_USED);
		}
		
		Date createTime = new Date();
		
		TrConsolidateResultSupplierBuyerGroupH newGroupH = new TrConsolidateResultSupplierBuyerGroupH();
		newGroupH.setTrDashboardGroupH(dashboardGroupH);
		newGroupH.setGroupType(request.getMainGroupType());
		newGroupH.setGroupName(request.getMainGroupName());
		newGroupH.setIsDeleted("0");
		newGroupH.setUsrCrt(audit.getCallerId());
		newGroupH.setDtmCrt(createTime);
		newGroupH.setUsrUpd(audit.getCallerId());
		newGroupH.setDtmUpd(createTime);
		daoFactory.getSupplierBuyerDao().insertGroupH(newGroupH);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(createTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setSupplierBuyerLastUpdDate(createTime);
		dashboardLastUpdate.setDtmUpd(createTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public MssResponseType deleteMainGroup(DeleteSupplierBuyerMainGroupRequest request, AuditContext audit) {
		
		if (StringUtils.isBlank(request.getMainGroupName())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Main group name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultSupplierBuyerGroupH sbGroupH = supplierBuyerGroupValidator.getConsolidateResultSupplierBuyerGroupH(dashboardGroupH, request.getMainGroupName(), true, audit);
		
		// set tr_consolidate_result_supplier_nuyer_group_d.id_consolidate_result_supplier_buyer_group_h = null
		daoFactory.getSupplierBuyerDao().updateGroupDSetGroupHeaderNull(sbGroupH, audit);
		daoFactory.getSupplierBuyerDao().deleteGroupH(sbGroupH);
		
		Date updateTime = new Date();
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setSupplierBuyerLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public MssResponseType addSubGroup(AddSupplierBuyerSubGroupRequest request, AuditContext audit) {
		
		if (StringUtils.isBlank(request.getSubGroupName())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Sub group name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultSupplierBuyerGroupD sbGroupD = daoFactory.getSupplierBuyerDao().getTrConsolidateResultSupplierBuyerGroupD(dashboardGroupH, request.getSubGroupName());
		if (sbGroupD != null) {
			throw new CommonException(getMessage("businesslogic.global.namealreadyused", new String[] {request.getSubGroupName()}, audit), ReasonCommon.NAME_ALREADY_USED);
		}
		
		Date createTime = new Date();
		
		TrConsolidateResultSupplierBuyerGroupD newGroupD = new TrConsolidateResultSupplierBuyerGroupD();
		newGroupD.setTrDashboardGroupH(dashboardGroupH); 
		newGroupD.setSubGroupName(request.getSubGroupName());
		newGroupD.setIsUserEdited("1");
		newGroupD.setIsDeleted("0");
		newGroupD.setUsrCrt(audit.getCallerId());
		newGroupD.setDtmCrt(createTime);
		newGroupD.setUsrUpd(audit.getCallerId());
		newGroupD.setDtmUpd(createTime);
		daoFactory.getSupplierBuyerDao().insertGroupD(newGroupD);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(createTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setSupplierBuyerLastUpdDate(createTime);
		dashboardLastUpdate.setDtmUpd(createTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}
	
	@Override
	public ListSupplierBuyerSubGroupOfMainGroupResponse getListSubGroupOfMainGroup(ListSupplierBuyerSubGroupOfMainGroupRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultSupplierBuyerGroupH groupH = supplierBuyerGroupValidator.getConsolidateResultSupplierBuyerGroupH(dashboardGroupH, request.getMainGroupName(), true, audit);
		List<SupplierBuyerSubGroupOfMainGroupBean> subGroups = daoFactory.getSupplierBuyerDao().getSubGroupNames(groupH);
		
		ListSupplierBuyerSubGroupOfMainGroupResponse response = new ListSupplierBuyerSubGroupOfMainGroupResponse();
		response.setSubGroups(subGroups);
		return response;
	}

	@Override
	public ListMasterSupplierBuyerSubGroupResponse getListMasterSubGroup(ListMasterSupplierBuyerSubGroupRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		ListMasterSubGroupFilter filter = new ListMasterSubGroupFilter();
		filter.setDashboardGroupH(dashboardGroupH);
		filter.setMainGroupName(request.getMainGroupName());
		filter.setSubGroupName(request.getSubGroupName());
		filter.setMin(min);
		filter.setMax(max);
		
		List<SupplierBuyerSubGroupOfMainGroupBean> subGroups = daoFactory.getSupplierBuyerDao().getListMasterSubGroup(filter);
		long totalData = daoFactory.getSupplierBuyerDao().countListMasterSubGroup(filter);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		ListMasterSupplierBuyerSubGroupResponse response = new ListMasterSupplierBuyerSubGroupResponse();
		response.setSubGroups(subGroups);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public MssResponseType deleteSupplierBuyerSubGroup(AddSupplierBuyerSubGroupRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultSupplierBuyerGroupD sbGroupD = supplierBuyerGroupValidator.getGroupDetail(dashboardGroupH, request.getSubGroupName(), true, audit);
		if (sbGroupD.getTrConsolidateResultSupplierBuyerGroupH() != null) {
			throw new CommonException(getMessage("businesslogic.supplierbuyergroup.mustbeunassigned", null, audit), ReasonCommon.FORBIDDEN_ACCESS);
		}
		
		Date updateTime = new Date();
		
		if ("1".equals(sbGroupD.getIsUserEdited())) {
			daoFactory.getSupplierBuyerDao().deleteGroupDetailMembers(sbGroupD);
			daoFactory.getSupplierBuyerDao().deleteGroupD(sbGroupD);
		} else {
			daoFactory.getSupplierBuyerDao().deleteGroupDetailMembers(sbGroupD, "1");
			daoFactory.getSupplierBuyerDao().updateGroupDetailMembersFlagAsDeleted(sbGroupD, "0", audit);
			
			sbGroupD.setIsUserEdited("1");
			sbGroupD.setIsDeleted("1");
			sbGroupD.setUsrUpd(audit.getCallerId());
			sbGroupD.setDtmUpd(updateTime);
			daoFactory.getSupplierBuyerDao().updateGroupD(sbGroupD);
		}
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setSupplierBuyerLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public ListSupplierBuyerSubGroupMemberResponse getListSubGroupMember(ListSupplierBuyerSubGroupMemberRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultSupplierBuyerGroupD sbGroupD = supplierBuyerGroupValidator.getGroupDetail(dashboardGroupH, request.getSubGroupName(), true, audit);
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		List<SupplierBuyerSubGroupMemberBean> members = daoFactory.getSupplierBuyerDao().getListSupplierBuyerSubGroupMember(sbGroupD, min, max);
		long totalData = daoFactory.getSupplierBuyerDao().countListSupplierBuyerSubGroupMember(sbGroupD);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		ListSupplierBuyerSubGroupMemberResponse response = new ListSupplierBuyerSubGroupMemberResponse();
		response.setMembers(members);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public MssResponseType deleteSubGroupMember(DeleteSupplierBuyerSubGroupMemberRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultSupplierBuyerGroupD groupD = supplierBuyerGroupValidator.getGroupDetail(dashboardGroupH, request.getSubGroupName(), true, audit);
		TrConsolidateResultSupplierBuyerGroupDMember member = supplierBuyerGroupValidator.getGroupDetailMember(groupD, request.getResultDetailId(), true, audit);
		Date updateTime = new Date();
		
		if ("1".equals(member.getIsUserEdited())) {
			daoFactory.getSupplierBuyerDao().deleteGroupDetailMember(member);
		} else {
			member.setIsDeleted("1");
			member.setIsUserEdited("1");
			member.setUsrUpd(audit.getCallerId());
			member.setDtmUpd(updateTime);
			daoFactory.getSupplierBuyerDao().updateGroupDetailMember(member);
		}
		
		groupD.setIsUserEdited("1");
		groupD.setUsrUpd(audit.getCallerId());
		groupD.setDtmUpd(updateTime);
		daoFactory.getSupplierBuyerDao().updateGroupD(groupD);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setSupplierBuyerLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}
	
	private List<TrOcrResultDetail> validateResultDetailIds(TrDashboardGroupH dashboardGroupH, List<String> resultDetailIds, AuditContext audit) {
		
		List<TrOcrResultDetail> details = new ArrayList<>();
		
		for (String resultDetailId : resultDetailIds) {
			TrOcrResultDetail detail = daoFactory.getOcrResultDao().getOcrResultDetail(dashboardGroupH, resultDetailId);
			if (null == detail) {
				throw new CommonException(getMessage("businesslogic.global.objectnotfound1", new String[] {CONST_RESULT_DETAIL_ID}, audit), ReasonCommon.OBJECT_NOT_FOUND);
			}
			
			TrConsolidateResultSupplierBuyerGroupDMember member = daoFactory.getSupplierBuyerDao().getGroupDetailMember(resultDetailId);
			if (null != member) {
				String subGroupName = member.getTrConsolidateResultSupplierBuyerGroupD().getSubGroupName();
				throw new CommonException(getMessage("businesslogic.global.objectalreadysaved", new String[] {CONST_RESULT_DETAIL_ID, subGroupName}, audit), ReasonCommon.NAME_ALREADY_USED);
			}
			
			details.add(detail);
		}
		
		return details;
	}

	@Override
	public MssResponseType addSupplierBuyerSubGroupMembers(AddSupplierBuyerSubGroupMembersRequest request, AuditContext audit) {
		
		if (CollectionUtils.isEmpty(request.getResultDetailIds())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_RESULT_DETAIL_ID}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrConsolidateResultSupplierBuyerGroupD groupD = supplierBuyerGroupValidator.getGroupDetail(dashboardGroupH, request.getSubGroupName(), true, audit);
		List<TrOcrResultDetail> ocrResultDetails = validateResultDetailIds(dashboardGroupH, request.getResultDetailIds(), audit);
		Date updateTime = new Date();
		
		for (TrOcrResultDetail ocrResultDetail : ocrResultDetails) {
			TrConsolidateResultSupplierBuyerGroupDMember member = new TrConsolidateResultSupplierBuyerGroupDMember();
			member.setTrConsolidateResultSupplierBuyerGroupD(groupD);
			member.setTrOcrResultDetail(ocrResultDetail);
			member.setIsUserEdited("1");
			member.setIsDeleted("0");
			member.setUsrCrt(audit.getCallerId());
			member.setDtmCrt(updateTime);
			member.setUsrUpd(audit.getCallerId());
			member.setDtmUpd(updateTime);
			daoFactory.getSupplierBuyerDao().insertGroupDetailMember(member);
		}
		
		groupD.setUsrUpd(audit.getCallerId());
		groupD.setDtmUpd(updateTime);
		daoFactory.getSupplierBuyerDao().updateGroupD(groupD);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setSupplierBuyerLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}
	
	private List<EditSupplierBuyerSubGroupBean> validateEditSubGroupRequest(TrDashboardGroupH dashboardGroupH, TrConsolidateResultSupplierBuyerGroupH groupH, EditSupplierBuyerSubGroupRequest request, AuditContext audit) {
		if (CollectionUtils.isEmpty(request.getSubGroups())) {
			return new ArrayList<>();
		}
		
		List<EditSupplierBuyerSubGroupBean> editSubGroupBeans = new ArrayList<>();
		
		for (EditSupplierBuyerSubGroupRequestBean subGroupBean : request.getSubGroups()) {
			
			TrConsolidateResultSupplierBuyerGroupD groupD = supplierBuyerGroupValidator.getGroupDetail(dashboardGroupH, subGroupBean.getSubGroupName(), true, audit);
			TrConsolidateResultSupplierBuyerGroupH currentGroupHeader = groupD.getTrConsolidateResultSupplierBuyerGroupH();
			if (null != currentGroupHeader && currentGroupHeader.getIdConsolidateResultSupplierBuyerGroupH() != groupH.getIdConsolidateResultSupplierBuyerGroupH()) {
				throw new CommonException(getMessage("businesslogic.global.forbiddenaccess1", new String[] {groupD.getSubGroupName()}, audit), ReasonCommon.FORBIDDEN_ACCESS);
			}
			
			EditSupplierBuyerSubGroupBean bean = new EditSupplierBuyerSubGroupBean();
			bean.setGroupD(groupD);
			bean.setDeleted(subGroupBean.getIsDeleted().booleanValue());
			editSubGroupBeans.add(bean);
			
		}
		
		return editSubGroupBeans;
	}

	@Override
	public MssResponseType editSupplierBuyerSubGroup(EditSupplierBuyerSubGroupRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		if (StringUtils.isBlank(request.getNewMainGroupName())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"New main group name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}

		if (!GlobalVal.GROUP_TYPE_SUPPLIER.equals(request.getMainGroupType()) && !GlobalVal.GROUP_TYPE_BUYER.equals(request.getMainGroupType())
		&& !GlobalVal.GROUP_TYPE_RELATED_PARTIES.equals(request.getMainGroupType())) {
			throw new CommonException(getMessage(GlobalKey.MSG_SUPPLIER_BUYER_INVALID_TYPE, null, audit), ReasonCommon.INVALID_VALUE);
		}
		
		TrConsolidateResultSupplierBuyerGroupH groupH = supplierBuyerGroupValidator.getConsolidateResultSupplierBuyerGroupH(dashboardGroupH, request.getOldMainGroupName(), true, audit);
		List<EditSupplierBuyerSubGroupBean> editSubGroupBeans = validateEditSubGroupRequest(dashboardGroupH, groupH, request, audit);
		Date updateTime = new Date();
		
		for (EditSupplierBuyerSubGroupBean editSubGroupBean : editSubGroupBeans) {
			TrConsolidateResultSupplierBuyerGroupD groupD = editSubGroupBean.getGroupD();
			groupD.setTrConsolidateResultSupplierBuyerGroupH(editSubGroupBean.isDeleted() ? null : groupH);
			groupD.setUsrUpd(audit.getCallerId());
			groupD.setDtmUpd(updateTime);
			daoFactory.getSupplierBuyerDao().updateGroupD(groupD);
		}
		
		groupH.setGroupName(request.getNewMainGroupName());
		groupH.setGroupType(request.getMainGroupType());
		groupH.setUsrUpd(audit.getCallerId());
		groupH.setDtmUpd(updateTime);
		daoFactory.getSupplierBuyerDao().updateGroupH(groupH);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setSupplierBuyerLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}
}
