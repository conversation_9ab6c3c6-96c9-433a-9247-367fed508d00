package com.adins.bsa.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "tr_consolidate_result_anomaly")
public class TrConsolidateResultAnomaly extends UpdateableEntity {
	
	private long idConsolidateResultAnomaly;
	private String anomalyId;
	private TrDashboardGroupH trDashboardGroupH;
	private TrDashboardGroupD trDashboardGroupD;
	private TrOcrResultHeader trOcrResultHeader;
	private TrOcrResultDetail trOcrResultDetail;
	private MsLov lovAnomalyReason;
	private MsLov lovAnomalyRisk;
	private String isUserEdited;
	private String isDeleted;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_consolidate_result_anomaly", unique = true, nullable = false)
	public long getIdConsolidateResultAnomaly() {
		return idConsolidateResultAnomaly;
	}

	public void setIdConsolidateResultAnomaly(long idConsolidateResultAnomaly) {
		this.idConsolidateResultAnomaly = idConsolidateResultAnomaly;
	}
	
	@Column(name = "anomaly_id", length = 64, nullable = false)
	public String getAnomalyId() {
		return anomalyId;
	}

	public void setAnomalyId(String anomalyId) {
		this.anomalyId = anomalyId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_h", nullable = false)
	public TrDashboardGroupH getTrDashboardGroupH() {
		return trDashboardGroupH;
	}

	public void setTrDashboardGroupH(TrDashboardGroupH trDashboardGroupH) {
		this.trDashboardGroupH = trDashboardGroupH;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_d", nullable = false)
	public TrDashboardGroupD getTrDashboardGroupD() {
		return trDashboardGroupD;
	}

	public void setTrDashboardGroupD(TrDashboardGroupD trDashboardGroupD) {
		this.trDashboardGroupD = trDashboardGroupD;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ocr_result_header")
	public TrOcrResultHeader getTrOcrResultHeader() {
		return trOcrResultHeader;
	}

	public void setTrOcrResultHeader(TrOcrResultHeader trOcrResultHeader) {
		this.trOcrResultHeader = trOcrResultHeader;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ocr_result_detail")
	public TrOcrResultDetail getTrOcrResultDetail() {
		return trOcrResultDetail;
	}

	public void setTrOcrResultDetail(TrOcrResultDetail trOcrResultDetail) {
		this.trOcrResultDetail = trOcrResultDetail;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_anomaly_reason", nullable = false)
	public MsLov getLovAnomalyReason() {
		return lovAnomalyReason;
	}

	public void setLovAnomalyReason(MsLov lovAnomalyReason) {
		this.lovAnomalyReason = lovAnomalyReason;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_anomaly_risk", nullable = false)
	public MsLov getLovAnomalyRisk() {
		return lovAnomalyRisk;
	}

	public void setLovAnomalyRisk(MsLov lovAnomalyRisk) {
		this.lovAnomalyRisk = lovAnomalyRisk;
	}

	@Column(name = "is_user_edited", length = 1)
	public String getIsUserEdited() {
		return isUserEdited;
	}

	public void setIsUserEdited(String isUserEdited) {
		this.isUserEdited = isUserEdited;
	}

	@Column(name = "is_deleted", length = 1)
	public String getIsDeleted() {
		return isDeleted;
	}
	
	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}
	
}
