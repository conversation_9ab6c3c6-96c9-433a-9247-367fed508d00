package com.adins.am.businesslogic;

import java.util.Date;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.LocaleUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.model.AmGeneralsetting;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.dataaccess.factory.api.DaoFactory;
import com.adins.framework.persistence.dao.api.AuditLog;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
@Transactional
public abstract class BaseLogic {
	private Logger logger = LoggerFactory.getLogger(BaseLogic.class);
	
	@Autowired
	protected AuditLog auditManager;
	@Autowired
	protected DaoFactory daoFactory;
	@Autowired
	protected MessageSource messageSource;

	public Locale retrieveLocaleAudit(AuditContext auditContext) {
		if (auditContext == null || auditContext.getParameters() == null 
				|| auditContext.getParameters().isEmpty()) {
			Locale locale = this.retrieveDefaultLocale();
			this.logger.trace("AuditContext is null, using default locale {}", locale);
			return locale;
		}
		
		Map<String, Object> auditParams = auditContext.getParameters();
		String auditLang = (String) auditParams.get(AmGlobalKey.AUDIT_KEY_LOCALE);
		if (StringUtils.isBlank(auditLang)) {
			Locale locale = this.retrieveDefaultLocale();
			this.logger.trace("AuditContext's locale not found, using default locale {}", locale);
			return locale;
		}
		
		return LocaleUtils.toLocale(auditLang);
	}
	
	public Locale retrieveDefaultLocale() {
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao()
				.getGsObjByCode(AmGlobalKey.GENERALSETTING_DEFAULT_LOCALE);
		if (gs == null) {
			return LocaleUtils.toLocale("en");
		}
		
		return LocaleUtils.toLocale(StringUtils.lowerCase(gs.getGsValue()));
	}
	
	/**
	 * @param code Key from messages_en.properties or messages_in.properties
	 * @param args Parameters for the message
	 * @param audit
	 * @return Message from messages_en.properties or messages_in.properties
	 */
	protected String getMessage(String code, Object[] args, AuditContext audit) {
		return messageSource.getMessage(code, args, retrieveLocaleAudit(audit));
	}
	
	/**
	 * <AUTHOR>
	 * 
	 * @param processName Process name for log
	 * @param startTime Process start time
	 * @param finishTime Process finish time
	 */
	protected void logProcessDuration(String processName, Date startTime, Date finishTime) {
		long durationMs = finishTime.getTime() - startTime.getTime();
		logger.info("{} duration: {} ms", processName, durationMs);
	}
	
}
