package com.adins.bsa.dataaccess.api;

import com.adins.am.model.AmGeneralsetting;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface CommonDao {
	String getUuidDao();
	
	// Sequence
	long nextSequenceTrBalanceMutationTrxNo();
	
	// General setting
	AmGeneralsetting getGeneralSetting(String code);
	AmGeneralsetting getGeneralSettingByTenant(String code, MsTenant tenant);
	void updateGeneralSetting(AmGeneralsetting generalSetting);
	
	// Execute function
	String executeDeleteBankStatement(TrDashboardGroupD dashboardGroupD, AuditContext audit);
	String executeDeleteOcrResultDetail(TrOcrResultDetail ocrResultDetail);
	String executeDeleteDashboard(TrDashboardGroupH dashboardGroupH, AuditContext audit);
}
