package com.adins.bsa.businesslogic.api;

import com.adins.bsa.webservices.model.businesstransactiongroup.ListBusinessTransactionGroupDetailResponse;
import com.adins.bsa.webservices.model.businesstransactiongroup.ListBusinessTransactionGroupResponse;
import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface BusinessTransactionGroupLogic {
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	ListBusinessTransactionGroupResponse getListBusinessTransactionGroup(GenericDashboardPagingRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType addBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType deleteBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	ListBusinessTransactionGroupDetailResponse getListBusinessTransactionGroupDetail(ListNonBusinessTransactionGroupDetailRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType addBusinessTransactionGroupDetail(AddNonBusinessTransactionGroupDetailRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType deleteBusinessTransactionGroupDetail(DeleteNonBusinessTransactionGroupDetailRequest request, AuditContext audit);
}
