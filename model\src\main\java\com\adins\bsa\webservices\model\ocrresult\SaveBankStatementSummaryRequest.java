package com.adins.bsa.webservices.model.ocrresult;

import java.util.List;

import com.adins.bsa.custom.SaveBankStatementSummaryRequestBean;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class SaveBankStatementSummaryRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	private String fileSourcePath;
	private String saveType;
	private List<SaveBankStatementSummaryRequestBean> summaries;
	
	public String getFileSourcePath() {
		return fileSourcePath;
	}
	public void setFileSourcePath(String fileSourcePath) {
		this.fileSourcePath = fileSourcePath;
	}
	public String getSaveType() {
		return saveType;
	}
	public void setSaveType(String saveType) {
		this.saveType = saveType;
	}
	public List<SaveBankStatementSummaryRequestBean> getSummaries() {
		return summaries;
	}
	public void setSummaries(List<SaveBankStatementSummaryRequestBean> summaries) {
		this.summaries = summaries;
	}
	
}
