package com.adins.bsa.dataaccess.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.BankStatementHeaderSummaryBean;
import com.adins.bsa.custom.BankStatementTransactionDetailBean;
import com.adins.bsa.custom.GetBankStatementTransactionSummaryCalculationResponseBean;
import com.adins.bsa.custom.NonBusinessTransactionGroupTransactionBean;
import com.adins.bsa.custom.OcrResultBean;
import com.adins.bsa.custom.OcrResultPeriodBean;
import com.adins.bsa.custom.queryfilter.ListBankStatementTransactionDetailFilter;
import com.adins.bsa.custom.queryfilter.ListOcrResultFilter;
import com.adins.bsa.custom.queryfilter.ListTransactionFilter;
import com.adins.bsa.dataaccess.api.OcrResultDao;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.model.TrOcrResultHeader;
import com.adins.bsa.model.TrOcrResultSummaryPeriod;
import com.adins.bsa.util.MssTool;

@Component
@Transactional
public class OcrResultDaoHbn extends BaseDaoHbn implements OcrResultDao {
	
	private static final String PARAM_ACCOUNT_NO = "accountNo";
	private static final String PARAM_PERIOD = "period";
	
	private static final Logger LOG = LoggerFactory.getLogger(OcrResultDaoHbn.class);

	@Override
	public List<OcrResultBean> getOcrResultList(ListOcrResultFilter filter) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("min", filter.getMin());
		params.put("max", filter.getMax());
		
		StringBuilder conditionalParam = buildListOcrResultConditionalParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by dgd.id_dashboard_group_d asc) as \"rowNum\", ")
				.append("dgd.file_name as filename, dgd.file_source_path as \"fileSourcePath\", ")
				.append("COALESCE(orh.account_bank, '-') as bank, COALESCE(orh.account_number, '-') as \"accountNo\", ")
				.append("COALESCE(orh.account_name, '-') as \"accountName\", ")
				.append("COALESCE(CAST(total_pages AS text), '-') as page, ")
				.append("TO_CHAR(dgd.dtm_crt, 'DD/MM/YYYY HH24:MI') as \"uploadDate\", ")
				.append("COALESCE(TO_CHAR(dgd.dtm_upd, 'DD/MM/YYYY HH24:MI'), '-') as \"lastUpdated\", ")
				.append("CASE WHEN ml.code = 'FAILED' THEN CONCAT(ml.description, ' - ', dgd.process_result_message) ELSE ml.description END as status, ")
				.append("CASE WHEN ml.code in ('READY_CHECK', 'EDIT', 'COMPLETE', 'CONSOLIDATED') THEN '1' ELSE '0' END as editable, ")
				.append("COALESCE(TRUNC(COUNT(CASE WHEN ord.is_red = '1' AND ord.is_edited = '0' AND ord.is_deleted = '0' THEN 1 END) * 100.0 / NULLIF(COUNT(CASE WHEN ord.is_deleted = '0' THEN 1 END), 0), 1) || '%', '-') AS \"redPercentage\", ")
				.append("CASE WHEN COUNT(CASE WHEN ord.is_red = '1' AND ord.is_edited = '0' AND ord.is_deleted = '0' THEN 1 END) * 100.0 / NULLIF(COUNT(CASE WHEN ord.is_deleted = '0' THEN 1 END), 0) >= 10 THEN '1' ELSE '0' END AS \"redPercentageWarning\" ")
				.append("from tr_dashboard_group_d dgd ")
				.append("left join tr_ocr_result_header orh on dgd.id_dashboard_group_d = orh.id_dashboard_group_d ")
				.append("left join tr_ocr_result_detail ord on dgd.id_dashboard_group_d = ord.id_dashboard_group_d ")
				.append("join ms_lov ml on dgd.lov_process_status = ml.id_lov ")
				.append("where 1=1 ")
				.append(conditionalParam)
				.append("GROUP BY dgd.id_dashboard_group_d, dgd.file_name, dgd.file_source_path, ")
				.append("orh.account_bank, orh.account_number, orh.account_name, ")
				.append("dgd.total_pages, dgd.dtm_crt, dgd.dtm_upd, ml.code, ml.description, dgd.process_result_message ")
			.append(") ")
			.append("select * ")
			.append("from cte ")
			.append("where \"rowNum\" between :min and :max ");
		
		return managerDAO.selectForListString(OcrResultBean.class, query.toString(), params, null);
	}

	@Override
	public long countOcrResultList(ListOcrResultFilter filter) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder conditionalParam = buildListOcrResultConditionalParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_dashboard_group_d dgd ")
			.append("left join tr_ocr_result_header orh on dgd.id_dashboard_group_d = orh.id_dashboard_group_d ")
			.append("join ms_lov ml on dgd.lov_process_status = ml.id_lov ")
			.append("where 1=1 ")
			.append(conditionalParam);
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		
		return result.longValue();
	}
	private StringBuilder buildListTransactionParam(ListTransactionFilter filter, Map<String, Object> params) {
		StringBuilder builder = new StringBuilder();
		
		builder.append("and dgd.id_dashboard_group_h = :idDashboardGroupH ");
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, filter.getDashboardGroupH().getIdDashboardGroupH());
		
		if (StringUtils.isNotBlank(filter.getAccountNo())) {
			builder.append(" and orh.account_number = :accountNo ");
			params.put(PARAM_ACCOUNT_NO, filter.getAccountNo());
		}
		
		if (StringUtils.isNotBlank(filter.getCategory())) {
			builder.append(" and ml.code = :category ");
			params.put("category", filter.getCategory());
		}
		
		if (StringUtils.isNotBlank(filter.getType())) {
			builder.append(" and ord.transaction_type = :transType ");
			params.put("transType", StringUtils.upperCase(filter.getType()));
		}
		
		if (StringUtils.isNotBlank(filter.getAmountMoreThan())) {
			builder.append(" and ord.transaction_amount >= CAST (:amountMoreThan as BIGINT) ");
			params.put("amountMoreThan", filter.getAmountMoreThan());
		}
		
		if (StringUtils.isNotBlank(filter.getAmountLessThan())) {
			builder.append(" and ord.transaction_amount <= CAST (:amountLessThan as BIGINT) ");
			params.put("amountLessThan", filter.getAmountLessThan());
		}
		
		if (StringUtils.isNotBlank(filter.getPeriod())) {
			String datePeriodeString = filter.getPeriod() + "-01";
			
			builder.append(" and DATE(ord.transaction_date) between CAST(date_trunc('MONTH',CAST(:dateString as date))as DATE) and cast((date_trunc('month', Cast(:dateString as date)) + interval '1 month - 1 day') as date)  ");
			params.put("dateString", datePeriodeString);
		}
		
		if (StringUtils.isNotBlank(filter.getDescription())) {
			builder.append(" and UPPER(ord.transaction_desc) LIKE :desc ");
			params.put("desc", "%" + StringUtils.upperCase(filter.getDescription()) + "%");
		}
		
		return builder;
	}
	
	private StringBuilder buildListOcrResultConditionalParam(ListOcrResultFilter filter, Map<String, Object> params) {
		StringBuilder builder = new StringBuilder();
		
		// is_active param
		builder.append("and dgd.is_active = '1' ");
		
		// tr_dashboard_group_h param
		builder.append("and dgd.id_dashboard_group_h = :idDashboardGroupH ");
		params.put("idDashboardGroupH", filter.getDashboardGroupH().getIdDashboardGroupH());
		
		// bank param
		if (StringUtils.isNotBlank(filter.getBankName())) {
			builder.append("and orh.account_bank = :bankName ");
			params.put("bankName", filter.getBankName());
		}
		
		// process status param
		if (StringUtils.isNotBlank(filter.getLovProcessResultCode())) {
			builder.append("and ml.code = :code ");
			params.put(MsLov.CODE_HBM, StringUtils.upperCase(filter.getLovProcessResultCode()));
		}
		
		return builder;
	}

	@Override
	public long countOcrResultByDashboardHAndAccountNo(TrDashboardGroupH dashboardGroupH, String accountNo) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		params.put(PARAM_ACCOUNT_NO, accountNo);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_ocr_result_header ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ")
			.append("and account_number = :accountNo ");
		
		BigInteger totalData = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == totalData) {
			return 0;
		}
		
		return totalData.longValue();
	}


	@Override
	public TrOcrResultDetail getOcrResultDetail(TrDashboardGroupH dashboardGroupH, String resultDetailId) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_DASHBOARD_GROUP_H, dashboardGroupH);
		params.put("resultDetailId", resultDetailId);
		
		return managerDAO.selectOne(
				"from TrOcrResultDetail ord "
				+ "join fetch ord.trOcrResultHeader orh "
				+ "join fetch ord.trDashboardGroupD dgg "
				+ "where orh.trDashboardGroupH = :dashboardGroupH "
				+ "and ord.resultDetailId = :resultDetailId "
				+ "and ord.isDeleted = '0' ", params);
	}
	
	@Override
	public TrOcrResultDetail getOcrResultDetail(TrDashboardGroupD dashboardGroupD, String resultDetailId) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupD.PARAM_DASHBOARD_GROUP_D, dashboardGroupD);
		params.put("resultDetailId", resultDetailId);
		
		return managerDAO.selectOne(
				"from TrOcrResultDetail ord "
				+ "join fetch ord.trDashboardGroupD dgd "
				+ "where ord.trDashboardGroupD = :dashboardGroupD "
				+ "and ord.resultDetailId = :resultDetailId "
				+ "and ord.isDeleted = '0' ", params);
	}


	
	@Override
	public TrOcrResultHeader getOcrResultHeaderByDashboardAndTenantAndAccountNo (TrDashboardGroupH trDashboardGroupH , MsTenant msTenant,String accountNo) {
		Map<String, Object> params = new HashMap<>();
		
		params.put(TrDashboardGroupH.PARAM_DASHBOARD_GROUP_H, trDashboardGroupH);
		params.put("tenant", msTenant);
		params.put(PARAM_ACCOUNT_NO, accountNo);
		return managerDAO.selectOne(
				"from TrOcrResultHeader orh "
				+ "join fetch orh.trDashboardGroupH dgh "
				+ "where orh.trDashboardGroupH = :dashboardGroupH "
				+ "and orh.accountNumber = :accountNo "
				+ "and dgh.msTenant = :tenant ", params);
	}
	
	
	@Override
	public List<NonBusinessTransactionGroupTransactionBean> getListTransactionNonBusinessGroup (ListTransactionFilter filter, String formSource) {
		Map<String, Object> params = new HashMap<>();
	
		StringBuilder conditionalParam = buildListTransactionParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select row_number() over(order by ord.transaction_date, ord.id_ocr_result_detail) as row, ")
			.append("ord.result_detail_id as \"resultDetailId\", dgd.file_name as file, orh.account_number as \"accountNo\", ")
			.append("cast(ord.transaction_date as varchar) as date, ord.transaction_desc as description, ")
			.append("ord.transaction_amount as amount, ord.transaction_type as type ")
			.append("from tr_ocr_result_detail ord ")
			.append("join tr_dashboard_group_d dgd on ord.id_dashboard_group_d = dgd.id_dashboard_group_d ")
			.append("join tr_ocr_result_header orh on ord.id_ocr_result_header = orh.id_ocr_result_header ")
			.append("left join ms_lov ml on ord.lov_category = ml.id_lov ")
			.append("where 1=1 ")
			.append(conditionalParam);
		
		if (GlobalVal.FORM_SOURCE_ANOMALY.equalsIgnoreCase(formSource)) {
			query
				.append("and not exists (")
					.append("select 1 ")
					.append("from tr_consolidate_result_anomaly ra ")
					.append("where ra.id_ocr_result_detail = ord.id_ocr_result_detail ")
					.append("and ra.is_deleted = '0' ")
				.append(") "); 
		} else {
			query
				.append("and not exists ( ")
					.append("select 1 ")
					.append("from tr_consolidate_result_nonbusiness_grouping_d ngd ")
					.append("where ngd.id_ocr_result_detail = ord.id_ocr_result_detail ")
					.append("and ngd.is_deleted = '0' ")
				.append(") ")
				.append("and not exists (")
					.append("select 1 ")
					.append("from tr_consolidate_result_business_grouping_d bgd ")
					.append("where bgd.id_ocr_result_detail = ord.id_ocr_result_detail ")
					.append("and bgd.is_deleted = '0' ")
				.append(") ")
				.append("and not exists (")
					.append("select 1 ")
					.append("from tr_consolidate_result_circular_grouping_d cgd ")
					.append("where cgd.id_ocr_result_detail = ord.id_ocr_result_detail ")
					.append("and cgd.is_deleted = '0' ")
				.append(") ")
				.append("and not exists (")
					.append("select 1 ")
					.append("from tr_consolidate_result_supplier_buyer_group_d_member sbgd ")
					.append("where sbgd.id_ocr_result_detail = ord.id_ocr_result_detail ")
					.append("and sbgd.is_deleted = '0' ")
				.append(") ");
		}
		
		return	managerDAO.selectForListString(NonBusinessTransactionGroupTransactionBean.class, query.toString(), params, null);

	}

	@Override
	public TrOcrResultHeader getOcrResultHeaderByDashboardAndTenantAndDetail(TrDashboardGroupH trDashboardGroupH, TrDashboardGroupD trDashboardGroupD, MsTenant msTenant) {
        Map<String, Object> params = new HashMap<>();
		
		params.put(TrDashboardGroupH.PARAM_DASHBOARD_GROUP_H, trDashboardGroupH);
		params.put(TrDashboardGroupD.PARAM_DASHBOARD_GROUP_D, trDashboardGroupD);
		params.put("tenant", msTenant); 
		return managerDAO.selectOne(
				"from TrOcrResultHeader orh "
				+ "join fetch orh.trDashboardGroupH dgh "
				+ "join fetch orh.trDashboardGroupD dgd "
				+ "where orh.trDashboardGroupH = :dashboardGroupH " 
				+ "and orh.trDashboardGroupD = :dashboardGroupD "
				+ "and dgh.msTenant = :tenant ", params);
	}

	@Override
	public List<BankStatementHeaderSummaryBean> getBankStatementHeaderSummaryPeriod(TrDashboardGroupD trDashboardGroupD) {
		Map<String, Object> params = new HashMap<>(); 
		params.put("idDashboardGroupD", trDashboardGroupD.getIdDashboardGroupD());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select to_char(orsp.period, 'yyyy-MM') as \"period\", ")
			.append("orsp.opening_balance as \"beginningBalance\", ")
			.append("orsp.ending_balance as \"endingBalance\" ")
			.append("from tr_ocr_result_summary_period orsp ")
			.append("join tr_dashboard_group_d dgd on dgd.id_dashboard_group_d = orsp.id_dashboard_group_d ")
			.append("where dgd.id_dashboard_group_d = :idDashboardGroupD ") 
			.append("group by \"period\",\"beginningBalance\", \"endingBalance\"")
			.append("order by period asc ");
		 
		return managerDAO.selectForListString(BankStatementHeaderSummaryBean.class, query.toString(), params, null); 
	}
	
	private StringBuilder buildListBankStatementTransactionDetailConditionalParam(ListBankStatementTransactionDetailFilter filter, Map<String, Object> params) {
		StringBuilder builder = new StringBuilder();
		
		// tr_dashboard_group_d param
		builder.append("and ord.id_dashboard_group_D = :idDashboardGroupD ");
		params.put("idDashboardGroupD", filter.getTrDashboardGroupD().getIdDashboardGroupD());
		
		// tr_dashboard_group_d param
		builder.append("and ord.id_ocr_result_header = :idOcrResultHeader ");
		params.put("idOcrResultHeader", filter.getTrOcrResultHeader().getIdOcrResultHeader());
		
		// type param
		if (StringUtils.isNotBlank(filter.getType())) { 
			builder.append("and ord.transaction_type = :type ");
			params.put("type", StringUtils.upperCase(filter.getType()));
		}
		
		// type param
		if (StringUtils.isNotBlank(filter.getStatus()) && "RED".equals(filter.getStatus())) {
			builder.append("and ord.is_red = :status ");
			params.put("status", "1");
			
			builder.append("and ord.is_edited = :edited ");
			params.put("edited", "0"); 
		}
		
		return builder;
	}

	@Override
	public List<BankStatementTransactionDetailBean> getListBankStatementTransactionDetail(ListBankStatementTransactionDetailFilter filter) {
		Map<String, Object> params = new HashMap<>();
		params.put("min", filter.getMin());
		params.put("max", filter.getMax());
		 
		StringBuilder conditionalParam = buildListBankStatementTransactionDetailConditionalParam(filter, params);
		 
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("SELECT row_number() over(order by ord.transaction_date asc, ord.id_ocr_result_detail asc) AS \"rowNum\", ")
				.append("TO_CHAR(ord.transaction_date, 'YYYY-MM-DD') AS \"date\", ")
				.append("CASE WHEN ord.transaction_date_confidence >= 0.9 THEN '0'ELSE '1'END AS \"dateConfidenceRed\", ")
				.append("ord.transaction_date_box_location AS \"dateBoxLocation\", ")
				.append("ord.transaction_date_box_page AS \"dateBoxPage\", ") 
				.append("ord.transaction_desc AS \"description\", ")
				.append("CASE WHEN ord.transaction_desc_confidence >= 0.9 THEN '0'ELSE '1'END AS \"descriptionConfidenceRed\", ")
				.append("ord.transaction_desc_box_location AS \"descriptionBoxLocation\", ")
				.append("ord.transaction_desc_box_page AS \"descriptionBoxPage\", ") 
				.append("ord.transaction_type AS \"type\",  ")
				.append("CASE WHEN ord.transaction_type_confidence >= 0.9 THEN '0'ELSE '1'END AS \"typeConfidenceRed\",  ")
				.append("ord.transaction_type_box_location AS \"typeBoxLocation\", ")
				.append("ord.transaction_type_box_page AS \"typeBoxPage\", ") 
				.append("ord.transaction_amount AS \"amount\", ")
				.append("CASE WHEN ord.transaction_amount_confidence >= 0.9 THEN '0'ELSE '1'END AS \"amountConfidenceRed\", ")
				.append("ord.transaction_amount_box_location AS \"amountBoxLocation\", ")
				.append("ord.transaction_amount_box_page AS \"amountBoxPage\", ") 
				.append("ord.transaction_ending_balance AS \"endingBalance\", ")
				.append("CASE WHEN ord.transaction_ending_balance_confidence >= 0.9 THEN '0'ELSE '1'END AS \"endingBalanceConfidenceRed\", ")
				.append("ord.transaction_ending_balace_box_location AS \"endingBalanceBoxLocation\", ")
				.append("ord.transaction_ending_balance_box_page AS \"endingBalanceBoxPage\", ") 
				.append("ord.is_edited AS \"isEdited\", ")
				.append("ord.is_delete_overlap AS \"isDeleteOverlap\", ")
				.append("ord.result_detail_id AS \"resultDetailId\" ")
				.append("FROM tr_ocr_result_detail ord ")
				.append("JOIN tr_ocr_result_header orh ON ord.id_ocr_result_header = orh.id_ocr_result_header ")
				.append("JOIN tr_dashboard_group_d dgd ON ord.id_dashboard_group_d = dgd.id_dashboard_group_d ")
				.append("WHERE 1=1 ")
				.append(conditionalParam)
			.append(") ")
			.append("select * from cte ")
			.append("where \"rowNum\" between :min and :max ");
		
		return managerDAO.selectForListString(BankStatementTransactionDetailBean.class, query.toString(), params, null);
	}

	@Override
	public long countListBankStatementTransactionDetail(ListBankStatementTransactionDetailFilter filter) {
		Map<String, Object> params = new HashMap<>();
		params.put("min", filter.getMin());
		params.put("max", filter.getMax());
		 
		StringBuilder conditionalParam = buildListBankStatementTransactionDetailConditionalParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_ocr_result_detail ord ")
			.append("join tr_ocr_result_header orh ON ord.id_ocr_result_header = orh.id_ocr_result_header ")
			.append("join tr_dashboard_group_d dgd ON ord.id_dashboard_group_d = dgd.id_dashboard_group_d ")
			.append("where 1=1 ")
			.append(conditionalParam);
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		
		return result.longValue();
	}

	@Override
	public TrOcrResultHeader getOcrResultHeaderByDashboard(TrDashboardGroupD dashboardGroupD) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupD.PARAM_DASHBOARD_GROUP_D, dashboardGroupD);
		
		return managerDAO.selectOne(
				"from TrOcrResultHeader orh "
				+ "where orh.trDashboardGroupD = :dashboardGroupD "
				+ "and orh.isActive = '1' ", params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrOcrResultHeader getOcrResultHeaderByDashboardNewTrx(TrDashboardGroupD dashboardGroupD) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupD.PARAM_DASHBOARD_GROUP_D, dashboardGroupD);
		
		return managerDAO.selectOne(
				"from TrOcrResultHeader orh "
				+ "where orh.trDashboardGroupD = :dashboardGroupD "
				+ "and orh.isActive = '1' ", params);
	}

	@Override
	public void updateOcrResultHeader(TrOcrResultHeader header) {
		managerDAO.update(header);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateOcrResultHeaderNewTrx(TrOcrResultHeader header) {
		managerDAO.update(header);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<OcrResultPeriodBean> getOcrResultPeriodsNewTrx(TrDashboardGroupH dashboardGroupH, String accountNo) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		params.put(TrOcrResultHeader.PARAM_ACCOUNT_NUMBER, accountNo);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select orh.account_number as \"accountNo\", TO_CHAR(rsp.period, 'YYYY-MM-DD HH24:MI:SS') as period ")
			.append("from tr_ocr_result_summary_period rsp ")
			.append("join tr_ocr_result_header orh on orh.id_ocr_result_header = rsp.id_ocr_result_header ")
			.append("where orh.id_dashboard_group_h = :idDashboardGroupH ")
			.append("and orh.account_number = :accountNumber ")
			.append("group by account_number, period ");
		
		return managerDAO.selectForListString(OcrResultPeriodBean.class, query.toString(), params, null);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<OcrResultPeriodBean> getOcrResultPeriodsNewTrx(TrDashboardGroupH dashboardGroupH, String accountNo, String period) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		params.put(TrOcrResultHeader.PARAM_ACCOUNT_NUMBER, accountNo);
		params.put(PARAM_PERIOD, MssTool.formatStringToDate(period, GlobalVal.DATE_TIME_FORMAT_SEC));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select orh.id_ocr_result_header as \"idOcrResultHeader\", rsp.total_transaction as \"totalTransaction\" ")
			.append("from tr_ocr_result_summary_period rsp ")
			.append("join tr_ocr_result_header orh on orh.id_ocr_result_header = rsp.id_ocr_result_header ")
			.append("where orh.id_dashboard_group_h = :idDashboardGroupH ")
			.append("and orh.account_number = :accountNumber ")
			.append("and period = :period ")
			.append("order by rsp.total_transaction desc, rsp.id_ocr_result_summary_period asc ");
		
		return managerDAO.selectForListString(OcrResultPeriodBean.class, query.toString(), params, null);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateOcrResultDetailIsDeleteOverlapNewTrx(Long idOcrResultHeader, Date periodDate, String isDeleteOverlap, String usrUpd) {
		
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(periodDate);
		
		// Get start of month
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		String startOfMonth = MssTool.formatDateToStringIn(calendar.getTime(), GlobalVal.DATE_FORMAT);
		
		// Get end of month
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		String endOfMonth = MssTool.formatDateToStringIn(calendar.getTime(), GlobalVal.DATE_FORMAT);
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrOcrResultHeader.PARAM_ID_OCR_RESULT_HEADER, idOcrResultHeader);
		params.put("startTime", MssTool.formatStringToDate(startOfMonth + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
		params.put("endTime", MssTool.formatStringToDate(endOfMonth + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
		params.put("isDeleteOverlap", isDeleteOverlap);
		params.put("usrUpd", usrUpd);
		
		LOG.info("Set is_delete_overlap to {} for id_ocr_result_header with trx date >= {} & <= {}", isDeleteOverlap, idOcrResultHeader, startOfMonth, endOfMonth);
		
		StringBuilder query = new StringBuilder();
		query
			.append("update tr_ocr_result_detail ")
			.append("set is_delete_overlap = :isDeleteOverlap , ")
			.append("usr_upd = :usrUpd , ")
			.append("dtm_upd = now() ")
			.append("where id_ocr_result_header = :idOcrResultHeader ")
			.append("and transaction_date >= :startTime ")
			.append("and transaction_date <= :endTime ");
		
		managerDAO.updateNativeString(query.toString(), params);
		
	}
	
	@Override
	public List<GetBankStatementTransactionSummaryCalculationResponseBean> getListPeriodBankStatementTransactionSummaryCalculation(long idDashboardGroupH,long idDashboardGroupD) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, idDashboardGroupH);
		params.put(TrDashboardGroupD.PARAM_ID_DASHBOARD_GROUP_D, idDashboardGroupD);
		
		StringBuilder query = new StringBuilder();
		query
			.append(" select TO_CHAR(torsp.period,'yyyy-MM') as \"period\" ")
			.append(" ,cast(torsp.opening_balance + sum ( case when tord.transaction_type = 'DEBIT' then  ")
			.append(" (transaction_amount * -1)  ")
			.append(" else ")
			.append(" (transaction_amount * 1)  ")
			.append(" end ")
			.append(" ) as varchar) as \"endingBalance\" ")
			.append(" from tr_dashboard_group_h tdgh ")
			.append(" join tr_dashboard_group_d  tdgd on tdgh.id_dashboard_group_h = tdgd.id_dashboard_group_h ")
			.append(" join tr_ocr_result_header torh on tdgh.id_dashboard_group_h = torh.id_dashboard_group_h  ")
			.append(" and torh.id_dashboard_group_d = tdgd.id_dashboard_group_d ")
			.append(" join tr_ocr_result_detail tord on tord.id_dashboard_group_d = tdgd.id_dashboard_group_d  ")
			.append(" and tord.id_ocr_result_header =torh.id_ocr_result_header ")
			.append(" join tr_ocr_result_summary_period torsp on torsp.id_dashboard_group_d = tdgd.id_dashboard_group_d  ")
			.append(" and torh.id_ocr_result_header =torsp.id_ocr_result_header ")
			.append(" and TO_CHAR(torsp.period,'yyyy-MM') = TO_CHAR(tord.transaction_date,'yyyy-MM') ")
			.append(" where tdgh.id_dashboard_group_h = :idDashboardGroupH ")
			.append(" and tdgd.id_dashboard_group_d = :idDashboardGroupD ")
			.append(" group by TO_CHAR(torsp.period,'yyyy-MM'),torsp.opening_balance ");
			
		
		return managerDAO.selectForListString(GetBankStatementTransactionSummaryCalculationResponseBean.class, query.toString(), params, null);
	}

	
	@Override
	public BigDecimal getPeriodBankStatementTransactionSummaryCalculation(long idDashboardGroupH,long idDashboardGroupD,String period, BigDecimal openingBalance) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, idDashboardGroupH);
		params.put(TrDashboardGroupD.PARAM_ID_DASHBOARD_GROUP_D, idDashboardGroupD);
		params.put(PARAM_PERIOD,period);
		params.put("openingBalance",openingBalance);
		StringBuilder query = new StringBuilder();
		query
			.append(" select :openingBalance + sum ( case when tord.transaction_type = 'DEBIT' then  ")
			.append(" (transaction_amount * -1)  ")
			.append(" else ")
			.append(" (transaction_amount * 1)  ")
			.append(" end ")
			.append(" ) as \"endingBalance\" ")
			.append(" from tr_dashboard_group_h tdgh ")
			.append(" join tr_dashboard_group_d  tdgd on tdgh.id_dashboard_group_h = tdgd.id_dashboard_group_h ")
			.append(" join tr_ocr_result_header torh on tdgh.id_dashboard_group_h = torh.id_dashboard_group_h  ")
			.append(" and torh.id_dashboard_group_d = tdgd.id_dashboard_group_d ")
			.append(" join tr_ocr_result_detail tord on tord.id_dashboard_group_d = tdgd.id_dashboard_group_d  ")
			.append(" and tord.id_ocr_result_header =torh.id_ocr_result_header ")
			.append(" join tr_ocr_result_summary_period torsp on torsp.id_dashboard_group_d = tdgd.id_dashboard_group_d  ")
			.append(" and torh.id_ocr_result_header =torsp.id_ocr_result_header ")
			.append(" and TO_CHAR(torsp.period,'yyyy-MM') = TO_CHAR(tord.transaction_date,'yyyy-MM') ")
			.append(" where tdgh.id_dashboard_group_h = :idDashboardGroupH ")
			.append(" and tdgd.id_dashboard_group_d = :idDashboardGroupD ")
			.append(" and TO_CHAR(torsp.period,'yyyy-MM') = :period ");
			
		BigDecimal result = (BigDecimal) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return BigDecimal.valueOf(0);
		}
		return result;
	}
	
	@Override
	public TrOcrResultSummaryPeriod getOcrResultSummaryPeriod(TrDashboardGroupD dashboardGroupD, Date period) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupD.PARAM_DASHBOARD_GROUP_D, dashboardGroupD);
		params.put(PARAM_PERIOD, period);
		
		return managerDAO.selectOne(
				"from TrOcrResultSummaryPeriod orsp "
				+ "join fetch orsp.trDashboardGroupD dgd "
				+ "where orsp.trDashboardGroupD = :dashboardGroupD "
				+ "and orsp.period = :period ", params);
	}

	@Override
	public void updateOcrResultSummaryPeriod(TrOcrResultSummaryPeriod summaryPeriod) {
		managerDAO.update(summaryPeriod);
	}

	@Override
	public long countOcrResultDetail(TrDashboardGroupD dashboardGroupD, Date transactionDate, String isDeleteOverlap) {
		
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(transactionDate);
		
		// Get start of month
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		String startOfMonth = MssTool.formatDateToStringIn(calendar.getTime(), GlobalVal.DATE_FORMAT);
		
		// Get end of month
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		String endOfMonth = MssTool.formatDateToStringIn(calendar.getTime(), GlobalVal.DATE_FORMAT);
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupD.PARAM_ID_DASHBOARD_GROUP_D, dashboardGroupD.getIdDashboardGroupD());
		params.put("isDeleteOverlap", isDeleteOverlap);
		params.put("startDate", MssTool.formatStringToDate(startOfMonth + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
		params.put("endDate", MssTool.formatStringToDate(endOfMonth + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_ocr_result_detail ")
			.append("where id_dashboard_group_d = :idDashboardGroupD ")
			.append("and is_delete_overlap = :isDeleteOverlap ")
			.append("and transaction_date >= :startDate ")
			.append("and transaction_date <= :endDate ");
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		
		return result.longValue();
	}

	@Override
	public void insertOcrResultDetail(TrOcrResultDetail ocrResultDetail) {
		managerDAO.insert(ocrResultDetail);
	}

	@Override
	public void updateOcrResultDetail(TrOcrResultDetail ocrResultDetail) {
		managerDAO.update(ocrResultDetail);
	}

	@Override
	public List<Map<String, Object>> getLatestBankStatementHeaders(MsTenant tenant, int totalData) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
		params.put("totalData", totalData);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select dgh.dashboard_group_name, dtl.file_source_path, dtl.account_name, ")
			.append("dtl.account_number, dtl.account_currency, dtl.account_bank_office, dtl.account_address ")
			.append("from tr_dashboard_group_h dgh ")
			.append("join lateral ( ")
				.append("select dgd.file_source_path, orh.account_name, orh.account_number, orh.account_currency, orh.account_bank_office, orh.account_address ")
				.append("from tr_ocr_result_header orh ")
				.append("join tr_dashboard_group_d dgd on orh.id_dashboard_group_d = dgd.id_dashboard_group_d ")
				.append("where orh.id_dashboard_group_h = dgh.id_dashboard_group_h ")
				.append("and dgd.is_active = '1' ")
				.append("order by orh.id_ocr_result_header desc limit 1 ")
			.append(") as dtl on true ")
			.append("where id_ms_tenant = :idMsTenant ")
			.append("and dgh.is_active = '1' ")
			.append("order by dgh.id_dashboard_group_h desc limit :totalData ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getLatestOcrResultDetails(MsTenant tenant, int totalData) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
		params.put("totalData", totalData);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select dgh.dashboard_group_name, dgd.file_source_path, dtl.result_detail_id ")
			.append("from tr_dashboard_group_d dgd ")
			.append("join tr_dashboard_group_h dgh on dgd.id_dashboard_group_h = dgh.id_dashboard_group_h ")
			.append("join lateral ( ")
				.append("select ord.result_detail_id ")
				.append("from tr_ocr_result_detail ord ")
				.append("where ord.id_dashboard_group_d = dgd.id_dashboard_group_d ")
				.append("and ord.is_deleted = '0' ")
				.append("order by ord.id_ocr_result_detail desc limit 1 ")
			.append(") dtl on true ")
			.append("where dgh.id_ms_tenant = :idMsTenant ")
			.append("and dgh.is_active = '1' ")
			.append("and dgd.is_active = '1' ")
			.append("order by dgd.id_dashboard_group_d desc limit :totalData ");
			
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

}
