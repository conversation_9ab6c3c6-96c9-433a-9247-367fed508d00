package com.adins.bsa.businesslogic.external.api;

import com.adins.bsa.webservices.model.external.dashboard.UpdateBankStatementProcessStatusExternalRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface DashboardExternalLogic {
	MssResponseType updateBankStatementProcessStatus(UpdateBankStatementProcessStatusExternalRequest request, String xApiKey, AuditContext audit);
}
