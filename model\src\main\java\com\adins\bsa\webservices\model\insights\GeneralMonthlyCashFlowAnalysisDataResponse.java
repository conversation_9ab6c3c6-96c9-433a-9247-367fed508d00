package com.adins.bsa.webservices.model.insights;

import java.util.List;

import com.adins.bsa.custom.GeneralCashFlowDetailBean;
import com.adins.bsa.custom.InsightsChartBean;
import com.adins.framework.service.base.model.MssResponseType;

public class GeneralMonthlyCashFlowAnalysisDataResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<String> labels;
	private List<InsightsChartBean> cashFlow;
	private List<GeneralCashFlowDetailBean> cashFlowDetails;
	private List<InsightsChartBean> transactionCount;
	
	public List<String> getLabels() {
		return labels;
	}
	public void setLabels(List<String> labels) {
		this.labels = labels;
	}
	public List<InsightsChartBean> getCashFlow() {
		return cashFlow;
	}
	public void setCashFlow(List<InsightsChartBean> cashFlow) {
		this.cashFlow = cashFlow;
	}
	public List<GeneralCashFlowDetailBean> getCashFlowDetails() {
		return cashFlowDetails;
	}
	public void setCashFlowDetails(List<GeneralCashFlowDetailBean> cashFlowDetails) {
		this.cashFlowDetails = cashFlowDetails;
	}
	public List<InsightsChartBean> getTransactionCount() {
		return transactionCount;
	}
	public void setTransactionCount(List<InsightsChartBean> transactionCount) {
		this.transactionCount = transactionCount;
	}
	
}
