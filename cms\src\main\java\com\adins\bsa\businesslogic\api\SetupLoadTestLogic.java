package com.adins.bsa.businesslogic.api;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.bsa.webservices.model.loadtest.SetupAddDashboardLoadTestRequest;
import com.adins.bsa.webservices.model.loadtest.SetupAddDashboardLoadTestResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListBankStatementResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListDashboardNameResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListEmailResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListObjectResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface SetupLoadTestLogic {

	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_LOAD_TEST_SETUP', #request.tenantCode, authentication)")
	SetupAddDashboardLoadTestResponse setupAddDashboard(SetupAddDashboardLoadTestRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_LOAD_TEST_SETUP', #request.tenantCode, authentication)")
	SetupListDashboardNameResponse getLatestActiveDashboardNames(SetupAddDashboardLoadTestRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_LOAD_TEST_SETUP', #request.tenantCode, authentication)")
	SetupAddDashboardLoadTestResponse setupAddNewBankStatement(SetupAddDashboardLoadTestRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_LOAD_TEST_SETUP', #request.tenantCode, authentication)")
	SetupListDashboardNameResponse getLatestConsolidateReadyDashboards(SetupAddDashboardLoadTestRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_LOAD_TEST_SETUP', #request.tenantCode, authentication)")
	SetupListEmailResponse getLatestLoadTestEmails(SetupAddDashboardLoadTestRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_LOAD_TEST_SETUP', #request.tenantCode, authentication)")
	SetupListEmailResponse getLatestEmailsWithOtp(SetupAddDashboardLoadTestRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_LOAD_TEST_SETUP', #request.tenantCode, authentication)")
	SetupListBankStatementResponse getLatestDeletableBankStatements(SetupAddDashboardLoadTestRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_LOAD_TEST_SETUP', #request.tenantCode, authentication)")
	SetupListObjectResponse getLatestBankStatementHeaders(SetupAddDashboardLoadTestRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_LOAD_TEST_SETUP', #request.tenantCode, authentication)")
	SetupListObjectResponse getLatestOcrResultDetails(SetupAddDashboardLoadTestRequest request, AuditContext audit);
}
