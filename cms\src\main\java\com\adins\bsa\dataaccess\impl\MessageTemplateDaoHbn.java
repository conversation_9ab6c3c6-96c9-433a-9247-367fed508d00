package com.adins.bsa.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.bsa.dataaccess.api.MessageTemplateDao;
import com.adins.bsa.model.MsMsgTemplate;

@Component
@Transactional
public class MessageTemplateDaoHbn extends BaseDaoHbn implements MessageTemplateDao {

	@Override
	@Transactional(readOnly = true)
	public MsMsgTemplate getMessageTemplate(String templateType, String templateCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("type", StringUtils.upperCase(templateType));
		params.put("code", StringUtils.upperCase(templateCode));
		
		return managerDAO.selectOne(
				"from MsMsgTemplate mt "
				+ "where mt.templateType = :type "
				+ "and mt.templateCode = :code ", params);
	}

}
