package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.AnomalyLogic;
import com.adins.bsa.webservices.frontend.api.AnomalyService;
import com.adins.bsa.webservices.model.anomaly.AddAnomaliesRequest;
import com.adins.bsa.webservices.model.anomaly.DeleteAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.DeleteGroupAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyMetadataRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyMetadataResponse;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyResponse;
import com.adins.bsa.webservices.model.anomaly.ListGroupAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListGroupAnomalyResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/anomaly")
@Api(value = "AnomalyService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericAnomalyServiceEndpoint implements AnomalyService {

	@Autowired AnomalyLogic anomalyLogic;
	
	@Override
	@POST
	@Path("/s/list")
	public ListAnomalyResponse getList(ListAnomalyRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return anomalyLogic.getList(request, audit);
	}

	@Override
	@POST
	@Path("/s/add")
	public MssResponseType addAnomalies(AddAnomaliesRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return anomalyLogic.addAnomalies(request, audit);
	}

	@Override
	@POST
	@Path("/s/delete")
	public MssResponseType deleteAnomaly(DeleteAnomalyRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return anomalyLogic.deleteAnomaly(request, audit);
	}

	@Override
	@POST
	@Path("/s/listGroupAnomaly")
	public ListGroupAnomalyResponse getListGroupAnomaly(ListGroupAnomalyRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return anomalyLogic.getListGroupAnomaly(request, audit);
	}

	@Override
	@POST
	@Path("/s/deleteGroupAnomaly")
	public MssResponseType deleteGroupAnomaly(DeleteGroupAnomalyRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return anomalyLogic.deleteGroupAnomaly(request, audit);
	}

	@Override
	@POST
	@Path("/s/listAnomalyMetadata")
	public ListAnomalyMetadataResponse getListAnomalyMetadata(ListAnomalyMetadataRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return anomalyLogic.getListAnomalyMetadata(request, audit);
	}

}
