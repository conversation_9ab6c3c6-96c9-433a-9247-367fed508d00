package com.adins.am.model;
// Generated 09-Sep-2021 22:49:32 by Hibernate Tools 5.2.12.Final

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.am.model.custom.ActiveDeleteAndUpdateableEntity;
import com.adins.bsa.model.MsUseroftenant;
import com.adins.bsa.model.TrDashboardGroupH;

/**
 * AmMsuser generated by hbm2java
 */
@Entity
@Table(name = "am_msuser")
public class AmMsuser extends ActiveDeleteAndUpdateableEntity  implements java.io.Serializable {
	private static final long serialVersionUID = 1L;

	public static final String ID_MS_USER_HBM = "idMsUser";
	public static final String LOGIN_ID_HBM = "loginId";
	public static final String FULLNAME_HBM = "fullName";
	public static final String HASHED_PHONE_HBM = "hashedPhone";
	public static final String OTP_CODE_HBM = "otpCode";
	public static final String HASHED_IDNO_HBM = "hashedIdNo";
	public static final String PASSWORD_HBM = "password";
	public static final String AM_MSUSER_HBM = "amMsuser";

	private long idMsUser;
	private String loginId;
	private String fullName;
	private String initialName;
	private String loginProvider;
	private String password;
	private Integer failCount;
	private String isLoggedIn;
	private String isLocked;
	private String isDormant;
	private Date lastLoggedIn;
	private Date lastLocked;
	private Date lastExpired;
	private Date lastDormant;
	private Date lastLoggedFail;
	private Date lastRequestOut;
	private Date prevLoggedIn;
	private Date prevLoggedFail;
	private String changePwdLogin;
	private String resetCode;
	private Date resetCodeRequestDate;
	private Short resetCodeRequestNum;
	private Short verifyResetCodeRequestNum;
	private Date verifyResetCodeRequestDate;

	private Set<AmUserpwdhistory> amUserpwdhistories = new HashSet<>(0);
	private Set<MsUseroftenant> msUseroftenants = new HashSet<>(0);
	private Set<AmMemberofrole> amMemberofroles = new HashSet<>(0);
	private Set<AmAuditlog> amAuditlogs = new HashSet<>(0);
	private Set<TrDashboardGroupH> trDashboardGroupHs = new HashSet<>(0);
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_user", unique = true, nullable = false)
	public long getIdMsUser() {
		return this.idMsUser;
	}

	public void setIdMsUser(long idMsUser) {
		this.idMsUser = idMsUser;
	}

	@Column(name = "login_id", length = 80)
	public String getLoginId() {
		return this.loginId;
	}

	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}

	@Column(name = "full_name", nullable = false, length = 80)
	public String getFullName() {
		return this.fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	@Column(name = "initial_name", length = 15)
	public String getInitialName() {
		return this.initialName;
	}

	public void setInitialName(String initialName) {
		this.initialName = initialName;
	}

	@Column(name = "login_provider", length = 6)
	public String getLoginProvider() {
		return this.loginProvider;
	}

	public void setLoginProvider(String loginProvider) {
		this.loginProvider = loginProvider;
	}

	@Column(name = "password", nullable = false, length = 200)
	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@Column(name = "fail_count")
	public Integer getFailCount() {
		return this.failCount;
	}

	public void setFailCount(Integer failCount) {
		this.failCount = failCount;
	}

	@Column(name = "is_logged_in", length = 1)
	public String getIsLoggedIn() {
		return this.isLoggedIn;
	}

	public void setIsLoggedIn(String isLoggedIn) {
		this.isLoggedIn = isLoggedIn;
	}

	@Column(name = "is_locked", length = 1)
	public String getIsLocked() {
		return this.isLocked;
	}

	public void setIsLocked(String isLocked) {
		this.isLocked = isLocked;
	}

	@Column(name = "is_dormant", length = 1)
	public String getIsDormant() {
		return this.isDormant;
	}

	public void setIsDormant(String isDormant) {
		this.isDormant = isDormant;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_logged_in", length = 29)
	public Date getLastLoggedIn() {
		return this.lastLoggedIn;
	}

	public void setLastLoggedIn(Date lastLoggedIn) {
		this.lastLoggedIn = lastLoggedIn;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_locked", length = 29)
	public Date getLastLocked() {
		return this.lastLocked;
	}

	public void setLastLocked(Date lastLocked) {
		this.lastLocked = lastLocked;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_expired", length = 29)
	public Date getLastExpired() {
		return this.lastExpired;
	}

	public void setLastExpired(Date lastExpired) {
		this.lastExpired = lastExpired;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_dormant", length = 29)
	public Date getLastDormant() {
		return this.lastDormant;
	}

	public void setLastDormant(Date lastDormant) {
		this.lastDormant = lastDormant;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_logged_fail", length = 29)
	public Date getLastLoggedFail() {
		return this.lastLoggedFail;
	}

	public void setLastLoggedFail(Date lastLoggedFail) {
		this.lastLoggedFail = lastLoggedFail;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_request_out", length = 29)
	public Date getLastRequestOut() {
		return this.lastRequestOut;
	}

	public void setLastRequestOut(Date lastRequestOut) {
		this.lastRequestOut = lastRequestOut;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "prev_logged_in", length = 29)
	public Date getPrevLoggedIn() {
		return this.prevLoggedIn;
	}

	public void setPrevLoggedIn(Date prevLoggedIn) {
		this.prevLoggedIn = prevLoggedIn;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "prev_logged_fail", length = 29)
	public Date getPrevLoggedFail() {
		return this.prevLoggedFail;
	}

	public void setPrevLoggedFail(Date prevLoggedFail) {
		this.prevLoggedFail = prevLoggedFail;
	}

	@Column(name = "change_pwd_login", length = 1)
	public String getChangePwdLogin() {
		return this.changePwdLogin;
	}

	public void setChangePwdLogin(String changePwdLogin) {
		this.changePwdLogin = changePwdLogin;
	}

	@Column(name = "reset_code", length = 40)
	public String getResetCode() {
		return this.resetCode;
	}

	public void setResetCode(String resetCode) {
		this.resetCode = resetCode;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "reset_code_request_date", length = 29)
	public Date getResetCodeRequestDate() {
		return this.resetCodeRequestDate;
	}

	public void setResetCodeRequestDate(Date resetCodeRequestDate) {
		this.resetCodeRequestDate = resetCodeRequestDate;
	}

	@Column(name = "reset_code_request_num")
	public Short getResetCodeRequestNum() {
		return this.resetCodeRequestNum;
	}

	public void setResetCodeRequestNum(Short resetCodeRequestNum) {
		this.resetCodeRequestNum = resetCodeRequestNum;
	}
	
	@Column(name = "verify_reset_code_request_num")
	public Short getVerifyResetCodeRequestNum() {
		return verifyResetCodeRequestNum;
	}

	public void setVerifyResetCodeRequestNum(Short verifyResetCodeRequestNum) {
		this.verifyResetCodeRequestNum = verifyResetCodeRequestNum;
	}

	@Column(name = "verify_reset_code_request_date")
	public Date getVerifyResetCodeRequestDate() {
		return verifyResetCodeRequestDate;
	}

	public void setVerifyResetCodeRequestDate(Date verifyResetCodeRequestDate) {
		this.verifyResetCodeRequestDate = verifyResetCodeRequestDate;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmUserpwdhistory> getAmUserpwdhistories() {
		return this.amUserpwdhistories;
	}

	public void setAmUserpwdhistories(Set<AmUserpwdhistory> amUserpwdhistories) {
		this.amUserpwdhistories = amUserpwdhistories;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<MsUseroftenant> getMsUseroftenants() {
		return this.msUseroftenants;
	}

	public void setMsUseroftenants(Set<MsUseroftenant> msUseroftenants) {
		this.msUseroftenants = msUseroftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmMemberofrole> getAmMemberofroles() {
		return this.amMemberofroles;
	}

	public void setAmMemberofroles(Set<AmMemberofrole> amMemberofroles) {
		this.amMemberofroles = amMemberofroles;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmAuditlog> getAmAuditlogs() {
		return this.amAuditlogs;
	}

	public void setAmAuditlogs(Set<AmAuditlog> amAuditlogs) {
		this.amAuditlogs = amAuditlogs;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuserCreator")
	public Set<TrDashboardGroupH> getTrDashboardGroupHs() {
		return trDashboardGroupHs;
	}
	
	public void setTrDashboardGroupHs(Set<TrDashboardGroupH> trDashboardGroupHs) {
		this.trDashboardGroupHs = trDashboardGroupHs;
	}	
	
}
