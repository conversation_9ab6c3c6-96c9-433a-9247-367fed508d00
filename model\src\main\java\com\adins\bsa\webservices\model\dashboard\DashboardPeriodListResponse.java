package com.adins.bsa.webservices.model.dashboard;

import java.util.List;

import com.adins.bsa.custom.DashboardPeriodBean;
import com.adins.framework.service.base.model.MssResponseType;

public class DashboardPeriodListResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<DashboardPeriodBean> periods;

	public List<DashboardPeriodBean> getPeriods() {
		return periods;
	}

	public void setPeriods(List<DashboardPeriodBean> periods) {
		this.periods = periods;
	}
	
}
