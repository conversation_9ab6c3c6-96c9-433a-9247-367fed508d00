package com.adins.bsa.validator.api;

import com.adins.am.model.AmMsuser;
import com.adins.bsa.constants.enums.DashboardAction;
import com.adins.bsa.constants.enums.DashboardFeature;
import com.adins.bsa.model.MsTenant;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface RoleValidator {
    
    /**
     * Add dashboard -> Feature: DASHBOARD, Action: ADD
     * Add bank statement -> Feature: OCR_RESULT, Action: ADD
     * Consolidate -> Feature: CONSOLIDATE, Action: ADD
     * Others -> Adjust to accessed tab and action
     */
    void validateFeaturePermission(AmMsuser user, MsTenant tenant, String roleCode, DashboardFeature feature, DashboardAction action, AuditContext auditContext);
}
