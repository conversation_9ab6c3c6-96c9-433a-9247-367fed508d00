package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.anomaly.AddAnomaliesRequest;
import com.adins.bsa.webservices.model.anomaly.DeleteAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.DeleteGroupAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyMetadataRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyMetadataResponse;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyResponse;
import com.adins.bsa.webservices.model.anomaly.ListGroupAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListGroupAnomalyResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface AnomalyService {
	ListAnomalyResponse getList(ListAnomalyRequest request);
	MssResponseType addAnomalies(AddAnomaliesRequest request);
	MssResponseType deleteAnomaly(DeleteAnomalyRequest request);
	ListGroupAnomalyResponse getListGroupAnomaly(ListGroupAnomalyRequest request);
	MssResponseType deleteGroupAnomaly(DeleteGroupAnomalyRequest request);
	ListAnomalyMetadataResponse getListAnomalyMetadata(ListAnomalyMetadataRequest request);
}
