package com.adins.bsa.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "ms_supervisorofuser")
public class MsSupervisorOfUser extends UpdateableEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private long idSupervisorOfUser;
	private AmMsuser msUserSupervisor;
	private AmMsuser msUserStaff;
	
	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_supervisor_of_user", unique = true, nullable = false)
	public long getIdSupervisorOfUser() {
		return idSupervisorOfUser;
	}
	public void setIdSupervisorOfUser(long idSupervisorOfUser) {
		this.idSupervisorOfUser = idSupervisorOfUser;
	}
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user_supervisor", nullable = false)
	public AmMsuser getMsUserSupervisor() {
		return msUserSupervisor;
	}
	public void setMsUserSupervisor(AmMsuser msUserSupervisor) {
		this.msUserSupervisor = msUserSupervisor;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user_staff", nullable = false)
	public AmMsuser getMsUserStaff() {
		return msUserStaff;
	}
	public void setMsUserStaff(AmMsuser msUserStaff) {
		this.msUserStaff = msUserStaff;
	}
}
