package com.adins.bsa.webservices.model.nonbusinesstransactiongroup;

import java.util.List;

import com.adins.bsa.custom.NonBusinessTransactionGroupTransactionBean;
import com.adins.framework.service.base.model.MssResponseType;

public class ListTransactionnonBusinessTransactionGroupResponse extends MssResponseType {
	private static final long serialVersionUID = 1L;
	
	private List<NonBusinessTransactionGroupTransactionBean> listTransactions;

	public List<NonBusinessTransactionGroupTransactionBean> getListTransactions() {
		return listTransactions;
	}

	public void setListTransactions(List<NonBusinessTransactionGroupTransactionBean> listTransactions) {
		this.listTransactions = listTransactions;
	}

	
}
