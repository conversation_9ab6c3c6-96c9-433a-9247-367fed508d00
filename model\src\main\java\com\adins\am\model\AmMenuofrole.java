package com.adins.am.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "am_menuofrole")
public class AmMenuofrole extends UpdateableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	private long idAmMenuofrole;
	private AmMsmenu amMsmenu;
	private AmMsrole amMsrole;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_am_menuofrole", unique = true, nullable = false)
	public long getIdAmMenuofrole() {
		return this.idAmMenuofrole;
	}

	public void setIdAmMenuofrole(long idAmMenuofrole) {
		this.idAmMenuofrole = idAmMenuofrole;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_menu", nullable = false)
	public AmMsmenu getAmMsmenu() {
		return this.amMsmenu;
	}

	public void setAmMsmenu(AmMsmenu amMsmenu) {
		this.amMsmenu = amMsmenu;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_role", nullable = false)
	public AmMsrole getAmMsrole() {
		return this.amMsrole;
	}

	public void setAmMsrole(AmMsrole amMsrole) {
		this.amMsrole = amMsrole;
	}

}
