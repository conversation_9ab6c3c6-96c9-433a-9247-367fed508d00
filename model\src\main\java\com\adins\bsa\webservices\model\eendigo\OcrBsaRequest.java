package com.adins.bsa.webservices.model.eendigo;

public class OcrBsaRequest {
	
	private String tenantCode;
	private String key;
	private String ossFile;
	private String ossPath;
	private Integer totalPages;
	private String fileType;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	}
	public String getOssFile() {
		return ossFile;
	}
	public void setOssFile(String ossFile) {
		this.ossFile = ossFile;
	}
	public String getOssPath() {
		return ossPath;
	}
	public void setOssPath(String ossPath) {
		this.ossPath = ossPath;
	}
	public Integer getTotalPages() {
		return totalPages;
	}
	public void setTotalPages(Integer totalPage) {
		this.totalPages= totalPage;
	}
	public String getFileType() {
		return fileType;
	}
	public void setFileType(String fileType) {
		this.fileType = fileType;
	}
	
}
