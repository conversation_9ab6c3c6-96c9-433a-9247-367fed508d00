package com.adins.bsa.custom;

import java.math.BigInteger;

public class OcrResultPeriodBean {

	private String accountNo;
	private String period; // YYYY-MM-DD HH24:MI:SS
	private BigInteger idOcrResultHeader;
	private Integer totalTransaction;
	
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public String getPeriod() {
		return period;
	}
	public void setPeriod(String period) {
		this.period = period;
	}
	public BigInteger getIdOcrResultHeader() {
		return idOcrResultHeader;
	}
	public void setIdOcrResultHeader(BigInteger idOcrResultHeader) {
		this.idOcrResultHeader = idOcrResultHeader;
	}
	public Integer getTotalTransaction() {
		return totalTransaction;
	}
	public void setTotalTransaction(Integer totalTransaction) {
		this.totalTransaction = totalTransaction;
	}
	
}
