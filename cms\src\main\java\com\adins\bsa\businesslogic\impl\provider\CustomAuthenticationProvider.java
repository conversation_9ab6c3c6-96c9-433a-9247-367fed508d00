package com.adins.bsa.businesslogic.impl.provider;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsmenu;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.OauthAccessToken;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.model.MsTenant;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.password.PasswordHash;

@Component
@Qualifier("customAuthenticationProvider")
public class CustomAuthenticationProvider extends BaseLogic implements AuthenticationProvider {
	
	@Value("${bsa.concurrentlogin.enabled:false}") private boolean allowConcurrentLogin; 
	
	@Override
	@Transactional(noRollbackFor={BadCredentialsException.class})
	public Authentication authenticate(Authentication authentication) throws AuthenticationException {
		
		String loginId = StringUtils.trim((String) authentication.getPrincipal());
		String password = (String) authentication.getCredentials();
		
		AuditContext auditContext = new AuditContext();
		auditContext.setCallerId(loginId);
		
		AmMsuser user = daoFactory.getUserDao().getUserByEmail(loginId);
		validateUserStatus(user, auditContext);
		
		if (!PasswordHash.validatePassword(password, user.getPassword())) {
			int failCount = user.getFailCount();
			String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_FAIL_COUNT);
			Integer maxFailCount =  Integer.valueOf(gsValue);
			failCount = (failCount < maxFailCount) ? failCount + 1 : failCount;
			
			user.setFailCount(failCount);
			user.setPrevLoggedFail(user.getLastLoggedFail());
			user.setLastLoggedFail(new Date());
			user.setDtmUpd(new Date());
			user.setUsrUpd(String.valueOf(user.getIdMsUser()));
			if (failCount >= maxFailCount) {
				user.setIsLocked("1");
				user.setLastLocked(new Date());
			}
			daoFactory.getUserDao().updateUser(user);
			
			throw new BadCredentialsException(getMessage("businesslogic.login.idpassword", null, auditContext));
		}
		
		if (!allowConcurrentLogin) {
			deleteAccessToken(user);
		}
		
		user.setFailCount(0);
		user.setIsLoggedIn("1");
		user.setPrevLoggedIn(user.getLastLoggedIn());
		user.setLastLoggedIn(new Date());
		user.setDtmUpd(new Date());
		user.setUsrUpd(String.valueOf(user.getIdMsUser()));
		daoFactory.getUserDao().updateUser(user);
			
		List<GrantedAuthority> auth = new ArrayList<>();
		auth.add(new SimpleGrantedAuthority("ROLE_APP"));
		
		HashMap<String, List<String>> tenantRoleMap = new HashMap<>();
		this.constructMapTenantRoles(tenantRoleMap, auth, user);
		return new BsaAuthenticationToken(loginId, password, auth, user, tenantRoleMap);
	}
	
	@Override
	public boolean supports(Class<?> authentication) {
		return true;
	}
	
	private void validateUserStatus(AmMsuser user, AuditContext auditContext) {
		if (user == null) {
			throw new BadCredentialsException(getMessage("businesslogic.login.idpassword", null, auditContext));
        }
		if (!"1".equals(user.getIsActive())) {
			throw new BadCredentialsException(getMessage("businesslogic.context.userinactive", null, auditContext));
		}
		
		if ("1".equals(user.getIsLocked())) {
			throw new BadCredentialsException(getMessage("businesslogic.context.useralreadylocked", null, auditContext));
		}
	}
	
	private void constructMapTenantRoles(HashMap<String, List<String>> mapTenantRole, List<GrantedAuthority> auth, AmMsuser user) {
		List<MsTenant> tenantList = daoFactory.getTenantDao().getListTenantByUser(user);

		for (MsTenant tenant : tenantList) {
			List<AmMsrole> roleList = daoFactory.getRoleDao().getListRoleByIdUserTenantCode(user.getIdMsUser(), tenant.getTenantCode());
			
			List<String> roleListTenantString = new ArrayList<>();

			for (AmMsrole role : roleList) {
				//new role based on menu
				List<AmMsmenu> menuList = daoFactory.getMenuDao().getMenuByIdRole(role.getIdMsRole());
				for (AmMsmenu menu : menuList) {
					String roleAuth = "ROLE_" + menu.getMenuCode();
					auth.add(new SimpleGrantedAuthority(roleAuth));
					roleListTenantString.add(roleAuth);
				}
			}
			mapTenantRole.put(tenant.getTenantCode(), roleListTenantString);
		}
	}
	
	private void deleteAccessToken(AmMsuser user) {
		List<OauthAccessToken> accessTokens = daoFactory.getAccessTokenDao().getAccessTokensByUsername(user.getLoginId());
		if (CollectionUtils.isNotEmpty(accessTokens)) {
			for (OauthAccessToken accessToken : accessTokens) {
				daoFactory.getAccessTokenDao().deleteAccessToken(accessToken);
			}
		}
	}

}
