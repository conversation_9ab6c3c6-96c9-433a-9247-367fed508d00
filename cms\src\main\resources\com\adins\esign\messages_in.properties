businesslogic.global.mandatory = {0} harus diisi
businesslogic.global.stringmaxlength = {0} tidak bisa melebihi {1} karakter
businesslogic.global.stringminlength = {0} harus setidaknya {1} karakter
businesslogic.global.arraymaxlength = Ukuran {0} tidak bisa lebih dari {1}
businesslogic.global.arrayminlength = Ukuran {0} tidak bisa kurang dari {1}
businesslogic.global.mustbefilledwith = {0} harus diisi {1}
businesslogic.global.numeric = {0} harus diisi angka
businesslogic.global.positivenumeric = {0} harus diisi angka positif
businesslogic.global.dateformat = {0} harus menggunakan format tanggal {1}
businesslogic.global.objectnotfound = {0} {1} tidak tercatat di sistem
businesslogic.global.objectnotfound1 = {0} tidak tercatat di sistem
businesslogic.global.objectnotfound2 = {0} tidak ditemukan di {1}
businesslogic.global.forbiddenaccess = Akses terlarang
businesslogic.global.forbiddenaccess1 = Aks<PERSON> terlarang ke {0}
businesslogic.global.alreadyused = {0} sudah digunakan
businesslogic.global.namealreadyused = Nama {0} sudah digunakan
businesslogic.global.duplicateobject = Objek duplikat ditemukan: {0}
businesslogic.global.objectalreadysaved = {0} sudah tersimpan di {1}
businesslogic.global.datainvalidstatus = Status {0} tidak bisa {1}
businesslogic.global.failedtogetossdocument = Gagal mengambil dokumen {0} dari OSS
businesslogic.global.currentlyprocessed = {0} sedang diproses

businesslogic.global.cannotbegreater = {0} tidak bisa lebih besar dari {1}

businesslogic.global.decimal.digit = {0} tidak bisa melebihi {1} digit
businesslogic.global.decimal.point = {0} tidak bisa memiliki {1} digit di belakang koma
businesslogic.global.decimal.maxvalue = {0} tidak bisa sama atau lebih besar dari {1}

businesslogic.global.string.numericonly = {0} harus berupa nilai numerik
businesslogic.global.string.alphabetonly = {0} harus berisi karakter alfabet
businesslogic.global.string.alphabetspaceonly = {0} harus berisi karakter alfabet atau spasi
businesslogic.global.string.alphanumericonly = {0} harus berisi karakter alfanumerik
businesslogic.global.string.alphanumericspaceonly = {0} harus berisi karakter alfanumerik atau spasi

businesslogic.global.date.beforetoday = Tanggal yang dipilih harus sebelum hari ini. Silakan pilih tanggal yang valid.

businesslogic.login.idpassword = Login gagal. Periksa kembali username dan kode akses
businesslogic.login.inactive = Login gagal. Username {0} tidak aktif
businesslogic.login.invalididpassword = Username atau kode akses tidak valid.
businesslogic.login.locked = Login gagal. Username {0} terkunci

AbstractUserDetailsAuthenticationProvider.badCredentials=Login gagal. Periksa kembali username dan kode akses

businesslogic.tenant.incorrectapikey = API key salah

businesslogic.context.useralreadylocked = Akun Anda telah terkunci karena terlalu banyak upaya login yang gagal

businesslogic.user.maxotpattempt = Anda telah meminta jumlah maksimum OTP yang diizinkan hari ini. Silakan coba lagi besok.
businesslogic.user.maxverifyotpattempt = Anda telah memasukkan OTP salah dengan jumlah yang diizinkan hari ini. Silakan coba lagi besok.
businesslogic.user.invalidotp = OTP tidak valid. Mohon cek dan coba kembali.
businesslogic.user.invalidpasswordformat = Password harus mengandung huruf kapital, huruf kecil, angka, dan karakter spesial
businesslogic.user.passwordmismatch = Password baru dan konfirmasi password tidak sama. Silakan coba kembali.

businesslogic.userval.emailusedbyother = Email {0} digunakan oleh pengguna lain
businesslogic.userval.phoneusedbyother = No HP {0} digunakan oleh pengguna lain
businesslogic.userval.emailnotfound = Email {0} tidak ditemukan di sistem
businesslogic.userval.phonenotfound = No HP {0} tidak ditemukan di sistem

businesslogic.supplierbuyergroup.invalidtype = Tipe tidak ditemukan. Mohon isi hanya dengan 'Supplier' atau 'Buyer' atau 'Related Parties'.
businesslogic.transactiontype.invalidtype = Tipe tidak ditemukan. Mohon isi hanya dengan 'Credit' atau 'Debit' atau 'Header' atau 'Summary'.
businesslogic.supplierbuyergroup.mustbeunassigned = Sub group harus dilepas dari main group dahulu

businesslogic.anomaly.transactionalreadysaved = Transaksi {0} sudah disimpan sebagai anomali
businesslogic.anomaly.unmappedrisk = Alasan anomali terpilih belum memiliki risiko

businesslogic.dashboard.currentlyconsolidating = {0} sudah sedang dikonsolidasi
businesslogic.dashboard.notyetconsolidated = {0} belum dikonsolidasi

businesslogic.ocrresult.stillprocessed = Bank statement masih diproses
businesslogic.ocrresult.completed = Bank statement sudah selesai diproses
businesslogic.ocrresult.cannotedit  = Status bank statement tidak bisa diubah
businesslogic.ocrresult.transactionoverlapped = Transaksi tumpang tindih
