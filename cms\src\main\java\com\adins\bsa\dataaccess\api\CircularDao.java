package com.adins.bsa.dataaccess.api;

import java.util.List;

import com.adins.bsa.custom.CircularBean;
import com.adins.bsa.custom.CircularTransactionBean;
import com.adins.bsa.custom.queryfilter.ListCircularFilter;
import com.adins.bsa.model.TrConsolidateResultCircularGroupingD;
import com.adins.bsa.model.TrConsolidateResultCircularGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface CircularDao {
	
	// tr_consolidate_result_circular_grouping_h
	void insertCircularGroupingH(TrConsolidateResultCircularGroupingH circularGroupingH);
	
	TrConsolidateResultCircularGroupingH getCircularGroupingH(TrDashboardGroupH dashboardGroupH, String groupName);
	TrConsolidateResultCircularGroupingH getCircularGroupingHIgnoreCase(TrDashboardGroupH dashboardGroupH, String groupName);
	
	void updateCircularGroupHIsDeleted(TrConsolidateResultCircularGroupingH circularGroupingH, String isDeleted, AuditContext audit);
	void deleteCircularGroupH(TrConsolidateResultCircularGroupingH circularGroupingH);
	
	List<CircularBean> getListCircular(ListCircularFilter filter);
	long countListCircular(ListCircularFilter filter);
	
	// tr_consolidate_result_circular_grouping_d
	void insertCircularGroupingD(TrConsolidateResultCircularGroupingD circularGroupingD);
	
	TrConsolidateResultCircularGroupingD getCircularGroupingD(TrDashboardGroupH dashboardGroupH, TrOcrResultDetail ocrResultDetail);
	
	void updateCircularGroupDsIsDeleted(TrConsolidateResultCircularGroupingH circularGroupingH, String isDeleted, AuditContext audit);
	void deleteCircularGroupDs(TrConsolidateResultCircularGroupingH circularGroupingH);
	
	List<CircularTransactionBean> getListCircularTransaction(TrConsolidateResultCircularGroupingH circularGroupingH, int min, int max);
}
