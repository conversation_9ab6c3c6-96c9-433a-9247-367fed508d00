package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.OcrResultLogic;
import com.adins.bsa.webservices.frontend.api.OcrResultService;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionFileResponse;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionRequest;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionResponse;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionSummaryResponse;
import com.adins.bsa.webservices.model.ocrresult.DeleteBankStatementRequest;
import com.adins.bsa.webservices.model.ocrresult.DeleteBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.EditBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.GetBankStatementTransactionSummaryCalculationRequest;
import com.adins.bsa.webservices.model.ocrresult.GetBankStatementTransactionSummaryCalculationResponse;
import com.adins.bsa.webservices.model.ocrresult.GetListBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.GetListBankStatementTransactionDetailResponse;
import com.adins.bsa.webservices.model.ocrresult.OcrResultListRequest;
import com.adins.bsa.webservices.model.ocrresult.OcrResultListResponse;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementHeaderRequest;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementSummaryRequest;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementTransactionDetailRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/ocrResult")
@Api(value = "OcrResultService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericOcrResultServiceEndpoint implements OcrResultService {
	
	@Autowired private OcrResultLogic ocrResultLogic;

	@Override
	@POST
	@Path("/s/list")
	public OcrResultListResponse getOcrResultList(OcrResultListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.getOcrResultList(request, audit);
	}

	@Override
	@POST
	@Path("/s/getBankStatementHeaderTransaction")
	public BankStatementHeaderTransactionResponse getBankStatementHeaderTransaction(BankStatementHeaderTransactionRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.getBankStatementHeaderTransaction(request, audit);
	}

	@Override
	@POST
	@Path("/s/getBankStatementHeaderTransactionFile")
	public BankStatementHeaderTransactionFileResponse getBankStatementHeaderTransactionFile(BankStatementHeaderTransactionRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.getBankStatementHeaderTransactionFile(request, audit);
	}

	@Override
	@POST
	@Path("/s/getBankStatementHeaderTransactionSummary")
	public BankStatementHeaderTransactionSummaryResponse getBankStatementHeaderTransactionSummary(BankStatementHeaderTransactionRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.getBankStatementHeaderTransactionSummary(request, audit);
	}

	@Override
	@POST
	@Path("/s/getListBankStatementTransactionDetail")
	public GetListBankStatementTransactionDetailResponse getListBankStatementTransactionDetail(GetListBankStatementTransactionDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.getListBankStatementTransactionDetail(request, audit);
	}

	@Override
	@POST
	@Path("/s/deleteBankStatement")
	public MssResponseType deleteBankStatement(DeleteBankStatementRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.deleteBankStatement(request, audit);
	}

	@Override
	@POST
	@Path("/s/saveBankStatementHeader")
	public MssResponseType saveBankStatementHeader(SaveBankStatementHeaderRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.saveBankStatementHeader(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/getCalculatedListBankStatementSummary")
	public GetBankStatementTransactionSummaryCalculationResponse getListBankStatementSummary(GetBankStatementTransactionSummaryCalculationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.getBankStatementTransactionSummaryCalculation(request, audit);
	}

	@Override
	@POST
	@Path("/s/saveBankStatementSummary")
	public MssResponseType saveBankStatementSummary(SaveBankStatementSummaryRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.saveBankStatementSummary(request, audit);
	}

	@Override
	@POST
	@Path("/s/saveBankStatementTransactionDetail")
	public MssResponseType saveBankStatementTransactionDetail(SaveBankStatementTransactionDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.saveBankStatementTransactionDetail(request, audit);
	}

	@Override
	@POST
	@Path("/s/deleteBankStatementTransactionDetail")
	public MssResponseType deleteBankStatementTransactionDetail(DeleteBankStatementTransactionDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.deleteBankStatementTransactionDetail(request, audit);
	}

	@Override
	@POST
	@Path("/s/editBankStatementTransactionDetail")
	public MssResponseType editBankStatementTransactionDetail(EditBankStatementTransactionDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return ocrResultLogic.editBankStatementTransactionDetail(request, audit);
	}

}
