package com.adins.bsa.webservices.model.supplierbuyergroup;

import java.util.List;

import com.adins.bsa.custom.EditSupplierBuyerSubGroupRequestBean;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class EditSupplierBuyerSubGroupRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	private String oldMainGroupName;
	private String newMainGroupName;
	private String mainGroupType;
	private List<EditSupplierBuyerSubGroupRequestBean> subGroups;
	
	public String getOldMainGroupName() {
		return oldMainGroupName;
	}
	public void setOldMainGroupName(String oldMainGroupName) {
		this.oldMainGroupName = oldMainGroupName;
	}
	public String getNewMainGroupName() {
		return newMainGroupName;
	}
	public void setNewMainGroupName(String newMainGroupName) {
		this.newMainGroupName = newMainGroupName;
	}
	public String getMainGroupType() {
		return mainGroupType;
	}
	public void setMainGroupType(String mainGroupType) {
		this.mainGroupType = mainGroupType;
	}
	public List<EditSupplierBuyerSubGroupRequestBean> getSubGroups() {
		return subGroups;
	}
	public void setSubGroups(List<EditSupplierBuyerSubGroupRequestBean> subGroups) {
		this.subGroups = subGroups;
	}
	
}
