package com.adins.bsa.businesslogic.impl;

import java.net.HttpURLConnection;
import java.nio.charset.StandardCharsets;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.businesslogic.api.FunctionComputeLogic;
import com.adins.bsa.webservices.model.eendigo.OcrBsaRequest;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.aliyuncs.fc.client.FunctionComputeClient;
import com.aliyuncs.fc.constants.Const;
import com.aliyuncs.fc.request.InvokeFunctionRequest;
import com.aliyuncs.fc.response.InvokeFunctionResponse;
import com.google.gson.Gson;

@Component
public class GenericFunctionComputeLogic extends BaseLogic implements FunctionComputeLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericFunctionComputeLogic.class);
	
	@Value("${alicloud.fc.eendigo.service}") private String serviceName;
	
	@Autowired private FunctionComputeClient functionComputeClient;
	@Autowired private Gson gson;
	
	private enum FunctionName {
		OCR_BSA("bsa");
		
		private final String name;
		
		private FunctionName(String name) {
			this.name = name;
		}
		
		@Override
		public String toString() {
			return this.name;
		}
	}
	
	private void invokeAsyncFuctionCompute(FunctionName functionName, String payload) {
		if (null == functionName) {
			LOG.warn("Asynchronous invocation not accepted (Function name is null)");
			return;
		}
		
		// Prepare request
		InvokeFunctionRequest invReq = new InvokeFunctionRequest(serviceName, functionName.toString());
		invReq.setInvocationType(Const.INVOCATION_TYPE_ASYNC);
		invReq.setPayload(payload.getBytes());
		
		// Invoke function
		InvokeFunctionResponse invRes = functionComputeClient.invokeFunction(invReq);
		if (HttpURLConnection.HTTP_ACCEPTED != invRes.getStatus()) {
			String responsePayload = new String(invRes.getContent(), StandardCharsets.UTF_8);
			throw new CommonException(responsePayload, ReasonCommon.CONNECTION_ISSUE);
		}
		
		LOG.info("Asynchronous invocation accepted, request ID: {}", invRes.getRequestId());
	}

	@Override
	public void invokeAsyncOcrBsa(OcrBsaRequest request) {
		String payload = gson.toJson(request);
		invokeAsyncFuctionCompute(FunctionName.OCR_BSA, payload);
	}
}
