package com.adins.exceptions;

import com.adins.framework.exception.StatusCodes;

public class StatusCode extends StatusCodes {
	
	public static final int UNKNOWN = 9999;
	
	// Common error code
	private static final int COMMON_ERROR_CODE = 2_000;
	public static final int MANDATORY_PARAM		= COMMON_ERROR_CODE + 1;
	public static final int NUMERIC_PARAM 		= COMMON_ERROR_CODE + 2;
	public static final int OBJECT_NOT_FOUND	= COMMON_ERROR_CODE + 3;
	public static final int INVALID_FORMAT		= COMMON_ERROR_CODE + 4;
	public static final int FORBIDDEN_ACCESS	= COMMON_ERROR_CODE + 5;
	public static final int NAME_ALREADY_USED	= COMMON_ERROR_CODE + 6;
	public static final int DUPLICATE_OBJECT	= COMMON_ERROR_CODE + 7;
	public static final int INVALID_VALUE		= COMMON_ERROR_CODE + 8;
	public static final int INVALID_CONDITION	= COMMON_ERROR_CODE + 9;
	public static final int CONNECTION_ISSUE	= COMMON_ERROR_CODE + 10;

}
