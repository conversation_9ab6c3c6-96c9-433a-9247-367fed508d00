package com.adins.bsa.businesslogic.api;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.bsa.webservices.model.dashboard.AddDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardAccountNoListResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardBankListResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardConsolidateStatusResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardPeriodListRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardPeriodListResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardWarningStatusResponse;
import com.adins.bsa.webservices.model.dashboard.DeleteDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;
import com.adins.bsa.webservices.model.dashboard.ListDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.ListDashboardResponse;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListTransactionnonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListTransactionnonBusinessTransactionGroupResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface DashboardLogic {
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	ListDashboardResponse getListDashboard(ListDashboardRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType deleteDashboard(DeleteDashboardRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType addDashboard(AddDashboardRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType addNewBankStatement(AddDashboardRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	DashboardBankListResponse getDashboardBankList(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	DashboardAccountNoListResponse getDashboardAccountNoList(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	DashboardPeriodListResponse getDashboardPeriodList(DashboardPeriodListRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	ListTransactionnonBusinessTransactionGroupResponse getListTransactionnonBusinessTransactionGroup(ListTransactionnonBusinessTransactionGroupRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType startConsolidate(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	DashboardConsolidateStatusResponse getConsolidateStatus(GenericDashboardDropdownListRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	DashboardWarningStatusResponse getDashboardWarningStatus(GenericDashboardDropdownListRequest request, AuditContext audit);
	
}
