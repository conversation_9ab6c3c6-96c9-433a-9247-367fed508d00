package com.adins.bsa.custom.queryfilter;

import com.adins.bsa.model.TrDashboardGroupH;

public class ListMasterSubGroupFilter {
	
	private TrDashboardGroupH dashboardGroupH;
	private String mainGroupName;
	private String subGroupName;
	private int min;
	private int max;
	
	public TrDashboardGroupH getDashboardGroupH() {
		return dashboardGroupH;
	}
	public void setDashboardGroupH(TrDashboardGroupH dashboardGroupH) {
		this.dashboardGroupH = dashboardGroupH;
	}
	public String getMainGroupName() {
		return mainGroupName;
	}
	public void setMainGroupName(String mainGroupName) {
		this.mainGroupName = mainGroupName;
	}
	public String getSubGroupName() {
		return subGroupName;
	}
	public void setSubGroupName(String subGroupName) {
		this.subGroupName = subGroupName;
	}
	public int getMin() {
		return min;
	}
	public void setMin(int min) {
		this.min = min;
	}
	public int getMax() {
		return max;
	}
	public void setMax(int max) {
		this.max = max;
	}
	
}
