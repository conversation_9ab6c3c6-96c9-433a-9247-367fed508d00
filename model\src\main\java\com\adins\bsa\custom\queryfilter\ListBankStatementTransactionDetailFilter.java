package com.adins.bsa.custom.queryfilter; 
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrOcrResultHeader;

public class ListBankStatementTransactionDetailFilter { 
	private String type;
	private String status;
	private TrOcrResultHeader trOcrResultHeader; 
	private TrDashboardGroupD trDashboardGroupD;
	private int min;
	private int max;
	
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public TrOcrResultHeader getTrOcrResultHeader() {
		return trOcrResultHeader;
	}
	public void setTrOcrResultHeader(TrOcrResultHeader trOcrResultHeader) {
		this.trOcrResultHeader = trOcrResultHeader;
	}
	public TrDashboardGroupD getTrDashboardGroupD() {
		return trDashboardGroupD;
	}
	public void setTrDashboardGroupD(TrDashboardGroupD trDashboardGroupD) {
		this.trDashboardGroupD = trDashboardGroupD;
	}
	public int getMin() {
		return min;
	}
	public void setMin(int min) {
		this.min = min;
	}
	public int getMax() {
		return max;
	}
	public void setMax(int max) {
		this.max = max;
	}
	
	
}
