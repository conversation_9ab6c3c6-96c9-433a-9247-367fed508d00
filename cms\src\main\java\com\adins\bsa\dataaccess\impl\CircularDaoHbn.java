package com.adins.bsa.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.custom.ActiveDeleteAndUpdateableEntity;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.custom.CircularBean;
import com.adins.bsa.custom.CircularTransactionBean;
import com.adins.bsa.custom.queryfilter.ListCircularFilter;
import com.adins.bsa.dataaccess.api.CircularDao;
import com.adins.bsa.model.TrConsolidateResultCircularGroupingD;
import com.adins.bsa.model.TrConsolidateResultCircularGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.model.TrOcrResultHeader;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
@Transactional
public class CircularDaoHbn extends BaseDaoHbn implements CircularDao {

	@Override
	@Transactional(readOnly = true)
	public List<CircularBean> getListCircular(ListCircularFilter filter) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("min", filter.getMin());
		params.put("max", filter.getMax());
		StringBuilder conditionalParam = buildListCircularConditionalParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by cgh.id_consolidate_result_circular_grouping_h asc) as num, ")
				.append("cgh.group_name as \"groupName\", orh.account_number as \"accountNo\", ")
				.append("orh.account_name as \"accountName\", cgh.rolling_window as \"transactionWindow\", ")
				.append("to_char(cgh.dtm_crt, 'DD/MM/YYYY HH24:MI') as \"createDate\" ")
				.append("from tr_consolidate_result_circular_grouping_h cgh ")
				.append("join tr_ocr_result_header orh on cgh.id_ocr_result_header = orh.id_ocr_result_header ")
				.append("where 1=1 ")
				.append(conditionalParam)
			.append(") ")
			.append("select * from cte ")
			.append("where num between :min and :max ");
		
		return managerDAO.selectForListString(CircularBean.class, query.toString(), params, null);
	}

	@Override
	@Transactional(readOnly = true)
	public long countListCircular(ListCircularFilter filter) {
		
		Map<String, Object> params = new HashMap<>();
		StringBuilder conditionalParam = buildListCircularConditionalParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_consolidate_result_circular_grouping_h cgh ")
			.append("join tr_ocr_result_header orh on cgh.id_ocr_result_header = orh.id_ocr_result_header ")
			.append("where 1=1 ")
			.append(conditionalParam);
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0L;
		}
		
		return result.longValue();
	}
	
	private StringBuilder buildListCircularConditionalParam(ListCircularFilter filter, Map<String, Object> params) {
		StringBuilder builder = new StringBuilder();
		
		// dashboard param
		builder.append("and cgh.id_dashboard_group_h = :idDashboardGroupH ");
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, filter.getDashboardGroupH().getIdDashboardGroupH());
		
		// is_deleted param
		builder.append("and cgh.is_deleted = '0' ");
		
		// account number / tr_ocr_result_header param
		if (StringUtils.isNotBlank(filter.getAccountNo())) {
			builder.append("and orh.account_number = :accountNumber ");
			params.put(TrOcrResultHeader.PARAM_ACCOUNT_NUMBER, filter.getAccountNo());
		}
		
		// window param
		if (null != filter.getTransactionWindow()) {
			builder.append("and cgh.rolling_window = :rollingWindow ");
			params.put("rollingWindow", filter.getTransactionWindow());
		}
		
		return builder;
	}

	@Override
	public TrConsolidateResultCircularGroupingH getCircularGroupingHIgnoreCase(TrDashboardGroupH dashboardGroupH, String groupName) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_DASHBOARD_GROUP_H, dashboardGroupH);
		params.put("groupName", StringUtils.upperCase(groupName));
		
		return managerDAO.selectOne(
				"from TrConsolidateResultCircularGroupingH cgh "
				+ "where cgh.trDashboardGroupH = :dashboardGroupH "
				+ "and upper(cgh.groupName) = :groupName "
				+ "and cgh.isDeleted = '0' ", params);
	}

	@Override
	public TrConsolidateResultCircularGroupingH getCircularGroupingH(TrDashboardGroupH dashboardGroupH, String groupName) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_DASHBOARD_GROUP_H, dashboardGroupH);
		params.put("groupName", groupName);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultCircularGroupingH cgh "
				+ "where cgh.trDashboardGroupH = :dashboardGroupH "
				+ "and cgh.groupName = :groupName "
				+ "and cgh.isDeleted = '0' ", params);
	}

	@Override
	public void deleteCircularGroupH(TrConsolidateResultCircularGroupingH circularGroupingH) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultCircularGroupingH.KEY_ID_CIRC_GROUP_H, circularGroupingH.getIdConsolidateResultCircularGroupingH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("delete from tr_consolidate_result_circular_grouping_h ")
			.append("where id_consolidate_result_circular_grouping_h = :idCircularGroupH ");
		
		managerDAO.deleteNativeString(query.toString(), params);
		
	}

	@Override
	public void deleteCircularGroupDs(TrConsolidateResultCircularGroupingH circularGroupingH) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultCircularGroupingH.KEY_ID_CIRC_GROUP_H, circularGroupingH.getIdConsolidateResultCircularGroupingH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("delete from tr_consolidate_result_circular_grouping_d ")
			.append("where id_consolidate_result_circular_grouping_h = :idCircularGroupH ");
		
		managerDAO.deleteNativeString(query.toString(), params);
	}

	@Override
	public void updateCircularGroupHIsDeleted(TrConsolidateResultCircularGroupingH circularGroupingH, String isDeleted, AuditContext audit) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultCircularGroupingH.KEY_ID_CIRC_GROUP_H, circularGroupingH.getIdConsolidateResultCircularGroupingH());
		params.put(ActiveDeleteAndUpdateableEntity.IS_DELETED_HBM, isDeleted);
		params.put("usrUpd", audit.getCallerId());
		
		StringBuilder query = new StringBuilder();
		query
			.append("update tr_consolidate_result_circular_grouping_h ")
			.append("set is_deleted = :isDeleted , ")
			.append("is_user_edited = '1' , ")
			.append("usr_upd = :usrUpd , ")
			.append("dtm_upd = now() ")
			.append("where id_consolidate_result_circular_grouping_h = :idCircularGroupH ");
		
		managerDAO.updateNativeString(query.toString(), params);
		
	}

	@Override
	public void updateCircularGroupDsIsDeleted(TrConsolidateResultCircularGroupingH circularGroupingH, String isDeleted, AuditContext audit) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultCircularGroupingH.KEY_ID_CIRC_GROUP_H, circularGroupingH.getIdConsolidateResultCircularGroupingH());
		params.put(ActiveDeleteAndUpdateableEntity.IS_DELETED_HBM, isDeleted);
		params.put("usrUpd", audit.getCallerId());
		
		StringBuilder query = new StringBuilder();
		query
			.append("update tr_consolidate_result_circular_grouping_d ")
			.append("set is_deleted = :isDeleted , ")
			.append("is_user_edited = '1' , ")
			.append("usr_upd = :usrUpd , ")
			.append("dtm_upd = now() ")
			.append("where id_consolidate_result_circular_grouping_h = :idCircularGroupH ");
		
		managerDAO.updateNativeString(query.toString(), params);
		
	}

	@Override
	public TrConsolidateResultCircularGroupingD getCircularGroupingD(TrDashboardGroupH dashboardGroupH, TrOcrResultDetail ocrResultDetail) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("dashboardGroupH", dashboardGroupH);
		params.put("ocrResultDetail", ocrResultDetail);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultCircularGroupingD cgd "
				+ "join fetch cgd.trConsolidateResultCircularGroupingH cgh "
				+ "where cgh.trDashboardGroupH = :dashboardGroupH "
				+ "and cgd.trOcrResultDetail = :ocrResultDetail "
				+ "and cgd.isDeleted = '0' ", params);
	}

	@Override
	public List<CircularTransactionBean> getListCircularTransaction(TrConsolidateResultCircularGroupingH circularGroupingH, int min, int max) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("groupingH", circularGroupingH.getIdConsolidateResultCircularGroupingH());
		params.put(GlobalKey.PAGING_PARAM_MIN, min);
		params.put(GlobalKey.PAGING_PARAM_MAX, max);
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by ord.transaction_date asc, ord.id_ocr_result_detail asc) as num, ")
				.append("dgd.file_name as filename, orh.account_number as \"accountNo\", ")
				.append("to_char(ord.transaction_date, 'DD/MM/YYYY HH24:MI') as \"trxDate\", ")
				.append("ord.transaction_desc as description, ord.transaction_amount as amount, ord.transaction_type as type ")
				.append("from tr_consolidate_result_circular_grouping_d cgd ")
				.append("join tr_ocr_result_detail ord on cgd.id_ocr_result_detail = ord.id_ocr_result_detail ")
				.append("join tr_dashboard_group_d dgd on ord.id_dashboard_group_d = dgd.id_dashboard_group_d ")
				.append("join tr_ocr_result_header orh on ord.id_ocr_result_header = orh.id_ocr_result_header ")
				.append("where cgd.id_consolidate_result_circular_grouping_h = :groupingH ")
			.append(") ")
			.append("select * from cte ")
			.append("where num between :min and :max ");
		
		return managerDAO.selectForListString(CircularTransactionBean.class, query.toString(), params, null);
	}

	@Override
	public void insertCircularGroupingH(TrConsolidateResultCircularGroupingH circularGroupingH) {
		managerDAO.insert(circularGroupingH);
	}

	@Override
	public void insertCircularGroupingD(TrConsolidateResultCircularGroupingD circularGroupingD) {
		managerDAO.insert(circularGroupingD);
	}

}
