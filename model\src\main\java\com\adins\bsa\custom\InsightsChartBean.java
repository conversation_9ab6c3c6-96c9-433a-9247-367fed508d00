package com.adins.bsa.custom;

import java.io.Serializable;
import java.util.List;

public class InsightsChartBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private String label;
	private transient List<Object> data;
	
	public InsightsChartBean() {
	}
	
	public InsightsChartBean(String label, List<Object> data) {
		super();
		this.label = label;
		this.data = data;
	}
	
	public String getLabel() {
		return label;
	}
	public void setLabel(String label) {
		this.label = label;
	}
	public List<Object> getData() {
		return data;
	}
	public void setData(List<Object> data) {
		this.data = data;
	}
	
}
