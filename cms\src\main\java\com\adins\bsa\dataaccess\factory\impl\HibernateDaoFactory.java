package com.adins.bsa.dataaccess.factory.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.dataaccess.api.AnomalyDao;
import com.adins.bsa.dataaccess.api.BusinessTransactionGroupDao;
import com.adins.bsa.dataaccess.api.CircularDao;
import com.adins.bsa.dataaccess.api.CommonDao;
import com.adins.bsa.dataaccess.api.DashboardAuditLogDao;
import com.adins.bsa.dataaccess.api.DashboardGroupDao;
import com.adins.bsa.dataaccess.api.DashboardTabDao;
import com.adins.bsa.dataaccess.api.GeneralSettingDao;
import com.adins.bsa.dataaccess.api.LovDao;
import com.adins.bsa.dataaccess.api.MenuDao;
import com.adins.bsa.dataaccess.api.MessageTemplateDao;
import com.adins.bsa.dataaccess.api.NonBusinessTransactionGroupDao;
import com.adins.bsa.dataaccess.api.OauthAccessTokenDao;
import com.adins.bsa.dataaccess.api.OcrResultDao;
import com.adins.bsa.dataaccess.api.RoleDao;
import com.adins.bsa.dataaccess.api.SupplierBuyerGroupDao;
import com.adins.bsa.dataaccess.api.TenantDao;
import com.adins.bsa.dataaccess.api.UserDao;
import com.adins.bsa.dataaccess.api.UseroftenantDao;
import com.adins.bsa.dataaccess.factory.api.DaoFactory;

@Component
public class HibernateDaoFactory implements DaoFactory {
	
	@Autowired private CommonDao commonDao;
	@Autowired private DashboardGroupDao dashboardGroupDao;
	@Autowired private NonBusinessTransactionGroupDao nonBusinessTransactionGroupDao;
	@Autowired private GeneralSettingDao generalSettingDao;
	@Autowired private LovDao lovDao;
	@Autowired private MenuDao menuDao;
	@Autowired private RoleDao roleDao;
	@Autowired private OauthAccessTokenDao accessTokenDao;
	@Autowired private OcrResultDao ocrResultDao;
	@Autowired private TenantDao tenantDao;
	@Autowired private UserDao userDao;
	@Autowired private SupplierBuyerGroupDao supplierBuyerGroupDao;
	@Autowired private AnomalyDao anomalyDao;
	@Autowired private MessageTemplateDao messageTemplateDao;
	@Autowired private CircularDao circularDao;
	@Autowired private BusinessTransactionGroupDao businessTransactionGroupDao;
	@Autowired private DashboardAuditLogDao dashboardAuditLogDao;
	@Autowired private UseroftenantDao useroftenantDao;
	@Autowired private DashboardTabDao dashboardTabDao;
	
	@Override
	public MenuDao getMenuDao() {
		return menuDao;
	}
	@Override
	public UserDao getUserDao() {
		return userDao;
	}
	@Override
	public RoleDao getRoleDao() {
		return roleDao;
	}
	@Override
	public CommonDao getCommonDao() {
		return commonDao;
	}
	@Override
	public TenantDao getTenantDao() {
		return tenantDao;
	}
	@Override
	public GeneralSettingDao getGeneralSettingDao() {
		return generalSettingDao;
	}
	@Override
	public OauthAccessTokenDao getAccessTokenDao() {
		return accessTokenDao;
	}
	@Override
	public DashboardGroupDao getDashboardGroupDao() {
		return dashboardGroupDao;
	}
	@Override
	public NonBusinessTransactionGroupDao getNonBusinessTransactionGroupDao() {
		return nonBusinessTransactionGroupDao;
	}
	@Override
	public LovDao getLovDao() {
		return lovDao;
	}
	@Override
	public OcrResultDao getOcrResultDao() {
		return ocrResultDao;
	}
	@Override
	public SupplierBuyerGroupDao getSupplierBuyerDao() {
		return supplierBuyerGroupDao;
	}
	@Override
	public AnomalyDao getAnomalyDao() {
		return anomalyDao;
	}
	@Override
	public MessageTemplateDao getMessageTemplateDao() {
		return messageTemplateDao;
	}
	@Override
	public CircularDao getCircularDao() {
		return circularDao;
	}
	@Override
	public BusinessTransactionGroupDao getBusinessTransactionGroupDao() {
		return businessTransactionGroupDao;
	}
	@Override
	public DashboardAuditLogDao getDashboardAuditLogDao() {
		return dashboardAuditLogDao;
	}
	@Override
	public UseroftenantDao getUseroftenantDao() {
		return useroftenantDao;
	}
	@Override
	public DashboardTabDao getDashboardTabDao() {
		return dashboardTabDao;
	}
	
}
