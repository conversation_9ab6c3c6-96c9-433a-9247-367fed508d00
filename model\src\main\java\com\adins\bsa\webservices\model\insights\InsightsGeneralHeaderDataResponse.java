package com.adins.bsa.webservices.model.insights;

import java.math.BigDecimal;
import java.math.BigInteger;

import com.adins.framework.service.base.model.MssResponseType;

public class InsightsGeneralHeaderDataResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private BigDecimal openingBalance;
	private BigDecimal endingbalance;
	private BigDecimal overallCredit;
	private BigDecimal overallDebit;
	private BigInteger creditCount;
	private BigInteger debitCount;
	public BigDecimal getOpeningBalance() {
		return openingBalance;
	}
	public void setOpeningBalance(BigDecimal openingBalance) {
		this.openingBalance = openingBalance;
	}
	public BigDecimal getEndingbalance() {
		return endingbalance;
	}
	public void setEndingbalance(BigDecimal endingbalance) {
		this.endingbalance = endingbalance;
	}
	public BigDecimal getOverallCredit() {
		return overallCredit;
	}
	public void setOverallCredit(BigDecimal overallCredit) {
		this.overallCredit = overallCredit;
	}
	public BigDecimal getOverallDebit() {
		return overallDebit;
	}
	public void setOverallDebit(BigDecimal overallDebit) {
		this.overallDebit = overallDebit;
	}
	public BigInteger getCreditCount() {
		return creditCount;
	}
	public void setCreditCount(BigInteger creditCount) {
		this.creditCount = creditCount;
	}
	public BigInteger getDebitCount() {
		return debitCount;
	}
	public void setDebitCount(BigInteger debitCount) {
		this.debitCount = debitCount;
	}
	
}
