package com.adins.bsa.businesslogic.impl;

import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.businesslogic.api.CloudStorageLogic;
import com.adins.bsa.businesslogic.api.SetupLoadTestLogic;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.LoadTestSetupAddDashboardResponseBean;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.util.MssTool;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.bsa.webservices.model.loadtest.SetupAddDashboardLoadTestRequest;
import com.adins.bsa.webservices.model.loadtest.SetupAddDashboardLoadTestResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListBankStatementResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListDashboardNameResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListEmailResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListObjectResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericSetupLoadTestLogic extends BaseLogic implements SetupLoadTestLogic {
	
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private TenantValidator tenantValidator;
	@Autowired private UserValidator userValidator;

	@Override
	public SetupAddDashboardLoadTestResponse setupAddDashboard(SetupAddDashboardLoadTestRequest request, AuditContext audit) {
		
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		
		if (request.getIteration() <= 0) {
			return new SetupAddDashboardLoadTestResponse();
		}
		
		if (request.getVus() <= 0) {
			return new SetupAddDashboardLoadTestResponse();
		}
		
		byte[] documentByteArray = Base64.getDecoder().decode(request.getBase64Pdf());
		
		List<List<LoadTestSetupAddDashboardResponseBean>> loadTestDocuments = new ArrayList<>();
		for (int i = 0; i < request.getIteration(); i++) {
			
			List<LoadTestSetupAddDashboardResponseBean> documents = new ArrayList<>();
			for (int j = 0; j < request.getVus(); j++) {
				
				String filename = "LOAD_TEST_DOCUMENT_" + j + ".pdf";
				String ossFilename = UUID.randomUUID().toString() + "_" + user.getLoginId() + "_" + filename;
				cloudStorageLogic.storeTemporaryDashboardFile(ossFilename, documentByteArray);
				
				LoadTestSetupAddDashboardResponseBean documentBean = new LoadTestSetupAddDashboardResponseBean();
				documentBean.setFilename(filename);
				documentBean.setOssFilename(ossFilename);
				documents.add(documentBean);
			}
			
			loadTestDocuments.add(documents);
		}
		
		SetupAddDashboardLoadTestResponse response = new SetupAddDashboardLoadTestResponse();
		response.setDocuments(loadTestDocuments);
		return response;
	}

	@Override
	public SetupListDashboardNameResponse getLatestActiveDashboardNames(SetupAddDashboardLoadTestRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		int totalData = request.getIteration() * request.getVus();
		
		List<Map<String, Object>> result = daoFactory.getDashboardGroupDao().getLatestDashboardNames(tenant, "1", totalData);
		List<String> dashboardNames = new ArrayList<>();
		
		for (Map<String, Object> row : result) {
			dashboardNames.add((String) row.get("d0"));
		}
		
		SetupListDashboardNameResponse response = new SetupListDashboardNameResponse();
		response.setDashboardNames(dashboardNames);
		return response;
	}

	@Override
	public SetupAddDashboardLoadTestResponse setupAddNewBankStatement(SetupAddDashboardLoadTestRequest request, AuditContext audit) {
		AmMsuser user = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		
		if (request.getIteration() <= 0) {
			return new SetupAddDashboardLoadTestResponse();
		}
		
		if (request.getVus() <= 0) {
			return new SetupAddDashboardLoadTestResponse();
		}
		
		byte[] documentByteArray = Base64.getDecoder().decode(request.getBase64Pdf());
		Date currentTime = new Date();
		
		List<List<LoadTestSetupAddDashboardResponseBean>> loadTestDocuments = new ArrayList<>();
		for (int i = 0; i < request.getIteration(); i++) {
			
			List<LoadTestSetupAddDashboardResponseBean> documents = new ArrayList<>();
			for (int j = 0; j < request.getVus(); j++) {
				String timestamp = MssTool.formatDateToStringIn(currentTime, GlobalVal.DATE_TIME_FORMAT_SEC_NO_SPACE);
				String filename = "NEW_DOCUMENT_" + timestamp + "_" + i + "_" + j + ".pdf";
				String ossFilename = UUID.randomUUID().toString() + "_" + user.getLoginId() + "_" + filename;
				cloudStorageLogic.storeTemporaryDashboardFile(ossFilename, documentByteArray);
				
				LoadTestSetupAddDashboardResponseBean documentBean = new LoadTestSetupAddDashboardResponseBean();
				documentBean.setFilename(filename);
				documentBean.setOssFilename(ossFilename);
				documents.add(documentBean);
			}
			
			loadTestDocuments.add(documents);
		}
		
		SetupAddDashboardLoadTestResponse response = new SetupAddDashboardLoadTestResponse();
		response.setDocuments(loadTestDocuments);
		return response;
	}

	@Override
	public SetupListDashboardNameResponse getLatestConsolidateReadyDashboards(SetupAddDashboardLoadTestRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		int totalData = request.getIteration() * request.getVus();
		
		List<Map<String, Object>> result = daoFactory.getDashboardGroupDao().getLatestDashboardNames(tenant, "1", totalData);
		if (totalData != result.size()) {
			// Hardcode message karena hanya untuk setup loadtest
			throw new CommonException("Invalid size of returned data", ReasonCommon.INVALID_CONDITION);
		}
		
		daoFactory.getDashboardGroupDao().flagDashboardGroupAsCompleted(tenant, "1", totalData);
		
		List<String> dashboardNames = new ArrayList<>();
		
		for (Map<String, Object> row : result) {
			dashboardNames.add((String) row.get("d0"));
		}
		
		SetupListDashboardNameResponse response = new SetupListDashboardNameResponse();
		response.setDashboardNames(dashboardNames);
		return response;
	}

	@Override
	public SetupListEmailResponse getLatestLoadTestEmails(SetupAddDashboardLoadTestRequest request, AuditContext audit) {
		
		if (request.getIteration() <= 0 || request.getVus() <= 0) {
			return new SetupListEmailResponse();
		}
		
		int totalData = request.getIteration() * request.getVus();
		List<Map<String, Object>> result = daoFactory.getUserDao().getEmailsForLoadTest(totalData);
		List<String> emails = new ArrayList<>();
		
		for (Map<String, Object> row : result) {
			emails.add((String) row.get("d0"));
		}
		
		SetupListEmailResponse response = new SetupListEmailResponse();
		response.setEmails(emails);
		return response;
	}

	@Override
	public SetupListEmailResponse getLatestEmailsWithOtp(SetupAddDashboardLoadTestRequest request, AuditContext audit) {
		if (request.getIteration() <= 0 || request.getVus() <= 0) {
			return new SetupListEmailResponse();
		}
		
		int totalData = request.getIteration() * request.getVus();
		List<Map<String, Object>> result = daoFactory.getUserDao().getEmailsForLoadTest(totalData);
		List<String> emails = new ArrayList<>();
		List<String> otps = new ArrayList<>();
		
		for (Map<String, Object> row : result) {
			emails.add((String) row.get("d0"));
			otps.add((String) row.get("d1"));
		}
		
		SetupListEmailResponse response = new SetupListEmailResponse();
		response.setEmails(emails);
		response.setOtps(otps);
		return response;
	}

	@Override
	public SetupListBankStatementResponse getLatestDeletableBankStatements(SetupAddDashboardLoadTestRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		int totalData = request.getIteration() * request.getVus();
		
		List<Map<String, Object>> result = daoFactory.getDashboardGroupDao().getLatestDeletableBankStatements(tenant, totalData);
		List<String> dashboardNames = new ArrayList<>();
		List<String> bankStatements = new ArrayList<>();
		
		for (Map<String, Object> row : result) {
			dashboardNames.add((String) row.get("d0"));
			bankStatements.add((String) row.get("d1"));
		}
		
		SetupListBankStatementResponse response = new SetupListBankStatementResponse();
		response.setDashboardNames(dashboardNames);
		response.setBankStatements(bankStatements);
		return response;
	}

	@Override
	public SetupListObjectResponse getLatestBankStatementHeaders(SetupAddDashboardLoadTestRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		int totalData = request.getIteration() * request.getVus();
		
		List<Map<String, Object>> result = daoFactory.getOcrResultDao().getLatestBankStatementHeaders(tenant, totalData);
		SetupListObjectResponse response = new SetupListObjectResponse();
		response.setData(result);
		return response;
	}

	@Override
	public SetupListObjectResponse getLatestOcrResultDetails(SetupAddDashboardLoadTestRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		int totalData = request.getIteration() * request.getVus();
		
		List<Map<String, Object>> result = daoFactory.getOcrResultDao().getLatestOcrResultDetails(tenant, totalData);
		SetupListObjectResponse response = new SetupListObjectResponse();
		response.setData(result);
		return response;
	}

}
