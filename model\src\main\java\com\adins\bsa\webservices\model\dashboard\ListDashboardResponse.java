package com.adins.bsa.webservices.model.dashboard;

import java.util.List;

import com.adins.bsa.custom.DashboardBean;
import com.adins.framework.service.base.model.MssResponseType;

public class ListDashboardResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<DashboardBean> dashboards;
	private int page;
	private long totalPage;
	private long totalResult;
	public List<DashboardBean> getDashboards() {
		return dashboards;
	}
	public void setDashboards(List<DashboardBean> dashboards) {
		this.dashboards = dashboards;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public long getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}
	public long getTotalResult() {
		return totalResult;
	}
	public void setTotalResult(long totalResult) {
		this.totalResult = totalResult;
	}
	
}
