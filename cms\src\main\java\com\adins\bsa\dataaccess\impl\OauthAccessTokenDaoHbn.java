package com.adins.bsa.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.OauthAccessToken;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.dataaccess.api.OauthAccessTokenDao;

@Component
@Transactional
public class OauthAccessTokenDaoHbn extends BaseDaoHbn implements OauthAccessTokenDao {

	@Override
	public void deleteAccessToken(OauthAccessToken token) {
		managerDAO.delete(token);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<OauthAccessToken> getAccessTokensByUsername(String username) {
		Map<String, Object> params = new HashMap<>();
		params.put("username", StringUtils.upperCase(username));
		
		return (List<OauthAccessToken>) managerDAO.list(
				"from OauthAccessToken oat "
				+ "where UPPER(username) = :username ", params).get(AmGlobalKey.MAP_RESULT_LIST);
	}

}
