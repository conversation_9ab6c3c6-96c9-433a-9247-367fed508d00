package com.adins.bsa.webservices.model.user;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.framework.service.base.model.MssRequestType;

public class ResetPasswordRequest extends MssRequestType {
	
	private static final long serialVersionUID = 1L;

	@ValidationObjectName("Login ID")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 80)
	private String loginId;
	
	@ValidationObjectName("OTP")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 6)
	private String otp;
	
	@ValidationObjectName("New password")
	@Required(allowBlankString = false)
	@StringLength(minValue = 8, maxValue = 50)
	private String newPassword;
	
	@ValidationObjectName("New password confirmation")
	@Required(allowBlankString = false)
	@StringLength(minValue = 8, maxValue = 50)
	private String confirmNewPassword;
	
	public String getLoginId() {
		return loginId;
	}
	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}
	public String getOtp() {
		return otp;
	}
	public void setOtp(String otp) {
		this.otp = otp;
	}
	public String getNewPassword() {
		return newPassword;
	}
	public void setNewPassword(String newPassword) {
		this.newPassword = newPassword;
	}
	public String getConfirmNewPassword() {
		return confirmNewPassword;
	}
	public void setConfirmNewPassword(String confirmNewPassword) {
		this.confirmNewPassword = confirmNewPassword;
	}
	
}
