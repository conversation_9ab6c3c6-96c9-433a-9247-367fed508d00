package com.adins.bsa.webservices.model.circular;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;

public class CheckCircularGroupNameRequest extends GenericDashboardPagingRequest {
	
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("Group name")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 64)
	private String groupName;

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

}
