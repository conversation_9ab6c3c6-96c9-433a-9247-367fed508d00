package com.adins.bsa.webservices.model.insights;

import java.util.List;
import java.util.Map;

import com.adins.bsa.custom.InsightsChartBean;
import com.adins.framework.service.base.model.MssResponseType;

public class GeneralDailyAnalysisDataResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<String> labels;
	private List<InsightsChartBean> endingBalance;
	private transient List<Map<String, Object>> endingBalanceDetails;
	private List<InsightsChartBean> cashFlow;
	private transient List<Map<String, Object>> cashFlowDetails;
	
	public List<String> getLabels() {
		return labels;
	}
	public void setLabels(List<String> labels) {
		this.labels = labels;
	}
	public List<InsightsChartBean> getEndingBalance() {
		return endingBalance;
	}
	public void setEndingBalance(List<InsightsChartBean> endingBalance) {
		this.endingBalance = endingBalance;
	}
	public List<Map<String, Object>> getEndingBalanceDetails() {
		return endingBalanceDetails;
	}
	public void setEndingBalanceDetails(List<Map<String, Object>> endingBalanceDetails) {
		this.endingBalanceDetails = endingBalanceDetails;
	}
	public List<InsightsChartBean> getCashFlow() {
		return cashFlow;
	}
	public void setCashFlow(List<InsightsChartBean> cashFlow) {
		this.cashFlow = cashFlow;
	}
	public List<Map<String, Object>> getCashFlowDetails() {
		return cashFlowDetails;
	}
	public void setCashFlowDetails(List<Map<String, Object>> cashFlowDetails) {
		this.cashFlowDetails = cashFlowDetails;
	}
	
}
