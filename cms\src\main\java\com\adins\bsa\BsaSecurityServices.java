package com.adins.bsa;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.stereotype.Component;
import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.businesslogic.impl.provider.BsaAuthenticationToken;

@SuppressWarnings("deprecation")
@Component("bsaSecurityServices")
public class BsaSecurityServices extends BaseLogic {
	
	public boolean isValidUser(String email, Authentication authentication) {
		String authenticatedLoginId = (String) authentication.getPrincipal();
		if (StringUtils.isBlank(email)) {
			return false;
		}
		
		return authenticatedLoginId.equalsIgnoreCase(email);
	}
	
	public boolean isValidUserTenant(String email, String tenantCode, Authentication authentication) {
		if (StringUtils.isBlank(email)) {
			return false;
		}
		
		if (StringUtils.isBlank(tenantCode)) {
			return false;
		}
		
		String authenticatedLoginId = (String) authentication.getPrincipal();
		if (!authenticatedLoginId.equalsIgnoreCase(email)) {
			return false;
		}
		
		OAuth2Authentication auth = (OAuth2Authentication) authentication;
		BsaAuthenticationToken bsaToken = (BsaAuthenticationToken) auth.getUserAuthentication();
		Map<String, List<String>> mapTenantRole = bsaToken.getMapTenantRole();
		
		return mapTenantRole.containsKey(tenantCode);
	}
	
	public boolean isValidTenantRole(String role, String tenantCode, Authentication authentication) {
		if (StringUtils.isBlank(role) || StringUtils.isBlank(tenantCode)) {
			return false;
		}
		
		OAuth2Authentication auth = (OAuth2Authentication) authentication;
		BsaAuthenticationToken bsaToken = (BsaAuthenticationToken) auth.getUserAuthentication();
		Map<String, List<String>> tenantRoleMap = bsaToken.getMapTenantRole();
		List<String> roleList = tenantRoleMap.get(tenantCode);
		return roleList.contains(role);
	}

}
