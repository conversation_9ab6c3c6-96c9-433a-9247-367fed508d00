package com.adins.bsa.validator.api;

import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface DashboardValidator {
	TrDashboardGroupH getDashboardGroupH(MsTenant tenant, String dashboardName, boolean objectMustExists, AuditContext audit);
	TrDashboardGroupH getDashboardGroupHNewTrx(MsTenant tenant, String dashboardName, boolean objectMustExists, AuditContext audit);
	
	TrDashboardGroupD getDashboardGroupD(TrDashboardGroupH groupH, String fileSourcePath, boolean objectMustExists, AuditContext audit);
	TrDashboardGroupD getDashboardGroupDNewTrx(TrDashboardGroupH groupH, String fileSourcePath, boolean objectMustExists, AuditContext audit);
	TrDashboardGroupD getDashboardGroupD(MsTenant tenant, String fileSourcePath, boolean objectMustExists, AuditContext audit);
}
