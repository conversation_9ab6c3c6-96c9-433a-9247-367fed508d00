package com.adins.bsa.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "tr_consolidate_result_supplier_buyer_group_h")
public class TrConsolidateResultSupplierBuyerGroupH extends UpdateableEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private long idConsolidateResultSupplierBuyerGroupH;
	private TrDashboardGroupH trDashboardGroupH;
	private String groupType;
	private String groupName;
	private String isDeleted;
	
	public TrConsolidateResultSupplierBuyerGroupH() {}
	
	public TrConsolidateResultSupplierBuyerGroupH(long idConsolidateResultSupplierBuyerGroupH,
			TrDashboardGroupH trDashboardGroupH, String groupType, String groupName, String isDeleted) {
		super();
		this.idConsolidateResultSupplierBuyerGroupH = idConsolidateResultSupplierBuyerGroupH;
		this.trDashboardGroupH = trDashboardGroupH;
		this.groupType = groupType;
		this.groupName = groupName;
		this.isDeleted = isDeleted;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_consolidate_result_supplier_buyer_group_h", unique = true, nullable = false)
	public long getIdConsolidateResultSupplierBuyerGroupH() {
		return idConsolidateResultSupplierBuyerGroupH;
	}

	public void setIdConsolidateResultSupplierBuyerGroupH(long idConsolidateResultSupplierBuyerGroupH) {
		this.idConsolidateResultSupplierBuyerGroupH = idConsolidateResultSupplierBuyerGroupH;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_h", nullable = false)
	public TrDashboardGroupH getTrDashboardGroupH() {
		return trDashboardGroupH;
	}

	public void setTrDashboardGroupH(TrDashboardGroupH trDashboardGroupH) {
		this.trDashboardGroupH = trDashboardGroupH;
	}

	
	@Column(name = "group_type", length = 64)
	public String getGroupType() {
		return groupType;
	}

	public void setGroupType(String groupType) {
		this.groupType = groupType;
	}

	@Column(name = "group_name", length = 64)
	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	@Column(name = "is_deleted", length = 1)
	public String getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}
	
}
