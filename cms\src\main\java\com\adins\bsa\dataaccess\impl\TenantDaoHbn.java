package com.adins.bsa.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.TenantBean;
import com.adins.bsa.custom.queryfilter.ListTenantFilter;
import com.adins.bsa.dataaccess.api.TenantDao;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.MsUseroftenant;

@Transactional
@Component
public class TenantDaoHbn extends BaseDaoHbn implements TenantDao {
	
	@Override
	public MsTenant getTenantByCode(String tenantCode) {
		
		if (StringUtils.isBlank(tenantCode)) {
			return null;
		}
	
		return this.managerDAO.selectOne(
				"from MsTenant t "
				+ "where t.tenantCode = :tenantCode and t.isActive ='1'", 
						new Object[][] {{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}});
	}
	
	@Override
	public void insertTenant(MsTenant tenant) {
		this.managerDAO.insert(tenant);
	}
	
	@Override
	public List<MsTenant> getListTenantByUser(AmMsuser user) {
		if (null == user)
			return Collections.emptyList();

		StringBuilder query = new StringBuilder();
		query.append(" SELECT mt.id_ms_tenant, mt.tenant_code, mt.tenant_name, mt.is_active ")
				.append(" FROM ms_tenant mt ")
				.append(" JOIN ms_useroftenant uot ON uot.id_ms_tenant = mt.id_ms_tenant ")
				.append(" JOIN am_msuser amu ON amu.id_ms_user = uot.id_ms_user ")
				.append(" WHERE mt.is_active ='1' AND amu.is_active ='1' AND amu.id_ms_user = :idMsUser ");
		
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), new Object[][] {{"idMsUser", user.getIdMsUser()}});
		
		Iterator<Map<String, Object>> itr = result.iterator();
		List<MsTenant> tenantList = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			MsTenant tenant = new MsTenant();
			tenant.setIdMsTenant(((BigInteger) map.get("d0")).longValue());
			tenant.setTenantCode((String) map.get("d1"));
			tenant.setTenantName((String) map.get("d2"));
			tenant.setIsActive((String) map.get("d3"));

			tenantList.add(tenant);
		}
		return tenantList;
	}

	@Override
	public MsUseroftenant getUseroftenantByUserTenant(AmMsuser user, MsTenant tenant) {
		return this.managerDAO.selectOne(MsUseroftenant.class, 
				new Object[][] {{ Restrictions.eq("amMsuser", user) },{ Restrictions.eq("msTenant", tenant) }
		});
	}
	
	@Override
	public MsUseroftenant getUseroftenantByLoginIdTenantCode(String loginId, String tenantCode) {
		return this.managerDAO.selectOne("from MsUseroftenant uot "
				+ " join fetch uot.amMsuser amu "
				+ " join fetch uot.msTenant mt "
				+ " where amu.loginId = :loginId and mt.tenantCode = :tenantCode", 
				new Object[][] {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) },
								{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode) }
		});
	}

	@Override
	public void updateTenant(MsTenant tenant) {
		this.managerDAO.update(tenant);
	}

	@Override
	public void insertUserOfTenant(MsUseroftenant useroftenant) {
		this.managerDAO.insert(useroftenant);
	}

	@Override
	public MsUseroftenant getTenantByloginId(String loginId, String tenantCode) {
		Object[][] params = new Object[][] {{"loginid", StringUtils.upperCase(loginId)}, {"tenantcode", StringUtils.upperCase(tenantCode)}};
		
		return this.managerDAO.selectOne(
				"from MsUseroftenant nt "
				+ "join fetch nt.msTenant mt "
				+ "join fetch nt.amMsuser us "
				+ "where us.loginId = :loginid and mt.tenantCode = :tenantcode  "
				, params);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<MsUseroftenant> getUserTenant(long idMsUser) {
		Object[][] queryParams = {{AmMsuser.ID_MS_USER_HBM, idMsUser}};
		
		return (List<MsUseroftenant>) this.managerDAO.list(
				"from MsUseroftenant uot "
				+ "JOIN fetch uot.amMsuser amu "
				+ "JOIN fetch uot.msTenant mt "
				+ "where amu.idMsUser = :idMsUser ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
		
			
	}

	@Override
	public MsTenant getTenantByApiKeyAndTenantCode(String apiKey, String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		params.put("apiKey", apiKey);
		
		return managerDAO.selectOne(
				"from MsTenant mt "
				+ "where mt.apiKey = :apiKey and mt.tenantCode = :tenantCode ", params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsTenant getTenantByApiKeyAndTenantCodeNewTrx(String apiKey, String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		params.put("apiKey", apiKey);
		
		return managerDAO.selectOne(
				"from MsTenant mt "
				+ "where mt.apiKey = :apiKey ", params);
	}

	@Override
	@Transactional(readOnly = true)
	public List<TenantBean> getListTenant(ListTenantFilter filter) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalVal.QUERY_PARAM_MIN, filter.getMin());
		params.put(GlobalVal.QUERY_PARAM_MAX, filter.getMax());
		
		StringBuilder conditionalParam = constructListTenantParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by mt.tenant_name asc) as row, ")
				.append("mt.tenant_name as \"tenantName\", mt.tenant_code as \"tenantCode\", ")
				.append("mt.is_active as \"isActive\" ")
				.append("from ms_tenant mt ")
				.append("where 1=1 ")
				.append(conditionalParam)
			.append(") ")
			.append("select * ")
			.append("from cte ")
			.append("where row between :min and :max ");
		
		return managerDAO.selectForListString(TenantBean.class, query.toString(), params, null);
	}

	@Override
	@Transactional(readOnly = true)
	public long countListTenant(ListTenantFilter filter) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder conditionalParam = constructListTenantParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from ms_tenant mt ")
			.append("where 1=1 ")
			.append(conditionalParam);
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0L;
		}
			
		return result.longValue();
	}
	
	private StringBuilder constructListTenantParam(ListTenantFilter filter, Map<String, Object> params) {
		
		StringBuilder query = new StringBuilder();
		
		if (StringUtils.isNotBlank(filter.getTenantName())) {
			query.append("and UPPER(mt.tenant_name) like :tenantName ");
			params.put("tenantName", "%" + StringUtils.upperCase(filter.getTenantName()) + "%");
		}
		
		if (StringUtils.isNotBlank(filter.getStatus())) {
			query.append("and mt.is_active = :status ");
			params.put("status", filter.getStatus());
		}
		
		return query;
		
	}
	
}
