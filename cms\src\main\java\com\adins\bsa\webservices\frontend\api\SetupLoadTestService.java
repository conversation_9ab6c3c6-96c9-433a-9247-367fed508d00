package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.loadtest.SetupAddDashboardLoadTestRequest;
import com.adins.bsa.webservices.model.loadtest.SetupAddDashboardLoadTestResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListBankStatementResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListDashboardNameResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListEmailResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListObjectResponse;

public interface SetupLoadTestService {
	SetupAddDashboardLoadTestResponse setupAddDashboard(SetupAddDashboardLoadTestRequest request);
	SetupAddDashboardLoadTestResponse setupAddNewBankStatement(SetupAddDashboardLoadTestRequest request);
	SetupListDashboardNameResponse getLatestActiveDashboardNames(SetupAddDashboardLoadTestRequest request);
	SetupListDashboardNameResponse getLatestConsolidateReadyDashboards(SetupAddDashboardLoadTestRequest request);
	SetupListEmailResponse getLatestLoadTestEmails(SetupAddDashboardLoadTestRequest request);
	SetupListEmailResponse getLatestEmailsWithOtp(SetupAddDashboardLoadTestRequest request);
	SetupListBankStatementResponse getLatestDeletableBankStatements(SetupAddDashboardLoadTestRequest request);
	SetupListObjectResponse getLatestBankStatementHeaders(SetupAddDashboardLoadTestRequest request);
	SetupListObjectResponse getLatestOcrResultDetails(SetupAddDashboardLoadTestRequest request);
}
