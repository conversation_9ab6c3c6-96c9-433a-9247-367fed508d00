package com.adins.bsa.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "tr_dashboard_group_last_update_date")
public class TrDashboardGroupLastUpdateDate extends UpdateableEntity {

	private long idDashboardGroupLastUpdateDate;
	private TrDashboardGroupH trDashboardGroupH;
	private Date consolidateDate;
	private Date ocrResultLastUpdDate;
	private Date supplierBuyerLastUpdDate;
	private Date nonbusinessLastUpdDate;
	private Date anomalyLastUpdDate;
	private Date circularLastUpdDate;
	private Date businessLastUpdDate;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_dashboard_group_last_update_date", unique = true, nullable = false)
	public long getIdDashboardGroupLastUpdateDate() {
		return idDashboardGroupLastUpdateDate;
	}
	
	public void setIdDashboardGroupLastUpdateDate(long idDashboardGroupLastUpdateDate) {
		this.idDashboardGroupLastUpdateDate = idDashboardGroupLastUpdateDate;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_h", nullable = false)
	public TrDashboardGroupH getTrDashboardGroupH() {
		return trDashboardGroupH;
	}
	
	public void setTrDashboardGroupH(TrDashboardGroupH trDashboardGroupH) {
		this.trDashboardGroupH = trDashboardGroupH;
	}
	
	@Column(name = "consolidate_date")
	public Date getConsolidateDate() {
		return consolidateDate;
	}
	
	public void setConsolidateDate(Date consolidateDate) {
		this.consolidateDate = consolidateDate;
	}
	
	@Column(name = "ocr_result_last_upd_date")
	public Date getOcrResultLastUpdDate() {
		return ocrResultLastUpdDate;
	}
	
	public void setOcrResultLastUpdDate(Date ocrResultLastUpdDate) {
		this.ocrResultLastUpdDate = ocrResultLastUpdDate;
	}
	
	@Column(name = "supplier_buyer_last_upd_date")
	public Date getSupplierBuyerLastUpdDate() {
		return supplierBuyerLastUpdDate;
	}
	
	public void setSupplierBuyerLastUpdDate(Date supplierBuyerLastUpdDate) {
		this.supplierBuyerLastUpdDate = supplierBuyerLastUpdDate;
	}
	
	@Column(name = "nonbusiness_last_upd_date")
	public Date getNonbusinessLastUpdDate() {
		return nonbusinessLastUpdDate;
	}
	
	public void setNonbusinessLastUpdDate(Date nonbusinessLastUpdDate) {
		this.nonbusinessLastUpdDate = nonbusinessLastUpdDate;
	}
	
	@Column(name = "anomaly_last_upd_date")
	public Date getAnomalyLastUpdDate() {
		return anomalyLastUpdDate;
	}
	
	public void setAnomalyLastUpdDate(Date anomalyLastUpdDate) {
		this.anomalyLastUpdDate = anomalyLastUpdDate;
	}

	@Column(name = "circular_last_upd_date")
	public Date getCircularLastUpdDate() {
		return circularLastUpdDate;
	}

	public void setCircularLastUpdDate(Date circularLastUpdDate) {
		this.circularLastUpdDate = circularLastUpdDate;
	}

	@Column(name = "business_last_upd_date")
	public Date getBusinessLastUpdDate() {
		return businessLastUpdDate;
	}

	public void setBusinessLastUpdDate(Date businessLastUpdDate) {
		this.businessLastUpdDate = businessLastUpdDate;
	}
	
	
}
