package com.adins.bsa.webservices.model.dashboard;

import java.util.List;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.bsa.custom.DashboardFileBean;

public class AddDashboardRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("Files")
	@Required(allowEmptyCollection = false)
	private List<DashboardFileBean> files;
	
	public List<DashboardFileBean> getFiles() {
		return files;
	}
	public void setFiles(List<DashboardFileBean> files) {
		this.files = files;
	}
	
}
