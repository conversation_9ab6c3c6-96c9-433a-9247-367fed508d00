package com.adins.bsa.validator.api;

import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.model.TrOcrResultSummaryPeriod;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface OcrResultValidator {
	
	void validateAccountNoOnDashboard(TrDashboardGroupH trDashboardGroupH, String accountNo, AuditContext audit);
	TrOcrResultDetail getOcrResultDetail(TrDashboardGroupH dashboardGroupH, String resultDetailId, AuditContext audit);
	TrOcrResultDetail getOcrResultDetail(TrDashboardGroupD dashboardGroupD, String resultDetailId, AuditContext audit);
	
	/**
	 * @param dashboardGroupD
	 * @param period yyyy-MM-dd
	 * @param audit
	 * @return
	 */
	TrOcrResultSummaryPeriod getOcrResultSummaryPeriod(TrDashboardGroupD dashboardGroupD, String period, AuditContext audit);

}
