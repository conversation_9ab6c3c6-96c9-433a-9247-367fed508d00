package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.tenant.AddTenantRequest;
import com.adins.bsa.webservices.model.tenant.AddTenantUserRequest;
import com.adins.bsa.webservices.model.tenant.TenantListRequest;
import com.adins.bsa.webservices.model.tenant.TenantListResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface TenantService {
	
	MssResponseType addTenant(AddTenantRequest request);
	MssResponseType addTenantUser(AddTenantUserRequest request);
	TenantListResponse getTenantList(TenantListRequest request);
}
