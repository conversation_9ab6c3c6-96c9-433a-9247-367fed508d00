package com.adins.bsa.dataaccess.api;

import java.util.List;

import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMenuofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.custom.UserRoleBean;
import com.adins.bsa.model.MsTenant;

public interface RoleDao {
	
	// am_msrole
	void insertRole(AmMsrole newRole);
	
	AmMsrole getRole(MsTenant tenant, String roleCode);
	AmMsrole getRoleReadOnly(MsTenant tenant, String roleCode);
	
	List<AmMsrole> getListRoleByIdUserTenantCode(long idMsUser, String tenantCode);
	List<UserRoleBean> getUserRole(AmMsuser user);
	
	// am_memberofrole
	void insertMemberOfRole(AmMemberofrole memberOfRole);
	void insertMemberOfRoleNewTran(AmMemberofrole memberOfRole);
	void updateMemberofRole(AmMemberofrole memberofRole);
	
	// am_menuofrole
	void insertMenuofrole(AmMenuofrole menuofrole);
	
	
}
