package com.adins.am.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.TraceableEntity;

@Entity
@Table(name = "am_auditlog")
public class AmAuditlog extends TraceableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	public static final Object ACTIVITY_HBM = "activity";
	
	private long idAuditLog;
	private AmMsuser amMsuser;
	private String screenId;

	private String tableName;
	private String fieldName;
	
	private String oldValue;
	private String newValue;
	private String keyValue;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_audit_log", unique = true, nullable = false)
	public long getIdAuditLog() {
		return this.idAuditLog;
	}

	public void setIdAuditLog(long idAuditLog) {
		this.idAuditLog = idAuditLog;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user")
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@Column(name = "screen_id", length = 80)
	public String getScreenId() {
		return this.screenId;
	}

	public void setScreenId(String screenId) {
		this.screenId = screenId;
	}

	@Column(name = "table_name", length = 32)
	public String getTableName() {
		return this.tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	@Column(name = "field_name", length = 80)
	public String getFieldName() {
		return this.fieldName;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}

	@Column(name = "old_value", length = 4000)
	public String getOldValue() {
		return this.oldValue;
	}

	public void setOldValue(String oldValue) {
		this.oldValue = oldValue;
	}

	@Column(name = "new_value", length = 4000)
	public String getNewValue() {
		return this.newValue;
	}

	public void setNewValue(String newValue) {
		this.newValue = newValue;
	}

	@Column(name = "key_value", length = 80)
	public String getKeyValue() {
		return this.keyValue;
	}

	public void setKeyValue(String keyValue) {
		this.keyValue = keyValue;
	}
}
