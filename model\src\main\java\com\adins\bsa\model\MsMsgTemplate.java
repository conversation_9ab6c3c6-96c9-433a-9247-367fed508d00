package com.adins.bsa.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "ms_msg_template")
public class MsMsgTemplate  extends UpdateableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;

	private long idMsgTemplate;
	private String templateType;
	private String templateCode;
	private String name;
	private String subject;
	private String body;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY) 
	@Column(name = "id_msg_template", unique = true, nullable = false)
	public long getIdMsgTemplate() {
		return this.idMsgTemplate;
	}

	public void setIdMsgTemplate(long idMsgTemplate) {
		this.idMsgTemplate = idMsgTemplate;
	}

	@Column(name = "template_type", nullable = false, length = 16)
	public String getTemplateType() {
		return this.templateType;
	}

	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}

	@Column(name = "template_code", nullable = false, length = 32)
	public String getTemplateCode() {
		return this.templateCode;
	}

	public void setTemplateCode(String templateCode) {
		this.templateCode = templateCode;
	}

	@Column(name = "name", length = 128)
	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "subject", length = 128)
	public String getSubject() {
		return this.subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	@Column(name = "body")
	public String getBody() {
		return this.body;
	}

	public void setBody(String body) {
		this.body = body;
	}
	
}
