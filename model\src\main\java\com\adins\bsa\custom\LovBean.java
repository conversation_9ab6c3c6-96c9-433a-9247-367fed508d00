package com.adins.bsa.custom;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;

public class LovBean implements Serializable{
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "Nama lov group", example = "Signer Type")
	protected String lovGroupName;
	protected String code;
	protected String description;
	protected String sequence;
	protected String constraint1;
	protected String constraint2;
	protected String constraint3;
	protected String constraint4;
	protected String constraint5;
	protected String isActive;
	protected String codeEncrypt;
	
	public String getLovGroupName() {
		return lovGroupName;
	}

	public void setLovGroupName(String lovGroupName) {
		this.lovGroupName = lovGroupName;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getConstraint1() {
		return constraint1;
	}

	public void setConstraint1(String constraint1) {
		this.constraint1 = constraint1;
	}

	public String getConstraint2() {
		return constraint2;
	}

	public void setConstraint2(String constraint2) {
		this.constraint2 = constraint2;
	}

	public String getConstraint3() {
		return constraint3;
	}

	public void setConstraint3(String constraint3) {
		this.constraint3 = constraint3;
	}

	public String getConstraint4() {
		return constraint4;
	}

	public void setConstraint4(String constraint4) {
		this.constraint4 = constraint4;
	}

	public String getConstraint5() {
		return constraint5;
	}

	public void setConstraint5(String constraint5) {
		this.constraint5 = constraint5;
	}
	
	public String getSequence() {
		return sequence;
	}

	public void setSequence(String sequence) {
		this.sequence = sequence;
	}
	
	public String getIsActive() {
		return isActive;
	}

	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}

	public String getCodeEncrypt() {
		return codeEncrypt;
	}

	public void setCodeEncrypt(String codeEncrypt) {
		this.codeEncrypt = codeEncrypt;
	}
}
