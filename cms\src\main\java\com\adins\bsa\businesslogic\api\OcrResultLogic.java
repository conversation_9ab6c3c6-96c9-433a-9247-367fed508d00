package com.adins.bsa.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionFileResponse;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionRequest;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionResponse;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionSummaryResponse;
import com.adins.bsa.webservices.model.ocrresult.DeleteBankStatementRequest;
import com.adins.bsa.webservices.model.ocrresult.DeleteBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.EditBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.GetBankStatementTransactionSummaryCalculationRequest;
import com.adins.bsa.webservices.model.ocrresult.GetBankStatementTransactionSummaryCalculationResponse;
import com.adins.bsa.webservices.model.ocrresult.GetListBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.GetListBankStatementTransactionDetailResponse;
import com.adins.bsa.webservices.model.ocrresult.OcrResultListRequest;
import com.adins.bsa.webservices.model.ocrresult.OcrResultListResponse;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementHeaderRequest;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementSummaryRequest;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementTransactionDetailRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface OcrResultLogic {
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	OcrResultListResponse getOcrResultList(OcrResultListRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	BankStatementHeaderTransactionResponse getBankStatementHeaderTransaction(BankStatementHeaderTransactionRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	BankStatementHeaderTransactionFileResponse getBankStatementHeaderTransactionFile(BankStatementHeaderTransactionRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	BankStatementHeaderTransactionSummaryResponse getBankStatementHeaderTransactionSummary(BankStatementHeaderTransactionRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	GetListBankStatementTransactionDetailResponse getListBankStatementTransactionDetail(GetListBankStatementTransactionDetailRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType deleteBankStatement(DeleteBankStatementRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType saveBankStatementHeader(SaveBankStatementHeaderRequest request, AuditContext audit);

	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	GetBankStatementTransactionSummaryCalculationResponse getBankStatementTransactionSummaryCalculation(GetBankStatementTransactionSummaryCalculationRequest request , AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType saveBankStatementSummary(SaveBankStatementSummaryRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType saveBankStatementTransactionDetail(SaveBankStatementTransactionDetailRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType deleteBankStatementTransactionDetail(DeleteBankStatementTransactionDetailRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType editBankStatementTransactionDetail(EditBankStatementTransactionDetailRequest request, AuditContext audit);
}
