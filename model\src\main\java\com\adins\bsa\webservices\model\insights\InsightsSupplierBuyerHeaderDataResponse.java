package com.adins.bsa.webservices.model.insights;

import java.math.BigDecimal;

import com.adins.framework.service.base.model.MssResponseType;

public class InsightsSupplierBuyerHeaderDataResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private BigDecimal supplierAmount;
	private BigDecimal buyerAmount;
	private Integer supplierTrxCount;
	private Integer buyerTrxCount;
	
	public BigDecimal getSupplierAmount() {
		return supplierAmount;
	}
	public void setSupplierAmount(BigDecimal supplierAmount) {
		this.supplierAmount = supplierAmount;
	}
	public BigDecimal getBuyerAmount() {
		return buyerAmount;
	}
	public void setBuyerAmount(BigDecimal buyerAmount) {
		this.buyerAmount = buyerAmount;
	}
	public Integer getSupplierTrxCount() {
		return supplierTrxCount;
	}
	public void setSupplierTrxCount(Integer supplierTrxCount) {
		this.supplierTrxCount = supplierTrxCount;
	}
	public Integer getBuyerTrxCount() {
		return buyerTrxCount;
	}
	public void setBuyerTrxCount(Integer buyerTrxCount) {
		this.buyerTrxCount = buyerTrxCount;
	}
	
}
