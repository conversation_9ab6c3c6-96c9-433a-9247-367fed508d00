package com.adins.bsa.webservices.model.nonbusinesstransactiongroup;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.framework.service.base.model.MssRequestType;

public class AddNonBusinessTransactionGroupRequest extends MssRequestType {
	
	private static final long serialVersionUID = 1L;
	private String dashboardName;
	private String tenantCode;
	
	@Required(allowBlankString = false)
	@StringLength(maxValue = 64)
	private String groupName;
	
	public String getDashboardName() {
		return dashboardName;
	}
	public void setDashboardName(String dashboardName) {
		this.dashboardName = dashboardName;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	
}
