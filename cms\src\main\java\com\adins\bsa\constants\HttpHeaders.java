package com.adins.bsa.constants;

import java.util.Base64;

public class HttpHeaders {
	protected HttpHeaders() {
		throw new IllegalStateException("MediaType class shall not be instantiated! Class=" + this.getClass().getName());
	}
	
	// Key
	public static final String CONTENT_TYPE = javax.ws.rs.core.HttpHeaders.CONTENT_TYPE;
	public static final String AUTHORIZATION = javax.ws.rs.core.HttpHeaders.AUTHORIZATION;
	public static final String X_API_KEY = "x-api-key";
	public static final String X_REAL_IP = "x-real-ip";
	public static final String ACCEPT = javax.ws.rs.core.HttpHeaders.ACCEPT;
	public static final String PRIVY_MERCHANT_KEY = "Merchant-Key";
	public static final String PRIVY_TOKEN = "Token";
	
	// Value
	public static final String APPLICATION_JSON = javax.ws.rs.core.MediaType.APPLICATION_JSON;
	public static final String APPLICATION_PDF = org.springframework.http.MediaType.APPLICATION_PDF_VALUE;
	public static final String APPLICATION_FORM_URLENCODED = javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
	public static final String MULTIPART_FORMDATA = "multipart/form-data";
	
	public static String buildBearerToken(String token) {
		return "Bearer " + token;
	}
	
	public static String buildBasicAuthorization(String username, String password) {
		String toBeEncoded = username + ":" + password;
		String encoded = Base64.getEncoder().encodeToString(toBeEncoded.getBytes());
		return "Basic " + encoded;
	}
}
