package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.SetupLoadTestLogic;
import com.adins.bsa.webservices.frontend.api.SetupLoadTestService;
import com.adins.bsa.webservices.model.loadtest.SetupAddDashboardLoadTestRequest;
import com.adins.bsa.webservices.model.loadtest.SetupAddDashboardLoadTestResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListBankStatementResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListDashboardNameResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListEmailResponse;
import com.adins.bsa.webservices.model.loadtest.SetupListObjectResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/loadTestSetup")
@Api(value = "DashboardService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericSetupLoadTestServiceEndpoint implements SetupLoadTestService {
	
	@Autowired private SetupLoadTestLogic setupLoadTestLogic;

	@Override
	@POST
	@Path("/s/addDashboard")
	public SetupAddDashboardLoadTestResponse setupAddDashboard(SetupAddDashboardLoadTestRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return setupLoadTestLogic.setupAddDashboard(request, audit);
	}

	@Override
	@POST
	@Path("/s/getLatestActiveDashboards")
	public SetupListDashboardNameResponse getLatestActiveDashboardNames(SetupAddDashboardLoadTestRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return setupLoadTestLogic.getLatestActiveDashboardNames(request, audit);
	}

	@Override
	@POST
	@Path("/s/addNewBankStatement")
	public SetupAddDashboardLoadTestResponse setupAddNewBankStatement(SetupAddDashboardLoadTestRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return setupLoadTestLogic.setupAddNewBankStatement(request, audit);
	}

	@Override
	@POST
	@Path("/s/getLatestConsolidateReadyDashboards")
	public SetupListDashboardNameResponse getLatestConsolidateReadyDashboards(SetupAddDashboardLoadTestRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return setupLoadTestLogic.getLatestConsolidateReadyDashboards(request, audit);
	}

	@Override
	@POST
	@Path("/s/getLatestLoadTestEmails")
	public SetupListEmailResponse getLatestLoadTestEmails(SetupAddDashboardLoadTestRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return setupLoadTestLogic.getLatestLoadTestEmails(request, audit);
	}

	@Override
	@POST
	@Path("/s/getLatestEmailsWithOtp")
	public SetupListEmailResponse getLatestEmailsWithOtp(SetupAddDashboardLoadTestRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return setupLoadTestLogic.getLatestEmailsWithOtp(request, audit);
	}

	@Override
	@POST
	@Path("/s/getLatestDeletableBankStatements")
	public SetupListBankStatementResponse getLatestDeletableBankStatements(SetupAddDashboardLoadTestRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return setupLoadTestLogic.getLatestDeletableBankStatements(request, audit);
	}

	@Override
	@POST
	@Path("/s/getLatestBankStatementHeaders")
	public SetupListObjectResponse getLatestBankStatementHeaders(SetupAddDashboardLoadTestRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return setupLoadTestLogic.getLatestBankStatementHeaders(request, audit);
	}

	@Override
	@POST
	@Path("/s/getLatestOcrResultDetails")
	public SetupListObjectResponse getLatestOcrResultDetails(SetupAddDashboardLoadTestRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return setupLoadTestLogic.getLatestOcrResultDetails(request, audit);
	}

}
