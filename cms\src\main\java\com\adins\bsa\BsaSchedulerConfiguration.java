package com.adins.bsa;

import java.text.ParseException;
import java.util.Calendar;

import org.quartz.CronTrigger;
import org.quartz.JobDetail;
import org.quartz.SchedulerException;
import org.quartz.SimpleTrigger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.quartz.SchedulerFactoryBeanCustomizer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.quartz.CronTriggerFactoryBean;
import org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.scheduling.quartz.SimpleTriggerFactoryBean;

@Configuration
public class BsaSchedulerConfiguration implements SchedulerFactoryBeanCustomizer {
	
	private static final Logger LOG = LoggerFactory.getLogger(BsaSchedulerConfiguration.class);
	
	@Autowired
	private ApplicationContext applicationContext;
	@Autowired
	private Environment environment;
	
	@Override
	public void customize(SchedulerFactoryBean schedulerFactoryBean) {
		schedulerFactoryBean.setAutoStartup(true );
		schedulerFactoryBean.setApplicationContext(applicationContext);
		schedulerFactoryBean.setExposeSchedulerInRepository(true);
		schedulerFactoryBean.setOverwriteExistingJobs(true);
		schedulerFactoryBean.setWaitForJobsToCompleteOnShutdown(true);
	}
	
	SimpleTrigger createTrigger(JobDetail jobDetail, long pollFrequencyMs, String triggerName) {
        LOG.info("createTrigger(jobDetail={}, pollFrequencyMs={}, triggerName={})", jobDetail, pollFrequencyMs, triggerName);
        SimpleTriggerFactoryBean factoryBean = new SimpleTriggerFactoryBean();
        factoryBean.setJobDetail(jobDetail);
        factoryBean.setStartDelay(0L);
        factoryBean.setRepeatInterval(pollFrequencyMs);
        factoryBean.setName(triggerName);
        factoryBean.setRepeatCount(SimpleTrigger.REPEAT_INDEFINITELY);
        factoryBean.setMisfireInstruction(SimpleTrigger.MISFIRE_INSTRUCTION_RESCHEDULE_NEXT_WITH_REMAINING_COUNT);
        factoryBean.afterPropertiesSet();
        return factoryBean.getObject();
    }
    
	CronTrigger createCronTrigger(JobDetail jobDetail, String cronExpression, String triggerName) throws SchedulerException {
        LOG.info("createCronTrigger(jobDetail={}, cronExpression={}, triggerName={})", jobDetail, cronExpression, triggerName);
        // To fix an issue with time-based cron jobs
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        CronTriggerFactoryBean factoryBean = new CronTriggerFactoryBean();
        factoryBean.setJobDetail(jobDetail);
        factoryBean.setCronExpression(cronExpression);
        factoryBean.setStartTime(calendar.getTime());
        factoryBean.setStartDelay(0L);
        factoryBean.setName(triggerName);
        factoryBean.setMisfireInstruction(CronTrigger.MISFIRE_INSTRUCTION_DO_NOTHING);
        try {
			factoryBean.afterPropertiesSet();
		}
        catch (ParseException e) {
			LOG.error("Error upon creating cron trigger with exp '{}'", cronExpression);
			throw new SchedulerException("Failed upon initialized job scheduler");
        	
		}
        return factoryBean.getObject();
    }
	
	JobDetail createJobDetail(String jobBeanName, String jobMethodName, boolean concurrent, String jobName) throws SchedulerException {
		MethodInvokingJobDetailFactoryBean mijfb = new MethodInvokingJobDetailFactoryBean();
		mijfb.setBeanFactory(applicationContext);
		mijfb.setTargetBeanName(jobBeanName);
		mijfb.setTargetMethod(jobMethodName);
		mijfb.setConcurrent(concurrent);
		mijfb.setName(jobName);
		try {
			mijfb.afterPropertiesSet();
		}
		catch (ClassNotFoundException | NoSuchMethodException e) {
			LOG.error("Error upon creating job {} with method {}", jobBeanName, jobMethodName);
			throw new SchedulerException("Failed upon initialized job scheduler");
		}
		return mijfb.getObject();
	}
}
