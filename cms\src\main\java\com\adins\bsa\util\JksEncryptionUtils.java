package com.adins.bsa.util;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

import com.adins.framework.tool.encryption.EncryptionException;

public class JksEncryptionUtils {
	
	private JksEncryptionUtils() {}
	
	public static final String KEY_STORE_TYPE_JKS = "jks";

	/**
	 * 
	 * @param keyStoreUrl
	 * 		keystore stored in classpath, can use url "classpath:keystore.jks"
	 * 		keystore absolute path, can use url "file:///F:/keystore.jks"
	 * @param password
	 * @param keyStoreType
	 * @return
	 * @throws KeyStoreException
	 * @throws IOException
	 * @throws NoSuchAlgorithmException
	 * @throws CertificateException
	 */
	public static KeyStore loadKeyStore(final URL keyStoreUrl, final String password, final String keyStoreType){
		if (null == keyStoreUrl) {
			throw new IllegalArgumentException("Keystore url may not be null");
		}
		
		try (InputStream is = keyStoreUrl.openStream();) {			
			final KeyStore keystore = KeyStore.getInstance(keyStoreType);
			keystore.load(is, null == password ? null : password.toCharArray());
			return keystore;
		}
		catch (NoSuchAlgorithmException | CertificateException | IOException | KeyStoreException e) {
			throw new EncryptionException("Error on loading keystore url "+ keyStoreUrl, e); 
		}		
	}
		
	public static byte[] encryptWithKeyStore(final KeyStore keyStore, final String entryAlias, byte[] plainBytes) {
		try {			
			final Certificate cert = keyStore.getCertificate(entryAlias);
			final PublicKey publicKey = cert.getPublicKey();
				
			return rsaEncrypt(plainBytes, publicKey);
		}
		catch (Exception e) {
			throw new EncryptionException("Error on RSA encryption with keystore", e);
		}		
	}
	
	public static byte[] decryptWithKeyStore(final KeyStore keyStore, final String entryAlias, final String entryPassword,
			byte[] cipheredBytes) {
		try {
			final Key privateKey = keyStore.getKey(entryAlias, entryPassword.toCharArray());			
			return rsaDecrypt(cipheredBytes, privateKey);
		}
		catch (Exception e) {
			throw new EncryptionException("Error on RSA encryption with keystore", e);
		}
	}
	
	private static byte[] rsaEncrypt(byte[] plainBytes, PublicKey publicKey)
			throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
		Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWITHSHA-512ANDMGF1PADDING");
		cipher.init(Cipher.ENCRYPT_MODE, publicKey);
		return cipher.doFinal(plainBytes);
	}
	
	private static byte[] rsaDecrypt(byte[] cipheredBytes, Key privateKey)
			throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
		Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWITHSHA-512ANDMGF1PADDING");
		cipher.init(Cipher.DECRYPT_MODE, privateKey);
		return cipher.doFinal(cipheredBytes);		
	}
}
