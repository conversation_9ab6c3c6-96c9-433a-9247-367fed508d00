package com.adins.bsa.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "ms_dashboard_tab")
public class MsDashboardTab extends UpdateableEntity {
    
    private long idDashboardTab;
    private String dashboardTabCode;
    private String dashboardTabName;

    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_dashboard_tab", unique = true, nullable = false)
    public long getIdDashboardTab() {
        return this.idDashboardTab;
    }

    public void setIdDashboardTab(long idDashboardTab) {
        this.idDashboardTab = idDashboardTab;
    }

    @Column(name = "dashboard_tab_code", length = 64, nullable = false)
    public String getDashboardTabCode() {
        return this.dashboardTabCode;
    }

    public void setDashboardTabCode(String dashboardTabCode) {
        this.dashboardTabCode = dashboardTabCode;
    }

    @Column(name = "dashboard_tab_name", length = 64, nullable = false)
    public String getDashboardTabName() {
        return this.dashboardTabName;
    }

    public void setDashboardTabName(String dashboardTabName) {
        this.dashboardTabName = dashboardTabName;
    }

}
