package com.adins.bsa;

public class SmtpProperties {			
	private boolean sslEnable;
	private boolean starttlsEnable;
	private boolean auth;
	private int connectiontimeout;
	private int timeout;
	private int writetimeout;
	private String socketFactoryClass;
	
	public boolean isSslEnable() {
		return sslEnable;
	}
	public void setSslEnable(boolean sslEnable) {
		this.sslEnable = sslEnable;
	}
	public boolean isStarttlsEnable() {
		return starttlsEnable;
	}
	public void setStarttlsEnable(boolean starttlsEnable) {
		this.starttlsEnable = starttlsEnable;
	}
	public boolean isAuth() {
		return auth;
	}
	public void setAuth(boolean auth) {
		this.auth = auth;
	}
	public int getConnectiontimeout() {
		return connectiontimeout;
	}
	public void setConnectiontimeout(int connectiontimeout) {
		this.connectiontimeout = connectiontimeout;
	}
	public int getTimeout() {
		return timeout;
	}
	public void setTimeout(int timeout) {
		this.timeout = timeout;
	}
	public int getWritetimeout() {
		return writetimeout;
	}
	public void setWritetimeout(int writetimeout) {
		this.writetimeout = writetimeout;
	}
	public String getSocketFactoryClass() {
		return socketFactoryClass;
	}
	public void setSocketFactoryClass(String socketFactoryClass) {
		this.socketFactoryClass = socketFactoryClass;
	}	
}
