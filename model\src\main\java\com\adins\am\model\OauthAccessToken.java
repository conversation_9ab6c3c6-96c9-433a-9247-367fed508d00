package com.adins.am.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "oauth_access_token")
public class OauthAccessToken {
	
	private String tokenId;
	private byte[] token;
	private String authenticationId;
	private String username;
	private String clientId;
	private byte[] authentication;
	private String refreshToken;
	
	@Column(name = "token_id", nullable = false)
	public String getTokenId() {
		return tokenId;
	}
	public void setTokenId(String tokenId) {
		this.tokenId = tokenId;
	}
	
	@Column(name = "token", nullable = false)
	public byte[] getToken() {
		return token;
	}
	
	public void setToken(byte[] token) {
		this.token = token;
	}
	
	@Id
	@Column(name = "authentication_id", nullable = false)
	public String getAuthenticationId() {
		return authenticationId;
	}
	
	public void setAuthenticationId(String authenticationId) {
		this.authenticationId = authenticationId;
	}
	
	@Column(name = "user_name", nullable = false)
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	
	@Column(name = "authentication", nullable = false)
	public byte[] getAuthentication() {
		return authentication;
	}
	public void setAuthentication(byte[] authentication) {
		this.authentication = authentication;
	}
	
	@Column(name = "refresh_token", nullable = false)
	public String getRefreshToken() {
		return refreshToken;
	}
	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
	}

	@Column(name = "client_id", nullable = false)
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
}
