package com.adins.bsa.validator.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.model.MsSupervisorOfUser;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericUserValidator extends BaseLogic implements UserValidator {

	@Override
	public AmMsuser validateGetUserByEmail(String email, boolean userMustExists, AuditContext audit) {
		if (StringUtils.isBlank(email)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Email"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		AmMsuser user = daoFactory.getUserDao().getUserByEmail(email);
		if (userMustExists && null == user) {
			throw new CommonException(getMessage(GlobalKey.MSG_USERVAL_EMAIL_NOT_FOUND, new String[] {email}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return user;
	}

	@Override
	public AmMsuser validateGetUserByEmailNewTrx(String email, boolean userMustExists, AuditContext audit) {
		if (StringUtils.isBlank(email)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Email"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		AmMsuser user = daoFactory.getUserDao().getUserByEmailNewTrx(email);
		if (userMustExists && null == user) {
			throw new CommonException(getMessage(GlobalKey.MSG_USERVAL_EMAIL_NOT_FOUND, new String[] {email}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return user;
	}

	@Override
	public void validateUserDashboardAccess(AmMsuser requestingUser, TrDashboardGroupH dashboardGroupH, AuditContext audit) {
		
		if (!canUserAccessDashboard(requestingUser, dashboardGroupH, audit)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_FORBIDDEN, null, audit), ReasonCommon.FORBIDDEN_ACCESS);
		}
		
	}
	
	@Override
	public boolean canUserAccessDashboard(AmMsuser requestingUser, TrDashboardGroupH dashboardGroupH, AuditContext audit) {
		
		if (dashboardGroupH.getAmMsuserCreator().getIdMsUser() == requestingUser.getIdMsUser()) {
			return true;
		}
		
		long accessingIdUser = requestingUser.getIdMsUser();
		long dashboardCreatorIdUser = dashboardGroupH.getAmMsuserCreator().getIdMsUser();
		
		MsSupervisorOfUser spvOfUser = daoFactory.getUserDao().getUserByIdMsUserSupervisorAndStaff(accessingIdUser, dashboardCreatorIdUser);
		return null != spvOfUser;
	}

}
