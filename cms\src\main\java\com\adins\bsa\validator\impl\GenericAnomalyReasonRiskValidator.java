package com.adins.bsa.validator.impl;

import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.model.MsAnomalyReasonRisk;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.validator.api.AnomalyReasonRiskValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericAnomalyReasonRiskValidator extends BaseLogic implements AnomalyReasonRiskValidator {

	@Override
	public MsAnomalyReasonRisk getRiskByReasonAndTenant(<PERSON><PERSON><PERSON> lovReason, MsTenant msTenant, AuditContext audit) {
		MsAnomalyReasonRisk risk = daoFactory.getAnomalyDao().getRiskByReasonAndTenant(lovReason, msTenant);
		if (null == risk) {
			throw new CommonException(getMessage("businesslogic.anomaly.unmappedrisk", null, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return risk;
	}

	
}
