package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;
import com.adins.bsa.webservices.model.insights.ConsolidateDocumentDownloadUrlRequest;
import com.adins.bsa.webservices.model.insights.ConsolidateDocumentDownloadUrlResponse;
import com.adins.bsa.webservices.model.insights.ConsolidatedBankStatementListResponse;
import com.adins.bsa.webservices.model.insights.EditDashboardNameRequest;
import com.adins.bsa.webservices.model.insights.GeneralBodyCardDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralCircularChartDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralDailyAnalysisDataResponse;
import com.adins.bsa.webservices.model.insights.GeneralMonthlyCashFlowAnalysisDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsGeneralCircularHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsGeneralHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.InsightsInformationResponse;
import com.adins.bsa.webservices.model.insights.InsightsSupplierBuyerHeaderDataResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerGpmWcrLrResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerMonthlyCashflowAndGrowthRateResponse;
import com.adins.bsa.webservices.model.insights.SupplierBuyerTopFiveChartResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface InsightsService {

	InsightsInformationResponse getBasicInformation(GenericDashboardDropdownListRequest request);
	ConsolidatedBankStatementListResponse getConsolidatedBankStatements(GenericDashboardDropdownListRequest request);
	MssResponseType editDashboardName(EditDashboardNameRequest request);
	ConsolidateDocumentDownloadUrlResponse getConsolidateDocumentDownloadUrl(ConsolidateDocumentDownloadUrlRequest request);
	InsightsSupplierBuyerHeaderDataResponse getSupplierBuyerHeaderData(GenericDashboardDropdownListRequest request);
	SupplierBuyerMonthlyCashflowAndGrowthRateResponse getSupplierBuyerMonthlyCashflowAndGrowthRate(GenericDashboardDropdownListRequest request);
	SupplierBuyerGpmWcrLrResponse getSupplierBuyerGpmWcrLr(GenericDashboardDropdownListRequest request);
	SupplierBuyerTopFiveChartResponse getSupplierBuyerTopFiveChartData(GenericDashboardDropdownListRequest request);
	
	// Insights - General
	InsightsGeneralHeaderDataResponse getGeneralInsightsHeaderData(GenericDashboardDropdownListRequest request);
	GeneralMonthlyCashFlowAnalysisDataResponse getGeneralMonthlyCashFlowAnalysisData(GenericDashboardDropdownListRequest request);
	GeneralBodyCardDataResponse getGeneralBodyCardData(GenericDashboardDropdownListRequest request);
	GeneralDailyAnalysisDataResponse getGeneralDailyAnalysisData(GenericDashboardDropdownListRequest request);
	InsightsGeneralCircularHeaderDataResponse getGeneralCircularHeaderData(GenericDashboardDropdownListRequest request);
	GeneralCircularChartDataResponse getGeneralCircularChartData(GenericDashboardDropdownListRequest request);
}
