package com.adins.bsa.webservices.model.loadtest;

import com.adins.framework.service.base.model.MssRequestType;

public class SetupAddDashboardLoadTestRequest extends MssRequestType {
	
	private static final long serialVersionUID = 1L;
	private int iteration;
	private int vus;
	private String tenantCode;
	private String base64Pdf;
	
	public int getIteration() {
		return iteration;
	}
	public void setIteration(int iteration) {
		this.iteration = iteration;
	}
	public int getVus() {
		return vus;
	}
	public void setVus(int vus) {
		this.vus = vus;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getBase64Pdf() {
		return base64Pdf;
	}
	public void setBase64Pdf(String base64Pdf) {
		this.base64Pdf = base64Pdf;
	}
	
}
