package com.adins.bsa.webservices.model.insights;

import java.math.BigDecimal;

import com.adins.framework.service.base.model.MssResponseType;

public class GeneralBodyCardDataResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private BigDecimal higestBalance;
	private String highestBalanceDate;
	private BigDecimal lowestBalance;
	private String lowestBalanceDate;
	private BigDecimal monthlyAverageBalance;
	private BigDecimal dailyAverageBalance;
	private BigDecimal growthRate;
	
	public BigDecimal getHigestBalance() {
		return higestBalance;
	}
	public void setHigestBalance(BigDecimal higestBalance) {
		this.higestBalance = higestBalance;
	}
	public String getHighestBalanceDate() {
		return highestBalanceDate;
	}
	public void setHighestBalanceDate(String highestBalanceDate) {
		this.highestBalanceDate = highestBalanceDate;
	}
	public BigDecimal getLowestBalance() {
		return lowestBalance;
	}
	public void setLowestBalance(BigDecimal lowestBalance) {
		this.lowestBalance = lowestBalance;
	}
	public String getLowestBalanceDate() {
		return lowestBalanceDate;
	}
	public void setLowestBalanceDate(String lowestBalanceDate) {
		this.lowestBalanceDate = lowestBalanceDate;
	}
	public BigDecimal getMonthlyAverageBalance() {
		return monthlyAverageBalance;
	}
	public void setMonthlyAverageBalance(BigDecimal monthlyAverageBalance) {
		this.monthlyAverageBalance = monthlyAverageBalance;
	}
	public BigDecimal getDailyAverageBalance() {
		return dailyAverageBalance;
	}
	public void setDailyAverageBalance(BigDecimal dailyAverageBalance) {
		this.dailyAverageBalance = dailyAverageBalance;
	}
	public BigDecimal getGrowthRate() {
		return growthRate;
	}
	public void setGrowthRate(BigDecimal growthRate) {
		this.growthRate = growthRate;
	}
	
}
