package com.adins.bsa.dataaccess.api;

import java.util.List;

import com.adins.am.model.AmMsuser;
import com.adins.bsa.custom.TenantBean;
import com.adins.bsa.custom.queryfilter.ListTenantFilter;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.MsUseroftenant;

public interface TenantDao {
	// ms_tenant
	void insertTenant(MsTenant tenant);
	void updateTenant(MsTenant tenant);
	
	MsTenant getTenantByCode(String tenantCode);
	MsTenant getTenantByApiKeyAndTenantCode(String apiKey, String tenantCode);
	MsTenant getTenantByApiKeyAndTenantCodeNewTrx(String apiKey, String tenantCode);
	
	List<MsTenant> getListTenantByUser(AmMsuser user);
	
	// ms_useroftenant
	void insertUserOfTenant(MsUseroftenant useroftenant);
	
	MsUseroftenant getTenantByloginId(String loginId, String tenantCode);
	MsUseroftenant getUseroftenantByUserTenant(<PERSON><PERSON><PERSON> user, MsTenant tenant);
	MsUseroftenant getUseroftenantByLoginIdTenantCode(String loginId, String tenantCode);
	
	List<MsUseroftenant> getUserTenant(long idMsUser);
	
	// others
	List<TenantBean> getListTenant(ListTenantFilter filter);
	long countListTenant(ListTenantFilter filter);
	
	
}
