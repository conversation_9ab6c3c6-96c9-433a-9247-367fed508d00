package com.adins.bsa.businesslogic.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMenuofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserpwdhistory;
import com.adins.bsa.businesslogic.api.TenantLogic;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.TenantBean;
import com.adins.bsa.custom.queryfilter.ListTenantFilter;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.MsUseroftenant;
import com.adins.bsa.validator.api.ObjectRequestValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.webservices.model.tenant.AddTenantRequest;
import com.adins.bsa.webservices.model.tenant.AddTenantUserRequest;
import com.adins.bsa.webservices.model.tenant.TenantListRequest;
import com.adins.bsa.webservices.model.tenant.TenantListResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.tool.password.PasswordHash;

@Transactional
@Component
public class GenericTenantLogic extends BaseLogic implements TenantLogic {
	
	@Autowired private ObjectRequestValidator requestValidator;
	@Autowired private TenantValidator tenantValidator;
	
	@Override
	public MssResponseType addTenant(AddTenantRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null != tenant) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ALREADY_USED, new String[] {"Tenant code", request.getTenantCode()}, audit), ReasonCommon.INVALID_VALUE);
		}
		
		AmMsuser user = daoFactory.getUserDao().getUserByEmail(request.getEmail());
		if (null != user) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ALREADY_USED, new String[] {"Email", request.getEmail()}, audit), ReasonCommon.INVALID_VALUE);
		}
		
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_PASSWORD_FORMAT);
		String passwordRegex = StringUtils.chomp(gs.getGsValue());
		if (!request.getPassword().matches(passwordRegex)) {
			throw new CommonException(getMessage("businesslogic.user.invalidpasswordformat", null, audit), ReasonCommon.INVALID_VALUE);
		}
		
		tenant = new MsTenant();
		tenant.setTenantCode(StringUtils.upperCase(request.getTenantCode()));
		tenant.setTenantName(request.getTenantName());
		tenant.setApiKey(request.getApiKey());
		tenant.setUsrCrt(audit.getCallerId());
		tenant.setDtmCrt(new Date());
		tenant.setIsActive("1");
		daoFactory.getTenantDao().insertTenant(tenant);
		
		String fullname = StringUtils.upperCase(request.getName());
		String initialName = Arrays.stream(fullname.trim().split("\\s+"))
				.map(name -> name.substring(0, 1).toUpperCase())
				.collect(Collectors.joining());
		
		String hashedPassword = PasswordHash.createHash(request.getPassword());
		
		user = new AmMsuser();
		user.setIsActive("1");
		user.setIsDeleted("0");
		user.setLoginId(StringUtils.upperCase(request.getEmail()));
		user.setFullName(StringUtils.upperCase(fullname));
		user.setInitialName(initialName);
		user.setLoginProvider("DB");
		user.setPassword(hashedPassword);
		user.setFailCount(0);
		user.setIsLoggedIn("0");
		user.setIsLocked("0");
		user.setIsDormant("0");
		user.setChangePwdLogin("0");
		user.setUsrCrt(audit.getCallerId());
		user.setDtmCrt(new Date());
		daoFactory.getUserDao().insertUser(user);
		
		AmUserpwdhistory pwdHistory = new AmUserpwdhistory();
		pwdHistory.setUsrCrt(audit.getCallerId());
		pwdHistory.setDtmCrt(new Date());
		pwdHistory.setAmMsuser(user);
		pwdHistory.setPassword(hashedPassword);
		pwdHistory.setMsLov(daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_PWD_CHANGE_TYPE, GlobalVal.CODE_LOV_CHANGE_PWD_TYPE_NEW));
		daoFactory.getUserDao().insertUserPwdHistory(pwdHistory);
		
		AmMsrole role = new AmMsrole();
		role.setUsrCrt(audit.getCallerId());
		role.setDtmCrt(new Date());
		role.setIsActive("1");
		role.setIsDeleted("0");
		role.setRoleCode(GlobalVal.ROLE_CODE_CREDIT_ANALYST);
		role.setRoleName(GlobalVal.ROLE_NAME_CREDIT_ANALYST);
		role.setMsTenant(tenant);
		daoFactory.getRoleDao().insertRole(role);
		
		AmMenuofrole menuofrole = new AmMenuofrole();
		menuofrole.setUsrCrt(audit.getCallerId());
		menuofrole.setDtmCrt(new Date());
		menuofrole.setAmMsmenu(daoFactory.getMenuDao().getMenuByCode(GlobalVal.MENU_CODE_DASHBOARD));
		menuofrole.setAmMsrole(role);
		daoFactory.getRoleDao().insertMenuofrole(menuofrole);
		
		AmMemberofrole memberofrole = new AmMemberofrole();
		memberofrole.setUsrCrt(audit.getCallerId());
		memberofrole.setDtmCrt(new Date());
		memberofrole.setAmMsuser(user);
		memberofrole.setAmMsrole(role);
		daoFactory.getRoleDao().insertMemberOfRole(memberofrole);
		
		MsUseroftenant useroftenant = new MsUseroftenant();
		useroftenant.setUsrCrt(audit.getCallerId());
		useroftenant.setDtmCrt(new Date());
		useroftenant.setMsTenant(tenant);
		useroftenant.setAmMsuser(user);
		daoFactory.getTenantDao().insertUserOfTenant(useroftenant);
		
		return new MssResponseType();
	}

	@Override
	public MssResponseType addTenantUser(AddTenantUserRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		
		AmMsuser user = daoFactory.getUserDao().getUserByEmail(request.getEmail());
		if (null != user) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ALREADY_USED, new String[] {"Email", request.getEmail()}, audit), ReasonCommon.INVALID_VALUE);
		}
		
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_PASSWORD_FORMAT);
		String passwordRegex = StringUtils.chomp(gs.getGsValue());
		if (!request.getPassword().matches(passwordRegex)) {
			throw new CommonException(getMessage("businesslogic.user.invalidpasswordformat", null, audit), ReasonCommon.INVALID_VALUE);
		}
		
		AmMsrole role = daoFactory.getRoleDao().getRole(tenant, GlobalVal.ROLE_CODE_CREDIT_ANALYST);
		
		String hashedPassword = PasswordHash.createHash(request.getPassword());
		String fullname = StringUtils.upperCase(request.getName());
		String initialName = Arrays.stream(fullname.trim().split("\\s+"))
				.map(name -> name.substring(0, 1).toUpperCase())
				.collect(Collectors.joining());
		
		user = new AmMsuser();
		user.setIsActive("1");
		user.setIsDeleted("0");
		user.setLoginId(StringUtils.upperCase(request.getEmail()));
		user.setFullName(StringUtils.upperCase(fullname));
		user.setInitialName(initialName);
		user.setLoginProvider("DB");
		user.setPassword(hashedPassword);
		user.setFailCount(0);
		user.setIsLoggedIn("0");
		user.setIsLocked("0");
		user.setIsDormant("0");
		user.setChangePwdLogin("0");
		user.setUsrCrt(audit.getCallerId());
		user.setDtmCrt(new Date());
		daoFactory.getUserDao().insertUser(user);
		
		AmUserpwdhistory pwdHistory = new AmUserpwdhistory();
		pwdHistory.setUsrCrt(audit.getCallerId());
		pwdHistory.setDtmCrt(new Date());
		pwdHistory.setAmMsuser(user);
		pwdHistory.setPassword(hashedPassword);
		pwdHistory.setMsLov(daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_PWD_CHANGE_TYPE, GlobalVal.CODE_LOV_CHANGE_PWD_TYPE_NEW));
		daoFactory.getUserDao().insertUserPwdHistory(pwdHistory);
		
		AmMemberofrole memberofrole = new AmMemberofrole();
		memberofrole.setUsrCrt(audit.getCallerId());
		memberofrole.setDtmCrt(new Date());
		memberofrole.setAmMsuser(user);
		memberofrole.setAmMsrole(role);
		daoFactory.getRoleDao().insertMemberOfRole(memberofrole);
		
		MsUseroftenant useroftenant = new MsUseroftenant();
		useroftenant.setUsrCrt(audit.getCallerId());
		useroftenant.setDtmCrt(new Date());
		useroftenant.setMsTenant(tenant);
		useroftenant.setAmMsuser(user);
		daoFactory.getTenantDao().insertUserOfTenant(useroftenant);
		
		return new MssResponseType();
	}

	@Override
	public TenantListResponse getTenantList(TenantListRequest request, AuditContext audit) {
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		ListTenantFilter filter = new ListTenantFilter();
		filter.setTenantName(request.getTenantName());
		filter.setStatus(request.getStatus());
		filter.setMin(min);
		filter.setMax(max);
		
		List<TenantBean> tenants = daoFactory.getTenantDao().getListTenant(filter);
		long totalData = daoFactory.getTenantDao().countListTenant(filter);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;

		TenantListResponse response = new TenantListResponse();
		response.setTenants(tenants);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}
	
}
