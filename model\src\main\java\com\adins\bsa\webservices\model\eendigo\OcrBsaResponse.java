package com.adins.bsa.webservices.model.eendigo;

import com.google.gson.annotations.SerializedName;

public class OcrBsaResponse {
	private String status;
	private String message;
	@SerializedName("process_status") private String processStatus;
	@SerializedName("reff_id") private String reffId;
	private String timestamp;
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getProcessStatus() {
		return processStatus;
	}
	public void setProcessStatus(String processStatus) {
		this.processStatus = processStatus;
	}
	public String getReffId() {
		return reffId;
	}
	public void setReffId(String reffId) {
		this.reffId = reffId;
	}
	public String getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
	
}
