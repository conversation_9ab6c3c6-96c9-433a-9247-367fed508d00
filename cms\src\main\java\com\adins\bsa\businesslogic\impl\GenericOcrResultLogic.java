package com.adins.bsa.businesslogic.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.Base64;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.businesslogic.api.CloudStorageLogic;
import com.adins.bsa.businesslogic.api.OcrResultLogic;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.BankStatementHeaderInfoBean;
import com.adins.bsa.custom.BankStatementHeaderSummaryBean;
import com.adins.bsa.custom.BankStatementTransactionDetailBean;
import com.adins.bsa.custom.GetBankStatementTransactionSummaryCalculationResponseBean;
import com.adins.bsa.custom.OcrResultBean;
import com.adins.bsa.custom.OcrResultPeriodBean;
import com.adins.bsa.custom.SaveBankStatementSummaryRequestBean;
import com.adins.bsa.custom.queryfilter.ListBankStatementTransactionDetailFilter;
import com.adins.bsa.custom.queryfilter.ListOcrResultFilter;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.model.TrOcrResultHeader;
import com.adins.bsa.model.TrOcrResultSummaryPeriod;
import com.adins.bsa.util.MssTool;
import com.adins.bsa.validator.api.DashboardValidator;
import com.adins.bsa.validator.api.ObjectRequestValidator;
import com.adins.bsa.validator.api.ObjectValidator;
import com.adins.bsa.validator.api.OcrResultValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionFileResponse;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionRequest;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionResponse;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionSummaryResponse;
import com.adins.bsa.webservices.model.ocrresult.DeleteBankStatementRequest;
import com.adins.bsa.webservices.model.ocrresult.DeleteBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.EditBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.GetBankStatementTransactionSummaryCalculationRequest;
import com.adins.bsa.webservices.model.ocrresult.GetBankStatementTransactionSummaryCalculationResponse;
import com.adins.bsa.webservices.model.ocrresult.GetListBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.GetListBankStatementTransactionDetailResponse;
import com.adins.bsa.webservices.model.ocrresult.OcrResultListRequest;
import com.adins.bsa.webservices.model.ocrresult.OcrResultListResponse;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementHeaderRequest;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementSummaryRequest;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementTransactionDetailRequest;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
 

@Component
public class GenericOcrResultLogic extends BaseLogic implements OcrResultLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericOcrResultLogic.class);
	
	// Private constants
	private static final String CONST_AMOUNT = "Amount";
	private static final String CONST_ENDING_BALANCE = "Ending balance";
	private static final String CONST_FILE_SOURCE_PATH = "File Source Path";
	private static final String CONST_PENDING = "Pending";
	
	
	// Validator
	@Autowired private DashboardValidator dashboardValidator;
	@Autowired private ObjectValidator objectValidator;
	@Autowired private ObjectRequestValidator requestValidator;
	@Autowired private OcrResultValidator ocrResultValidator;
	@Autowired private TenantValidator tenantValidator;
	@Autowired private UserValidator userValidator;
	
	// Business logic
	@Autowired private CloudStorageLogic cloudStorageLogic;
	
	
	@Override
	public OcrResultListResponse getOcrResultList(OcrResultListRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		ListOcrResultFilter filter = new ListOcrResultFilter();
		filter.setDashboardGroupH(dashboardGroupH);
		filter.setBankName(request.getBankName());
		filter.setLovProcessResultCode(request.getLovProcessResultCode());
		filter.setMin(min);
		filter.setMax(max);
		
		List<OcrResultBean> results = daoFactory.getOcrResultDao().getOcrResultList(filter);
		long totalData = daoFactory.getOcrResultDao().countOcrResultList(filter);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		OcrResultListResponse response = new OcrResultListResponse();
		response.setResults(results);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public BankStatementHeaderTransactionResponse getBankStatementHeaderTransaction(BankStatementHeaderTransactionRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		if (!"1".equals(dashboardGroupH.getIsActive())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1,
					new String[] {request.getDashboardName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		if (StringUtils.isBlank(request.getFileSourcePath())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_FILE_SOURCE_PATH}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrDashboardGroupD trDashboardGroupD = daoFactory.getDashboardGroupDao().getDashboardGroupDByFileSourcePath(dashboardGroupH.getDashboardGroupName(), request.getFileSourcePath());
		if (trDashboardGroupD == null || !"1".equals(trDashboardGroupD.getIsActive())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1,
					new String[] {request.getFileSourcePath()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(trDashboardGroupD.getLovProcessStatus().getCode())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATAINVALIDSTATUS,
					new String[] {request.getFileSourcePath(), CONST_PENDING}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		TrOcrResultHeader trOcrResultHeader = daoFactory.getOcrResultDao().getOcrResultHeaderByDashboardAndTenantAndDetail(dashboardGroupH, trDashboardGroupD, tenant);
		if (trOcrResultHeader == null || !"1".equals(trOcrResultHeader.getIsActive())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1,
					new String[] {request.getFileSourcePath()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		BankStatementHeaderTransactionResponse response = new BankStatementHeaderTransactionResponse();
		response.setAccountName(trOcrResultHeader.getAccountName());
		response.setAccountNo(trOcrResultHeader.getAccountNumber());
		response.setBankOffice(trOcrResultHeader.getAccountBankOffice());
		response.setCurrency(trOcrResultHeader.getAccountCurrency());
		response.setAddress(trOcrResultHeader.getAccountAddress()); 
		response.setIsEdited(trOcrResultHeader.getIsEdited());
		
		response.setAccountNameInfo(createInfoList(
                trOcrResultHeader.getAccountNameConfidence(),
                trOcrResultHeader.getAccountNameBoxLocation(),
                trOcrResultHeader.getAccountNameBoxPage()
        ));

        response.setAccountNoInfo(createInfoList(
                trOcrResultHeader.getAccountNumberConfidence(),
                trOcrResultHeader.getAccountNumberBoxLocation(),
                trOcrResultHeader.getAccountNumberBoxPage()
        ));

        response.setBankOfficeInfo(createInfoList(
                trOcrResultHeader.getAccountBankOfficeConfidence(),
                trOcrResultHeader.getAccountBankOfficeBoxLocation(),
                trOcrResultHeader.getAccountBankOfficeBoxPage()
        ));

        response.setCurrencyInfo(createInfoList(
                trOcrResultHeader.getAccountCurrencyConfidence(),
                trOcrResultHeader.getAccountCurrencyBoxLocation(),
                trOcrResultHeader.getAccountCurrencyBoxPage()
        ));
        
        response.setAddressInfo(createInfoList(
                trOcrResultHeader.getAccountAddresConfidence(),
                trOcrResultHeader.getAccountAddressBoxLocation(),
                trOcrResultHeader.getAccountAddressBoxPage()
        ));
		
		return response;
	}
	
	private List<BankStatementHeaderInfoBean> createInfoList(
            Double confidence, String boxLocation, Integer boxPage) {

        List<BankStatementHeaderInfoBean> infoList = new ArrayList<>();
        BankStatementHeaderInfoBean infoBean = new BankStatementHeaderInfoBean();

        infoBean.setConfidence(confidence);
        infoBean.setBoxLocation(boxLocation);
        infoBean.setBoxPage(boxPage);

        infoList.add(infoBean);
        return infoList;
    }

	@Override
	public BankStatementHeaderTransactionFileResponse getBankStatementHeaderTransactionFile(BankStatementHeaderTransactionRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		if (!"1".equals(dashboardGroupH.getIsActive())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1,
					new String[] {request.getDashboardName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		if (StringUtils.isBlank(request.getFileSourcePath())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_FILE_SOURCE_PATH}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrDashboardGroupD trDashboardGroupD = daoFactory.getDashboardGroupDao().getDashboardGroupDByFileSourcePath(dashboardGroupH.getDashboardGroupName(), request.getFileSourcePath());
		if (trDashboardGroupD == null || !"1".equals(trDashboardGroupD.getIsActive())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1,
					new String[] {request.getFileSourcePath()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(trDashboardGroupD.getLovProcessStatus().getCode())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATAINVALIDSTATUS,
					new String[] {request.getFileSourcePath(), CONST_PENDING}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		 
		byte[] dashboardFile = cloudStorageLogic.getDashboardFile(tenant, trDashboardGroupD.getFileSourcePath());
		if (null == dashboardFile) { 
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_FAILED_GETDOCUMENT,
					new String[] {request.getFileSourcePath()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		LOG.info("Document {} found in OSS", trDashboardGroupD.getFileSourcePath());
		String base64Document = Base64.getEncoder().encodeToString(dashboardFile);
		BankStatementHeaderTransactionFileResponse response = new BankStatementHeaderTransactionFileResponse();
		response.setPdfBase64(base64Document);
		return response; 
		
	}

	@Override
	public BankStatementHeaderTransactionSummaryResponse getBankStatementHeaderTransactionSummary(BankStatementHeaderTransactionRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		if (!"1".equals(dashboardGroupH.getIsActive())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1,
					new String[] {request.getDashboardName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		if (StringUtils.isBlank(request.getFileSourcePath())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_FILE_SOURCE_PATH}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrDashboardGroupD trDashboardGroupD = daoFactory.getDashboardGroupDao().getDashboardGroupDByFileSourcePath(dashboardGroupH.getDashboardGroupName(), request.getFileSourcePath());
		if (trDashboardGroupD == null || !"1".equals(trDashboardGroupD.getIsActive())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1,
					new String[] {request.getFileSourcePath()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(trDashboardGroupD.getLovProcessStatus().getCode())) {
				throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATAINVALIDSTATUS,
						new String[] {request.getFileSourcePath(), CONST_PENDING}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
			
		List<BankStatementHeaderSummaryBean> results = daoFactory.getOcrResultDao().getBankStatementHeaderSummaryPeriod(trDashboardGroupD);

		BankStatementHeaderTransactionSummaryResponse response = new BankStatementHeaderTransactionSummaryResponse();
		response.setSummaries(results);  
		return response;
	}

	@Override
	public GetListBankStatementTransactionDetailResponse getListBankStatementTransactionDetail(GetListBankStatementTransactionDetailRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		if (!"1".equals(dashboardGroupH.getIsActive())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1,
					new String[] {request.getDashboardName()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		if (StringUtils.isBlank(request.getFileSourcePath())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_FILE_SOURCE_PATH}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrDashboardGroupD trDashboardGroupD = daoFactory.getDashboardGroupDao().getDashboardGroupDByFileSourcePath(dashboardGroupH.getDashboardGroupName(), request.getFileSourcePath());
		if (trDashboardGroupD == null || !"1".equals(trDashboardGroupD.getIsActive())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1,
					new String[] {request.getFileSourcePath()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(trDashboardGroupD.getLovProcessStatus().getCode())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATAINVALIDSTATUS,
					new String[] {request.getFileSourcePath(), CONST_PENDING}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		TrOcrResultHeader trOcrResultHeader = daoFactory.getOcrResultDao().getOcrResultHeaderByDashboardAndTenantAndDetail(dashboardGroupH, trDashboardGroupD, tenant);
		if (trOcrResultHeader == null || !"1".equals(trOcrResultHeader.getIsActive())) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1,
					new String[] {request.getFileSourcePath()}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		AmGeneralsetting maxRowSizeGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRowSize = Integer.parseInt(maxRowSizeGs.getGsValue());
		int min = ((request.getPage() - 1) * maxRowSize) + 1;
		int max = request.getPage() * maxRowSize;
		
		ListBankStatementTransactionDetailFilter filter = new ListBankStatementTransactionDetailFilter();
		filter.setTrDashboardGroupD(trDashboardGroupD); 
		filter.setTrOcrResultHeader(trOcrResultHeader);
		filter.setType(request.getType());
		filter.setStatus(request.getShowStatus());
		filter.setMin(min);
		filter.setMax(max);
		
		List<BankStatementTransactionDetailBean> results = daoFactory.getOcrResultDao().getListBankStatementTransactionDetail(filter);
		long totalData = daoFactory.getOcrResultDao().countListBankStatementTransactionDetail(filter);
		long totalPage = (totalData % maxRowSize == 0) ? totalData / maxRowSize : (totalData / maxRowSize) + 1;
		
		GetListBankStatementTransactionDetailResponse response = new GetListBankStatementTransactionDetailResponse();
		response.setResults(results);
		response.setPage(request.getPage());
		response.setTotalPage(totalPage);
		response.setTotalResult(totalData);
		return response;
	}

	@Override
	public MssResponseType deleteBankStatement(DeleteBankStatementRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrDashboardGroupD dashboardGroupD = dashboardValidator.getDashboardGroupD(dashboardGroupH, request.getFileSourcePath(), true, audit);
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(dashboardGroupD.getLovProcessStatus().getCode())) {
			throw new CommonException(getMessage(GlobalKey.MSG_OCR_RESULT_STILL_PROCESSED, null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		daoFactory.getCommonDao().executeDeleteBankStatement(dashboardGroupD, audit);
		
		Date updateTime = new Date();
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setOcrResultLastUpdDate(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

	@Override
	public MssResponseType saveBankStatementHeader(SaveBankStatementHeaderRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmailNewTrx(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupHNewTrx(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrDashboardGroupD dashboardGroupD = dashboardValidator.getDashboardGroupDNewTrx(dashboardGroupH, request.getFileSourcePath(), true, audit);
		TrOcrResultHeader header = daoFactory.getOcrResultDao().getOcrResultHeaderByDashboardNewTrx(dashboardGroupD);
		String oldAccountNo = header.getAccountNumber();
		
		boolean checkDeleteOverlap = !oldAccountNo.equals(request.getAccountNo());
		LOG.info("Previous account no: {}, new account no: {}", header.getAccountNumber(), request.getAccountNo());
		Date updateTime = new Date();
		
		header.setAccountName(request.getAccountName());
		header.setAccountNumber(request.getAccountNo());
		header.setAccountBankOffice(request.getBankOffice());
		header.setAccountCurrency(request.getCurrency());
		header.setAccountAddress(request.getAddress());
		header.setIsEdited("1");
		header.setUsrUpd(audit.getCallerId());
		header.setDtmUpd(updateTime);
		daoFactory.getOcrResultDao().updateOcrResultHeaderNewTrx(header);
		
		dashboardGroupD.setLovProcessStatus(daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_STATUS, GlobalVal.CODE_LOV_PROCESS_TYPE_EDIT));
		dashboardGroupD.setUsrUpd(audit.getCallerId());
		dashboardGroupD.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardDNewTrx(dashboardGroupD);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardHNewTrx(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdateNewTrx(dashboardGroupH);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setOcrResultLastUpdDate(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDateNewTrx(dashboardLastUpdate);
		
		if (checkDeleteOverlap) {
			updateDeleteOverlapFlag(dashboardGroupH, request.getAccountNo(), audit);
			updateDeleteOverlapFlag(dashboardGroupH, oldAccountNo, audit);
		}
		
		return new MssResponseType();
	}
	
	private void updateDeleteOverlapFlag(TrDashboardGroupH dashboardGroupH, String accountNo, AuditContext audit) {
		// Ambil semua periode dalam format YYYY-MM-DD HH24:MI:SS yang dimiliki account no dalam dashboard tertentu
		List<OcrResultPeriodBean> periodBeans = daoFactory.getOcrResultDao().getOcrResultPeriodsNewTrx(dashboardGroupH, accountNo);
		if (CollectionUtils.isEmpty(periodBeans)) {
			LOG.info("Dashboard {}, account number {}, processing no data", dashboardGroupH.getDashboardGroupName(), accountNo);
			return;
		}
		
		LOG.info("Dashboard {}, account number {}, processing {} data", dashboardGroupH.getDashboardGroupName(), accountNo, periodBeans.size());
		for (OcrResultPeriodBean periodBean : periodBeans) {
			
			// Ambil semua summary period yang dimiliki account no dalam periode tertentu (Summary period dengan transaksi terbanyak adalah data pertama)
			List<OcrResultPeriodBean> ocrResultPeriodBeans = daoFactory.getOcrResultDao().getOcrResultPeriodsNewTrx(dashboardGroupH, accountNo, periodBean.getPeriod());
			String formattedPeriod = periodBean.getPeriod().substring(0, 7); // Extract YYYY-MM only
			Date periodDate = MssTool.formatStringToDate(periodBean.getPeriod(), GlobalVal.DATE_TIME_FORMAT_SEC);
			
			if (CollectionUtils.isEmpty(ocrResultPeriodBeans)) {
				LOG.info("Dashboard {}, account number {}, period {}, processing 0 data", dashboardGroupH.getDashboardGroupName(), accountNo, formattedPeriod);
				continue;
			}
			
			LOG.info("Dashboard {}, account number {}, period {}, processing {} data", dashboardGroupH.getDashboardGroupName(), accountNo, formattedPeriod, ocrResultPeriodBeans.size());
			for (int i = 0; i < ocrResultPeriodBeans.size(); i++) {
				Long idOcrResultHeader = ocrResultPeriodBeans.get(i).getIdOcrResultHeader().longValue();
				String isDeleteOverlap = i == 0 ? "0" : "1";
				daoFactory.getOcrResultDao().updateOcrResultDetailIsDeleteOverlapNewTrx(idOcrResultHeader, periodDate, isDeleteOverlap, audit.getCallerId());
			}
		}
	}

	
	public GetBankStatementTransactionSummaryCalculationResponse getBankStatementTransactionSummaryCalculation(GetBankStatementTransactionSummaryCalculationRequest request , AuditContext audit) {
		GetBankStatementTransactionSummaryCalculationResponse response = new GetBankStatementTransactionSummaryCalculationResponse();
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH trDashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, trDashboardGroupH, audit);
		
		TrDashboardGroupD trDashboardGroupD = dashboardValidator.getDashboardGroupD(trDashboardGroupH, request.getFileSourcePath(), true, audit);
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(trDashboardGroupD.getLovProcessStatus().getCode())) {
				throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATAINVALIDSTATUS,
						new String[] {request.getFileSourcePath(), CONST_PENDING}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		List<GetBankStatementTransactionSummaryCalculationResponseBean> listBankStatementSummary = new ArrayList<>();
		if (StringUtils.isBlank(request.getPeriod()) && StringUtils.isBlank(request.getOpeningBalance())) {
			listBankStatementSummary =  daoFactory.getOcrResultDao().getListPeriodBankStatementTransactionSummaryCalculation(trDashboardGroupH.getIdDashboardGroupH(), trDashboardGroupD.getIdDashboardGroupD());
			response.setListSummary(listBankStatementSummary);
		} else {
			
			
			if (StringUtils.isBlank(request.getPeriod())) {
				throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Period"}, audit), ReasonCommon.MANDATORY_PARAM);
			}
			
			if(StringUtils.isBlank(request.getOpeningBalance())){
				throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Opening Balance"}, audit), ReasonCommon.MANDATORY_PARAM);
			}
			String period = request.getPeriod();
			BigDecimal openingBalance = new BigDecimal( request.getOpeningBalance());
			GetBankStatementTransactionSummaryCalculationResponseBean bean = new GetBankStatementTransactionSummaryCalculationResponseBean();
			BigDecimal endingBalance = daoFactory.getOcrResultDao().getPeriodBankStatementTransactionSummaryCalculation(trDashboardGroupH.getIdDashboardGroupH(), trDashboardGroupD.getIdDashboardGroupD(),period , openingBalance);
			bean.setPeriod(period);
			bean.setEndingBalance(endingBalance.toString());
			listBankStatementSummary.add(bean);
			response.setListSummary(listBankStatementSummary);
		}
		
		return response;
	}
	
	private List<TrOcrResultSummaryPeriod> validateSaveBankStatementSummaryRequest(SaveBankStatementSummaryRequest request, TrDashboardGroupD dashboardGroupD, AuditContext audit) {
		
		if (CollectionUtils.isEmpty(request.getSummaries())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Summaries"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (!GlobalVal.SAVE_TYPE_COMPLETE.equals(request.getSaveType()) && !GlobalVal.SAVE_TYPE_EDIT.equals(request.getSaveType())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MUST_BE_FILLED_WITH, new String[] {"Save type", "COMPLETE / EDIT"}, audit), ReasonCommon.INVALID_VALUE);
		}
		
		List<TrOcrResultSummaryPeriod> summaryPeriods = new ArrayList<>();
		for (SaveBankStatementSummaryRequestBean summary : request.getSummaries()) {
			
			requestValidator.validateAttributes(summary, audit);
			
			// If in yyyy-MM format, append -01
			if (summary.getPeriod().length() == 7) {
				String updatedPeriod = summary.getPeriod() + "-01";
				summary.setPeriod(updatedPeriod);
			}
			
			TrOcrResultSummaryPeriod summaryPeriod = ocrResultValidator.getOcrResultSummaryPeriod(dashboardGroupD, summary.getPeriod(), audit);
			summaryPeriods.add(summaryPeriod);
		}
		
		return summaryPeriods;
	}

	@Override
	public MssResponseType saveBankStatementSummary(SaveBankStatementSummaryRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrDashboardGroupD dashboardGroupD = dashboardValidator.getDashboardGroupD(dashboardGroupH, request.getFileSourcePath(), true, audit);
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(dashboardGroupD.getLovProcessStatus().getCode())) {
			throw new CommonException(getMessage(GlobalKey.MSG_OCR_RESULT_STILL_PROCESSED, null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		List<SaveBankStatementSummaryRequestBean> summaries = request.getSummaries();
		List<TrOcrResultSummaryPeriod> summaryPeriods = validateSaveBankStatementSummaryRequest(request, dashboardGroupD, audit);
		Date updateTime = new Date();
		
		for (int i = 0; i < summaryPeriods.size(); i++) {
			TrOcrResultSummaryPeriod summaryPeriod = summaryPeriods.get(i);
			summaryPeriod.setOpeningBalance(summaries.get(i).getBeginningBalance());
			summaryPeriod.setEndingBalance(summaries.get(i).getEndingBalance());
			summaryPeriod.setUsrUpd(audit.getCallerId());
			summaryPeriod.setDtmUpd(updateTime);
			daoFactory.getOcrResultDao().updateOcrResultSummaryPeriod(summaryPeriod);
		}
		
		String processStatusCode = GlobalVal.SAVE_TYPE_COMPLETE.equals(request.getSaveType()) ? GlobalVal.CODE_LOV_PROCESS_TYPE_COMPLETE : GlobalVal.CODE_LOV_PROCESS_TYPE_EDIT;
		
		dashboardGroupD.setLovProcessStatus(daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_STATUS, processStatusCode));
		dashboardGroupD.setUsrUpd(audit.getCallerId());
		dashboardGroupD.setDtmUpd(new Date());
		daoFactory.getDashboardGroupDao().updateDashboardD(dashboardGroupD);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setOcrResultLastUpdDate(updateTime);
		
		return new MssResponseType();
	}
	
	private void validateSaveBankStatementTransactionDetailRequest(SaveBankStatementTransactionDetailRequest request, AuditContext audit) {
		objectValidator.validateDateFormat(request.getTransactionDate(), GlobalVal.DATE_FORMAT, "Transaction date", audit);
		
		if (StringUtils.isBlank(request.getDescription())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Description"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (null == request.getAmount()) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_AMOUNT}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		// request.getAmount() <= 0
		if (request.getAmount().compareTo(BigDecimal.ZERO) != 1) {
			throw new CommonException(getMessage("businesslogic.global.positivenumeric", new String[] {CONST_AMOUNT}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (null == request.getEndingBalance()) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_ENDING_BALANCE}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (!GlobalVal.TRX_TYPE_CREDIT.equals(request.getTransactionType()) && !GlobalVal.TRX_TYPE_DEBIT.equals(request.getTransactionType())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MUST_BE_FILLED_WITH, new String[] {"Transaction type", "CREDIT / DEBIT"}, audit), ReasonCommon.INVALID_FORMAT);
		}
	}

	@Override
	public MssResponseType saveBankStatementTransactionDetail(SaveBankStatementTransactionDetailRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrDashboardGroupD dashboardGroupD = dashboardValidator.getDashboardGroupD(dashboardGroupH, request.getFileSourcePath(), true, audit);
		
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(dashboardGroupD.getLovProcessStatus().getCode())) {
			throw new CommonException(getMessage(GlobalKey.MSG_OCR_RESULT_STILL_PROCESSED, null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		validateSaveBankStatementTransactionDetailRequest(request, audit);
		
		Date trxDate = MssTool.formatStringToDate(request.getTransactionDate(), GlobalVal.DATE_FORMAT);
		Date currentDate = MssTool.changeDateFormat(new Date(), GlobalVal.DATE_FORMAT);
		
		// Negasi before supaya tanggal hari ini & hari berikutnya rejected
		if (!trxDate.before(currentDate)) {
			throw new CommonException(getMessage("businesslogic.global.date.beforetoday", null, audit), ReasonCommon.INVALID_VALUE);
		}
		
		long overlappedCount = daoFactory.getOcrResultDao().countOcrResultDetail(dashboardGroupD, trxDate, "1");
		if (overlappedCount > 0) {
			throw new CommonException(getMessage(GlobalKey.MSG_OCR_RESULT_TRX_OVERLAPPED, null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		TrOcrResultHeader ocrResultHeader = daoFactory.getOcrResultDao().getOcrResultHeaderByDashboard(dashboardGroupD);
		
		Date updateTime = new Date();
		
		TrOcrResultDetail resultDetail = new TrOcrResultDetail();
		resultDetail.setTrDashboardGroupD(dashboardGroupD);
		resultDetail.setTrOcrResultHeader(ocrResultHeader);
		resultDetail.setResultDetailId(UUID.randomUUID().toString());
		resultDetail.setTransactionDate(MssTool.formatStringToDate(request.getTransactionDate() + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
		resultDetail.setTransactionAmount(request.getAmount());
		resultDetail.setTransactionDesc(request.getDescription());
		resultDetail.setTransactionEndingBalance(request.getEndingBalance());
		resultDetail.setTransactionType(request.getTransactionType());
		resultDetail.setIsEdited("1");
		resultDetail.setIsDeleted("0");
		resultDetail.setIsDeleteOverlap("0");
		resultDetail.setUsrCrt(audit.getCallerId());
		resultDetail.setDtmCrt(updateTime);
		daoFactory.getOcrResultDao().insertOcrResultDetail(resultDetail);
		
		dashboardGroupD.setLovProcessStatus(daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_STATUS, GlobalVal.CODE_LOV_PROCESS_TYPE_EDIT));
		dashboardGroupD.setUsrUpd(audit.getCallerId());
		dashboardGroupD.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardD(dashboardGroupD);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		return new MssResponseType();
	}

	@Override
	public MssResponseType deleteBankStatementTransactionDetail(DeleteBankStatementTransactionDetailRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrDashboardGroupD dashboardGroupD = dashboardValidator.getDashboardGroupD(dashboardGroupH, request.getFileSourcePath(), true, audit);
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(dashboardGroupD.getLovProcessStatus().getCode())) {
			throw new CommonException(getMessage(GlobalKey.MSG_OCR_RESULT_STILL_PROCESSED, null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		TrOcrResultDetail ocrResultDetail = ocrResultValidator.getOcrResultDetail(dashboardGroupD, request.getResultDetailId(), audit);
		long overlappedCount = daoFactory.getOcrResultDao().countOcrResultDetail(dashboardGroupD, ocrResultDetail.getTransactionDate(), "1");
		if (overlappedCount > 0) {
			throw new CommonException(getMessage(GlobalKey.MSG_OCR_RESULT_TRX_OVERLAPPED, null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		daoFactory.getCommonDao().executeDeleteOcrResultDetail(ocrResultDetail);
		
		Date updateTime = new Date();
		
		dashboardGroupD.setLovProcessStatus(daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_STATUS, GlobalVal.CODE_LOV_PROCESS_TYPE_EDIT));
		dashboardGroupD.setUsrUpd(audit.getCallerId());
		dashboardGroupD.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardD(dashboardGroupD);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setOcrResultLastUpdDate(updateTime);
		dashboardLastUpdate.setSupplierBuyerLastUpdDate(updateTime);
		dashboardLastUpdate.setNonbusinessLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}
	
	private void validateEditBankStatementTransactionDetailRequest(EditBankStatementTransactionDetailRequest request, AuditContext audit) {
		objectValidator.validateDateFormat(request.getTransactionDate(), GlobalVal.DATE_FORMAT, "Transaction date", audit);
		
		if (StringUtils.isBlank(request.getDescription())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Description"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (null == request.getAmount()) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_AMOUNT}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		// request.getAmount() <= 0.0 
		if (request.getAmount().compareTo(BigDecimal.ZERO) != 1) {
			throw new CommonException(getMessage("businesslogic.global.positivenumeric", new String[] {CONST_AMOUNT}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (null == request.getEndingBalance()) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_ENDING_BALANCE}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (!GlobalVal.TRX_TYPE_CREDIT.equals(request.getTransactionType()) && !GlobalVal.TRX_TYPE_DEBIT.equals(request.getTransactionType())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MUST_BE_FILLED_WITH, new String[] {"Transaction type", "CREDIT / DEBIT"}, audit), ReasonCommon.INVALID_FORMAT);
		}
	}

	@Override
	public MssResponseType editBankStatementTransactionDetail(EditBankStatementTransactionDetailRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser requestingUser = userValidator.validateGetUserByEmail(audit.getCallerId(), true, audit);
		TrDashboardGroupH dashboardGroupH = dashboardValidator.getDashboardGroupH(tenant, request.getDashboardName(), true, audit);
		userValidator.validateUserDashboardAccess(requestingUser, dashboardGroupH, audit);
		
		TrDashboardGroupD dashboardGroupD = dashboardValidator.getDashboardGroupD(dashboardGroupH, request.getFileSourcePath(), true, audit);
		if (GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING.equals(dashboardGroupD.getLovProcessStatus().getCode())) {
			throw new CommonException(getMessage(GlobalKey.MSG_OCR_RESULT_STILL_PROCESSED, null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		TrOcrResultDetail ocrResultDetail = ocrResultValidator.getOcrResultDetail(dashboardGroupD, request.getResultDetailId(), audit);
		long overlappedCount = daoFactory.getOcrResultDao().countOcrResultDetail(dashboardGroupD, ocrResultDetail.getTransactionDate(), "1");
		if (overlappedCount > 0) {
			throw new CommonException(getMessage(GlobalKey.MSG_OCR_RESULT_TRX_OVERLAPPED, null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		validateEditBankStatementTransactionDetailRequest(request, audit);
		
		Date newTrxDate = MssTool.formatStringToDate(request.getTransactionDate(), GlobalVal.DATE_FORMAT);
		Date currentDate = MssTool.changeDateFormat(new Date(), GlobalVal.DATE_FORMAT);
		
		// Negasi before supaya tanggal hari ini & hari berikutnya rejected
		if (!newTrxDate.before(currentDate)) {
			throw new CommonException(getMessage("businesslogic.global.date.beforetoday", null, audit), ReasonCommon.INVALID_VALUE);
		}
		
		long overlappedNewDateCount = daoFactory.getOcrResultDao().countOcrResultDetail(dashboardGroupD, newTrxDate, "1");
		if (overlappedNewDateCount > 0) {
			throw new CommonException(getMessage(GlobalKey.MSG_OCR_RESULT_TRX_OVERLAPPED, null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		Date updateTime = new Date();
		
		ocrResultDetail.setTransactionDate(MssTool.formatStringToDate(request.getTransactionDate() + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
		ocrResultDetail.setTransactionAmount(request.getAmount());
		ocrResultDetail.setTransactionDesc(request.getDescription());
		ocrResultDetail.setTransactionEndingBalance(request.getEndingBalance());
		ocrResultDetail.setTransactionType(request.getTransactionType());
		ocrResultDetail.setIsEdited("1");
		ocrResultDetail.setUsrUpd(audit.getCallerId());
		ocrResultDetail.setDtmUpd(updateTime);
		daoFactory.getOcrResultDao().updateOcrResultDetail(ocrResultDetail);
		
		dashboardGroupD.setLovProcessStatus(daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_PROCESS_STATUS, GlobalVal.CODE_LOV_PROCESS_TYPE_EDIT));
		dashboardGroupD.setUsrUpd(audit.getCallerId());
		dashboardGroupD.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardD(dashboardGroupD);
		
		dashboardGroupH.setUsrUpd(audit.getCallerId());
		dashboardGroupH.setDtmUpd(updateTime);
		daoFactory.getDashboardGroupDao().updateDashboardH(dashboardGroupH);
		
		TrDashboardGroupLastUpdateDate dashboardLastUpdate = daoFactory.getDashboardGroupDao().getDashboardLastUpdate(dashboardGroupH);
		dashboardLastUpdate.setOcrResultLastUpdDate(updateTime);
		dashboardLastUpdate.setDtmUpd(updateTime);
		dashboardLastUpdate.setUsrUpd(audit.getCallerId());
		daoFactory.getDashboardGroupDao().updateDashboardGroupLastUpdateDate(dashboardLastUpdate);
		
		return new MssResponseType();
	}

}
