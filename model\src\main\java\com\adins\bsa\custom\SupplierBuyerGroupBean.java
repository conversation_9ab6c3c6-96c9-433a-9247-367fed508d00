package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigInteger;

public class SupplierBuyerGroupBean implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private BigInteger no;
	private String mainGroup;
	private String createdDate;
	private String lastUpdated;
	private String type;
	
	public BigInteger getNo() {
		return no;
	}
	public void setNo(BigInteger no) {
		this.no = no;
	}
	public String getMainGroup() {
		return mainGroup;
	}
	public void setMainGroup(String mainGroup) {
		this.mainGroup = mainGroup;
	}
	public String getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(String createdDate) {
		this.createdDate = createdDate;
	}
	public String getLastUpdated() {
		return lastUpdated;
	}
	public void setLastUpdated(String lastUpdated) {
		this.lastUpdated = lastUpdated;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
}
