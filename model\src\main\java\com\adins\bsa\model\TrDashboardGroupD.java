package com.adins.bsa.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;

@Entity
@Table(name = "tr_dashboard_group_d")
public class TrDashboardGroupD extends ActiveAndUpdateableEntity {

	public static final String PARAM_ID_DASHBOARD_GROUP_D = "idDashboardGroupD";
	public static final String PARAM_DASHBOARD_GROUP_D = "dashboardGroupD";
	public static final String PARAM_FILE_SOURCE_PATH = "fileSourcePath";
	
	private long idDashboardGroupD;
	private TrDashboardGroupH trDashboardGroupH;
	private AmMsuser amMsuserCreator;
	private String fileName;
	private String fileSourcePath;
	private MsLov lovProcessStatus;
	private String processResultMessage;
	private Integer totalPages;
	private String isInConsolidatedResult;
	private String isEditedAfterConsolidated;
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_dashboard_group_d", unique = true, nullable = false)
	public long getIdDashboardGroupD() {
		return idDashboardGroupD;
	}
	
	public void setIdDashboardGroupD(long idDashboardGroupD) {
		this.idDashboardGroupD = idDashboardGroupD;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_h", nullable = false)
	public TrDashboardGroupH getTrDashboardGroupH() {
		return trDashboardGroupH;
	}
	
	public void setTrDashboardGroupH(TrDashboardGroupH trDashboardGroupH) {
		this.trDashboardGroupH = trDashboardGroupH;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user_creator", nullable = false)
	public AmMsuser getAmMsuserCreator() {
		return amMsuserCreator;
	}
	
	public void setAmMsuserCreator(AmMsuser amMsuserCreator) {
		this.amMsuserCreator = amMsuserCreator;
	}
	
	@Column(name = "file_name", length = 256)
	public String getFileName() {
		return fileName;
	}
	
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	
	@Column(name = "file_source_path", length = 256)
	public String getFileSourcePath() {
		return fileSourcePath;
	}
	
	public void setFileSourcePath(String fileSourcePath) {
		this.fileSourcePath = fileSourcePath;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_process_status", nullable = false)
	public MsLov getLovProcessStatus() {
		return lovProcessStatus;
	}
	
	public void setLovProcessStatus(MsLov lovProcessStatus) {
		this.lovProcessStatus = lovProcessStatus;
	}
	
	@Column(name = "process_result_message", length = 256)
	public String getProcessResultMessage() {
		return processResultMessage;
	}
	
	public void setProcessResultMessage(String processResultMessage) {
		this.processResultMessage = processResultMessage;
	}
	
	@Column(name = "total_pages")
	public Integer getTotalPages() {
		return totalPages;
	}
	
	public void setTotalPages(Integer totalPages) {
		this.totalPages = totalPages;
	}
	
	@Column(name = "is_in_consolidated_result", length = 1)
	public String getIsInConsolidatedResult() {
		return isInConsolidatedResult;
	}
	
	public void setIsInConsolidatedResult(String isInConsolidatedResult) {
		this.isInConsolidatedResult = isInConsolidatedResult;
	}
	
	@Column(name = "is_edited_after_consolidated", length = 1)
	public String getIsEditedAfterConsolidated() {
		return isEditedAfterConsolidated;
	}
	
	public void setIsEditedAfterConsolidated(String isEditedAfterConsolidated) {
		this.isEditedAfterConsolidated = isEditedAfterConsolidated;
	}
	
}
