package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigInteger;

public class NonBusinessTransactionGroupBean implements Serializable {
 
	private static final long serialVersionUID = 1L;
	private BigInteger rowNum;
	private String groupName;
	private String createdDate;
	private String lastUpdated;
	 
	public BigInteger getRowNum() {
		return rowNum;
	}
	public void setRowNum(BigInteger rowNum) {
		this.rowNum = rowNum;
	} 
	
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(String createdDate) {
		this.createdDate = createdDate;
	}
	public String getLastUpdated() {
		return lastUpdated;
	}
	public void setLastUpdated(String lastUpdated) {
		this.lastUpdated = lastUpdated;
	}
	
}
