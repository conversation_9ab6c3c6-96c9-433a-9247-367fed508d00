package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigDecimal;

import com.adins.bsa.annotations.DecimalLimit;
import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.ValidationObjectName;

public class SaveBankStatementSummaryRequestBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("Period")
	@Required(allowBlankString = false)
	private String period;
	
	@ValidationObjectName("Beginning balance")
	@Required
	@DecimalLimit(precision = 17, scale = 2)
	private BigDecimal beginningBalance;
	
	@ValidationObjectName("Ending balance")
	@Required
	@DecimalLimit(precision = 17, scale = 2)
	private BigDecimal endingBalance;
	
	public String getPeriod() {
		return period;
	}
	public void setPeriod(String period) {
		this.period = period;
	}
	public BigDecimal getBeginningBalance() {
		return beginningBalance;
	}
	public void setBeginningBalance(BigDecimal beginningBalance) {
		this.beginningBalance = beginningBalance;
	}
	public BigDecimal getEndingBalance() {
		return endingBalance;
	}
	public void setEndingBalance(BigDecimal endingBalance) {
		this.endingBalance = endingBalance;
	}
	
}
