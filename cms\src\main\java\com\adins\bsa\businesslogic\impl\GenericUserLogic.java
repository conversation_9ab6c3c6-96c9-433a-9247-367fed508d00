package com.adins.bsa.businesslogic.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserpwdhistory;
import com.adins.bsa.businesslogic.api.EmailSenderLogic;
import com.adins.bsa.businesslogic.api.MessageTemplateLogic;
import com.adins.bsa.businesslogic.api.UserLogic;
import com.adins.bsa.constants.AmGlobalKey;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.MenuUserBean;
import com.adins.bsa.custom.UserProfileBean;
import com.adins.bsa.custom.UserRoleBean;
import com.adins.bsa.custom.email.EmailInformationBean;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.MsMsgTemplate;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.util.MssTool;
import com.adins.bsa.validator.api.ObjectRequestValidator;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.bsa.validator.api.UserValidator;
import com.adins.bsa.webservices.model.user.CheckUserForgotPasswordRequest;
import com.adins.bsa.webservices.model.user.ResetPasswordRequest;
import com.adins.bsa.webservices.model.user.UserMenuRequest;
import com.adins.bsa.webservices.model.user.UserMenuResponse;
import com.adins.bsa.webservices.model.user.UserProfileRequest;
import com.adins.bsa.webservices.model.user.UserProfileResponse;
import com.adins.bsa.webservices.model.user.VerifyOtpRequest;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.password.PasswordHash;

@Component
public class GenericUserLogic extends BaseLogic implements UserLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericUserLogic.class);
	
	@Autowired private UserValidator userValidator;
	@Autowired private ObjectRequestValidator requestValidator;
	@Autowired private TenantValidator tenantValidator;
	
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private MessageTemplateLogic messageTemplateLogic;
	
	@Override
	public UserProfileResponse getUserProfile(UserProfileRequest request, AuditContext audit) {
		AmMsuser user = userValidator.validateGetUserByEmail(request.getUsername(), true, audit);
		List<UserRoleBean> roles = daoFactory.getRoleDao().getUserRole(user);
		
		UserProfileBean bean = new UserProfileBean();
		bean.setLoginId(user.getLoginId());
		bean.setFullname(user.getFullName());
		bean.setChangePwdLogin("1".equals(user.getChangePwdLogin()) ? "1": "0");
		bean.setRoles(roles);
		
		UserProfileResponse response = new UserProfileResponse();
		response.setUser(bean);
		return response;
	}

	@Override
	public UserMenuResponse getUserMenu(UserMenuRequest request, AuditContext audit) {
		
		if (StringUtils.isBlank(request.getRoleCode())) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"roleCode"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		MsTenant tenant = tenantValidator.validateGetTenant(request.getTenantCode(), true, audit);
		List<MenuUserBean> menus = daoFactory.getMenuDao().getMenuByRoleAndTenant(request.getRoleCode(), tenant.getTenantCode());
		
		UserMenuResponse response = new UserMenuResponse();
		response.setMenuList(menus);
		return response;
	}

	@Override
	public MssResponseType checkUserForgotPassword(CheckUserForgotPasswordRequest request, AuditContext audit) {
		
		AmMsuser user = userValidator.validateGetUserByEmail(request.getLoginId(), true, audit);
		validateUserForgotPasswordAttempt(user, audit);
		return new MssResponseType();
	}

	@Override
	public MssResponseType sendOtpForgotPassword(CheckUserForgotPasswordRequest request, AuditContext audit) {
		
		AmMsuser user = userValidator.validateGetUserByEmail(request.getLoginId(), true, audit);
		validateUserForgotPasswordAttempt(user, audit);
		
		String otpCode = MssTool.generateRandomCharacters(GlobalVal.NUMBER, 6);
		Date updateTime = new Date();
		
		Short newRequestNum = (short) (user.getResetCodeRequestNum() + 1);
		user.setResetCode(otpCode);
		user.setResetCodeRequestDate(updateTime);
		user.setResetCodeRequestNum(newRequestNum);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(updateTime);
		daoFactory.getUserDao().updateUser(user);
		
		AmGeneralsetting generalsetting = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_SKIP_SEND_EMAIL);
		if ("1".equals(generalsetting.getGsValue())) {
			LOG.info("Skip sending email");
			return new MssResponseType();
		}
		
		// send email
		Map<String, Object> userMap = new HashMap<>();
		userMap.put("name", user.getFullName());
		userMap.put("otp", otpCode);
		
		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", userMap);
		
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_CODE_OTP_FORGOT_PASSWORD, templateParameters);
		
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setTo(new String[] { user.getLoginId() });
		emailInfo.setBodyMessage(template.getBody());
		emailInfo.setSubject(template.getSubject());
		emailSenderLogic.sendEmail(emailInfo, null);
		
		return new MssResponseType();
	}
	
	private void validateUserForgotPasswordAttempt(AmMsuser user, AuditContext audit) {
		
		if (null == user.getResetCodeRequestNum()) {
			user.setResetCodeRequestNum((short) 0);
			return;
		}
		
		if (null == user.getResetCodeRequestDate()) {
			user.setResetCodeRequestNum((short) 0);
			return;
		}
		
		int maxAttempt = getForgotPasswordMaxAttempt();
		int currentAttempt = user.getResetCodeRequestNum() != null ? user.getResetCodeRequestNum().intValue() : 0;
		
		if (!DateUtils.isSameDay(new Date(), user.getResetCodeRequestDate())) {
			user.setResetCodeRequestNum((short) 0);
			return;
		}
		
		if (currentAttempt >= maxAttempt) {
			throw new CommonException(getMessage("businesslogic.user.maxotpattempt", null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
	}
	
	private void validateVerifyResetCodeAttempt(AmMsuser user, AuditContext audit) {
		if (null == user.getVerifyResetCodeRequestNum()) {
			user.setVerifyResetCodeRequestNum((short) 0);
			return;
		}
		
		if (null == user.getVerifyResetCodeRequestDate()) {
			user.setVerifyResetCodeRequestNum((short) 0);
			return;
		}
		
		if (!DateUtils.isSameDay(new Date(), user.getVerifyResetCodeRequestDate())) {
			user.setVerifyResetCodeRequestNum((short) 0);
			return;
		}
		
		int maxAttempt = getForgotPasswordMaxAttempt();
		int currentAttempt = user.getVerifyResetCodeRequestNum() != null ? user.getVerifyResetCodeRequestNum().intValue() : 0;
		if (currentAttempt >= maxAttempt) {
			throw new CommonException(getMessage("businesslogic.user.maxverifyotpattempt", null, audit), ReasonCommon.INVALID_CONDITION);
		}
	}
	
	private int getForgotPasswordMaxAttempt() {
		AmGeneralsetting generalsetting = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_OTP_RESET_PWD_DAILY);
		if (null ==  generalsetting) {
			return 3;
		}
		
		try {
			return Integer.parseInt(generalsetting.getGsValue());
		} catch (Exception e) {
			return 3;
		}
	}

	@Override
	public MssResponseType verifyOtpForgotPassword(VerifyOtpRequest request, AuditContext audit) {
		
		requestValidator.validateAttributes(request, audit);
		AmMsuser user = userValidator.validateGetUserByEmail(request.getLoginId(), true, audit);
		
		validateVerifyResetCodeAttempt(user, audit);
		
		if (request.getOtp().equals(user.getResetCode())) {
			return new MssResponseType();
		}
		
		Date updateTime = new Date();
		
		Short newRequestNum = (short) (user.getVerifyResetCodeRequestNum() + 1);
		user.setVerifyResetCodeRequestNum(newRequestNum);
		user.setVerifyResetCodeRequestDate(updateTime);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(updateTime);
		daoFactory.getUserDao().updateUser(user);
		
		Status status = new Status();
		status.setCode(StatusCode.INVALID_CONDITION);
		status.setMessage(getMessage("businesslogic.user.invalidotp", null, audit));
		
		MssResponseType response = new MssResponseType();
		response.setStatus(status);
		return response;
	}

	@Override
	public MssResponseType resetPassword(ResetPasswordRequest request, AuditContext audit) {
		requestValidator.validateAttributes(request, audit);
		
		AmMsuser user = userValidator.validateGetUserByEmail(request.getLoginId(), true, audit);
		
		if (!request.getOtp().equals(user.getResetCode())) {
			throw new CommonException(getMessage("businesslogic.user.invalidotp", null, audit), ReasonCommon.INVALID_VALUE);
		}
		
		if (!request.getNewPassword().equals(request.getConfirmNewPassword())) {
			throw new CommonException(getMessage("businesslogic.user.passwordmismatch", null, audit), ReasonCommon.INVALID_VALUE);
		}
		
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_PASSWORD_FORMAT);
		String passwordRegex = StringUtils.chomp(gs.getGsValue());
		if (!request.getNewPassword().matches(passwordRegex)) {
			throw new CommonException(getMessage("businesslogic.user.invalidpasswordformat", null, audit), ReasonCommon.INVALID_VALUE);
		}
		
		String hashedPassword = PasswordHash.createHash(request.getNewPassword());
		Date updateTime = new Date();
		
		user.setIsLocked("0");
		user.setFailCount(0);
		user.setResetCode(null);
		user.setChangePwdLogin("0");
		user.setPassword(hashedPassword);
		user.setUsrUpd(audit.getCallerId());
		user.setDtmUpd(updateTime);
		daoFactory.getUserDao().updateUser(user);
		
		MsLov lovPwChangeType = daoFactory.getLovDao().getLovByGroupAndCode(GlobalVal.LOV_GROUP_PWD_CHANGE_TYPE, GlobalVal.CODE_LOV_CHANGE_PWD_TYPE_RESET);
		AmUserpwdhistory pwdHistory = new AmUserpwdhistory();
		pwdHistory.setAmMsuser(user);
		pwdHistory.setPassword(hashedPassword);
		pwdHistory.setMsLov(lovPwChangeType);
		pwdHistory.setDtmCrt(updateTime);
		pwdHistory.setUsrCrt(audit.getCallerId());
		daoFactory.getUserDao().insertUserPwdHistory(pwdHistory);
		
		return new MssResponseType();
	}

}
