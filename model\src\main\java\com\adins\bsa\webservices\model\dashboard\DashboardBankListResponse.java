package com.adins.bsa.webservices.model.dashboard;

import java.util.List;

import com.adins.bsa.custom.DashboardBankBean;
import com.adins.framework.service.base.model.MssResponseType;

public class DashboardBankListResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<DashboardBankBean> banks;

	public List<DashboardBankBean> getBanks() {
		return banks;
	}

	public void setBanks(List<DashboardBankBean> banks) {
		this.banks = banks;
	}
	
}
