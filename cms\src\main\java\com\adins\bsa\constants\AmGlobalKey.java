package com.adins.bsa.constants;

public class AmGlobal<PERSON>ey {

	protected AmGlobalKey() {
		throw new IllegalStateException("AmGlobalKey class shall not be instantiated! Class=" + this.getClass().getName());
	}

	/**
	 * Key for for storing exception error code, specified in as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_EXCEPTION = "excp";

	/**
	 * Key for for storing last action result, specified in as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_LAST_RESULT = "lastResult";

	/**
	 * Key for for storing default previous parameter for redirection, as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_REDIR_PARAM_MAP = "redir.params";

	/**
	 * Key for for storing default target for redirection, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_REDIR_TARGET = "redir.target";

	/**
	 *  Key for for storing state, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_STATE = "state";

	/**
	 *  Key for for storing mode, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_ACTION_NAME = "actionName";

	/**
	 *  Key for for storing mode, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_MODE = "mode";

	/**
	 * Property key for <i>other</i> context factory.
	 */
	public static final String CONTEXT_FACTORY = "context.factory";

	/**
	 * Property key for <i>other</i> Context URL provider.
	 */
	public static final String CONTEXT_URL = "context.url";

	/**
	 * Property key for initializing group of Context Class locations.
	 */
	public static final String CONTEXT_INITIALIZER_CLASS = "initializerClassLocation";
	
	/**
	 * Property key for database (datasource) JNDI name.
	 */
	public static final String JNDI_DATASOURCE = "jndi.datasource";

	/**
	 * Key for for storing result list object, used in map for storing query result.
	 */
	public static final String MAP_RESULT_LIST = "resultList";

	/**
	 * Key for for storing size of result list object, used in map for storing query result.
	 */
	public static final String MAP_RESULT_SIZE = "resultSize";

	/**
	 * Key for for storing result list object with that version, used in map for storing query result.
	 */
	public static final String MAP_RESULT_VERSION = "resultVersion";

	/**
	 * String that is used for storing action message.
	 */
	public static final String SESSION_ACTION_MESSAGE = "SESSION_ACTION_MESSAGE";

	/**
	 *Key for storing current parameter information in application session.
	 */
	public static final String SESSION_CURR_PARAMETER = "SESSION_CURR_PARAMETER";

	/**
	 *Key for storing login information in application session.
	 */
	public static final String SESSION_LOGIN = "SESSION_LOGIN";

	/**
	 * String that is used for storing model version for concurrency checking.
	 */
	public static final String SESSION_MODEL_VERSION = "SESSION_MODEL_VERSION";

	/**
	 *Key for storing push parameter information in application session.
	 */
	public static final String SESSION_PUSH_PARAMETER = "SESSION_PUSH_PARAMETER";

	/**
	 *Key for storing temporary parameter information in application session.
	 */
	public static final String SESSION_TEMP_PARAMETER = "SESSION_TEMP_PARAMETER";

	/**
	 *Key for specifying location of file application properties when using in system environment.
	 */
	public static final String SYSTEM_ENVIRONMENT = "application.test.properties";

	public static final String AUDIT_KEY_LOCALE = "locale";
	
	public static final String GENERALSETTING_MAX_ROW 			= "MAX_ROW_SIZE";
    public static final String GENERALSETTING_DEFAULT_LOCALE 	= "AM_DEFAULT_LOCALE";
	public static final String GENERALSETTING_MAX_FAIL_COUNT 	= "AM_MAX_FAIL_COUNT";
	public static final String GENERALSETTING_SYSTEM_TIMEOUT 	= "AM_SYSTEM_TIMEOUT";
	public static final String GENERALSETTING_PASSWORD_FORMAT = "AM_PASSWORD_FORMAT";
    public static final String GENERALSETTING_PASSWORD_EXPIRED 	= "AM_PWD_EXPIRED";
	public static final String GENERALSETTING_LAST_PASSWORD 	= "LAST_PASSWORD";
	public static final String GENERALSETTING_INTERFACE_TYPE 	= "INTERFACE_TYPE"; 
	public static final String GENERALSETTING_AES_KEY 			= "AES_KEY";
	public static final String GENERALSETTING_MAX_DATE_RANGE	= "RPT_MAX_DATE_RANGE";
	public static final String GENERALSETTING_MAX_EMAIL_KEEP_ALIVE	= "MAX_EMAIL_KEEP_ALIVE";
	public static final String GENERALSETTING_OTP_RESET_PWD_DAILY 		= "OTP_RESET_PWD_DAILY";
	public static final String GENERALSETTING_REGEX_PHONE_FORMAT 	= "AM_PHONE_FORMAT";
	public static final String GENERALSETTING_REGEX_EMAIL_FORMAT	= "AM_EMAIL_FORMAT";
	public static final String GENERALSETTING_REGEX_NAME_CLEANSING 	= "REGEX_NAME_CLEANSING";
	public static final String GENERALSETTING_IMAP_CONN_TIMEOUT		= "IMAP_CONN_TIMEOUT";
	public static final String GENERALSETTING_IMAP_READ_TIMEOUT		= "IMAP_READ_TIMEOUT";
	public static final String GENERALSETTING_IMAP_WRITE_TIMEOUT	= "IMAP_WRITE_TIMEOUT";
	public static final String GENERALSETTING_OSS_SIGNATURE_LIFETIME = "OSS_SIGNATURE_LIFETIME";
	public static final String GENERALSETTING_OSS_DIRECT_UPLOAD_MAX_SIZE = "OSS_DIRECT_UPLOAD_MAX_SIZE";
	public static final String GENERALSETTING_SKIP_SEND_EMAIL = "SKIP_SEND_EMAIL";
}
