package com.adins.bsa.custom;

import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.TrOcrResultDetail;

public class AddAnomalyBean {
	
	private TrOcrResultDetail ocrResultDetail;
	private MsLov lovReason;
	//add lovRisk
	private MsLov lovRisk;
	
	public AddAnomalyBean(TrOcrResultDetail ocrResultDetail, MsLov lovReason, MsLov lovRisk) {
		this.ocrResultDetail = ocrResultDetail;
		this.lovReason = lovReason;
		this.lovRisk = lovRisk;
	}
	
	public TrOcrResultDetail getOcrResultDetail() {
		return ocrResultDetail;
	}
	public void setOcrResultDetail(TrOcrResultDetail ocrResultDetail) {
		this.ocrResultDetail = ocrResultDetail;
	}
	public MsLov getLovReason() {
		return lovReason;
	}
	public void setLovReason(MsLov lovReason) {
		this.lovReason = lovReason;
	}

	public MsLov getLovRisk() {
		return lovRisk;
	}

	public void setLovRisk(MsLov lovRisk) {
		this.lovRisk = lovRisk;
	}
	
}
