package com.adins.bsa.dataaccess.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMenuofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.custom.UserRoleBean;
import com.adins.bsa.dataaccess.api.RoleDao;
import com.adins.bsa.model.MsTenant;

@Transactional
@Component
public class RoleDaoHbn extends BaseDaoHbn implements RoleDao {
	
	@Override
	public void insertRole(AmMsrole newRole) {
		this.managerDAO.insert(newRole);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<AmMsrole> getListRoleByIdUserTenantCode(long idMsUser, String tenantCode) {
		if (0 == idMsUser)
			return Collections.emptyList() ;

		StringBuilder query = new StringBuilder();
		query.append(" from AmMsrole role ")
				.append(" join fetch role.amMemberofroles mr ")
				.append(" join fetch mr.amMsuser amu ")
				.append(" join fetch role.msTenant mt ")
				.append(" WHERE role.isActive ='1' AND amu.isActive ='1' AND amu.idMsUser = :idMsUser AND mt.tenantCode = :tenantCode  ");
		
		Map<String, Object> result = this.managerDAO.list(query.toString(), new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser},{"tenantCode", tenantCode}});
		return (List<AmMsrole>) result.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@Override
	public void insertMemberOfRole(AmMemberofrole memberOfRole) {
		this.managerDAO.insert(memberOfRole);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertMemberOfRoleNewTran(AmMemberofrole memberOfRole) {
		this.managerDAO.insert(memberOfRole);
	}

	@Override
	public void updateMemberofRole(AmMemberofrole memberofRole) {
		this.managerDAO.update(memberofRole);
	}

	@Override
	@Transactional(readOnly = true)
	public List<UserRoleBean> getUserRole(AmMsuser user) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select mt.tenant_code as \"tenantCode\", mt.tenant_name as \"tenantName\", ")
			.append("mr.role_code as \"roleCode\", mr.role_name as \"roleName\" ")
			.append("from am_memberofrole mor ")
			.append("join am_msrole mr on mor.id_ms_role = mr.id_ms_role ")
			.append("join ms_tenant mt on mr.id_ms_tenant = mt.id_ms_tenant ")
			.append("where mor.id_ms_user = :idMsUser ")
			.append("and mr.is_active = '1' ");
		
		return managerDAO.selectForListString(UserRoleBean.class, query.toString(), params, null);
	}

	@Override
	public void insertMenuofrole(AmMenuofrole menuofrole) {
		managerDAO.insert(menuofrole);
	}

	@Override
	public AmMsrole getRole(MsTenant tenant, String roleCode) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("tenant", tenant);
		params.put(AmMsrole.ROLE_CODE_HBM, StringUtils.upperCase(roleCode));
		
		return managerDAO.selectOne(
				"from AmMsrole mr "
				+ "join fetch mr.msTenant mt "
				+ "where mr.msTenant = :tenant "
				+ "and mr.roleCode = :roleCode ", params);
	}

	@Override
	@Transactional(readOnly = true)
	public AmMsrole getRoleReadOnly(MsTenant tenant, String roleCode) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("tenant", tenant);
		params.put(AmMsrole.ROLE_CODE_HBM, StringUtils.upperCase(roleCode));
		
		return managerDAO.selectOne(
				"from AmMsrole mr "
				+ "join fetch mr.msTenant mt "
				+ "where mr.msTenant = :tenant "
				+ "and mr.roleCode = :roleCode ", params);
	}
}
