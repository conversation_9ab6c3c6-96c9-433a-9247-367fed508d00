package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.NonBusinessTransactionGroupLogic;
import com.adins.bsa.webservices.frontend.api.NonBusinessTransactionGroupService;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailResponse;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/nonBusinessTransactionGroup")
@Api(value = "NonBusinessTransactionGroupService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericNonBusinessTransactionGroupServiceEndpoint implements NonBusinessTransactionGroupService {
	
	@Autowired private NonBusinessTransactionGroupLogic nonBusinessTransactionGroupLogic;
	
	@Override
	@POST
	@Path("/s/list")
	public ListNonBusinessTransactionGroupResponse getListNonBusinessTransactionGroup(GenericDashboardPagingRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return nonBusinessTransactionGroupLogic.getListNonBusinessTransactionGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/add")
	public MssResponseType addNonBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return nonBusinessTransactionGroupLogic.addNonBusinessTransactionGroup(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/addDetails")
	public MssResponseType addNonBusinessTransactionGroup(AddNonBusinessTransactionGroupDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return nonBusinessTransactionGroupLogic.addNonBusinessTransactionGroupDetail(request, audit);
	}

	@Override
	@POST
	@Path("/s/delete")
	public MssResponseType deleteNonBusinessTransactionGroup(DeleteNonBusinessTransactionGroupRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return nonBusinessTransactionGroupLogic.deleteNonBusinessTransactionGroup(request, audit);
	}

	@Override
	@POST
	@Path("/s/listDetail")
	public ListNonBusinessTransactionGroupDetailResponse getListNonBusinessTransactionGroupDetail(ListNonBusinessTransactionGroupDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return nonBusinessTransactionGroupLogic.getListNonBusinessTransactionGroupDetail(request, audit);
	}

	@Override
	@POST
	@Path("/s/deleteDetail")
	public MssResponseType deleteNonBusinessTransactionGroupDetail(DeleteNonBusinessTransactionGroupDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return nonBusinessTransactionGroupLogic.deleteNonBusinessTransactionGroupDetail(request, audit);
	}

}
