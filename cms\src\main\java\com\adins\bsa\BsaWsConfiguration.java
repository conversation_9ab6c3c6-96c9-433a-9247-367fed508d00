package com.adins.bsa;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.cxf.Bus;
import org.apache.cxf.endpoint.Server;
import org.apache.cxf.ext.logging.LoggingFeature;
import org.apache.cxf.feature.Feature;
import org.apache.cxf.jaxrs.JAXRSServerFactoryBean;
import org.apache.cxf.jaxrs.swagger.Swagger2Feature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import com.adins.bsa.webservices.external.api.DashboardExternalService;
import com.adins.bsa.webservices.frontend.api.AnomalyService;
import com.adins.bsa.webservices.frontend.api.BusinessTransactionGroupService;
import com.adins.bsa.webservices.frontend.api.CircularService;
import com.adins.bsa.webservices.frontend.api.DashboardService;
import com.adins.bsa.webservices.frontend.api.DataService;
import com.adins.bsa.webservices.frontend.api.InsightsService;
import com.adins.bsa.webservices.frontend.api.NonBusinessTransactionGroupService;
import com.adins.bsa.webservices.frontend.api.OcrResultService;
import com.adins.bsa.webservices.frontend.api.OssService;
import com.adins.bsa.webservices.frontend.api.SetupLoadTestService;
import com.adins.bsa.webservices.frontend.api.SupplierBuyerGroupService;
import com.adins.bsa.webservices.frontend.api.TenantService;
import com.adins.bsa.webservices.frontend.api.UserService;
import com.adins.cxf.SubsystemContextInterceptor;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.jaxrs.json.JacksonJsonProvider;

@Configuration
public class BsaWsConfiguration {
	
	@Value("${cxf.logging.limit:500000}") private int loggingLimit;
	@Value("${logging.sensitive.parameter}") private String sensitiveParams;
	
	@Bean
	@ConditionalOnProperty(value = "cxf.feature.swagger2.enabled", matchIfMissing = false, havingValue = "true")
	public Swagger2Feature swagger2Feature() {
		return new Swagger2Feature();
	}
	
	private Set<String> getSensitiveParameters() {
		String[] arrays = sensitiveParams.split(";");
		List<String> list = Arrays.asList(arrays);
		return new HashSet<>(list);
	}

	@Bean
	@ConditionalOnProperty(value = "cxf.feature.logging.enabled", matchIfMissing = true, havingValue = "true")
	public LoggingFeature loggingFeature() {
		LoggingFeature loggingFeature = new LoggingFeature();
		loggingFeature.addSensitiveElementNames(getSensitiveParameters());
		loggingFeature.setLogBinary(true);
		loggingFeature.setLimit(loggingLimit);
		return loggingFeature;
	}

	@Bean
	public Server rsServer(Environment environment, Bus bus,
			TenantService tenantService,
			UserService userService,
			DashboardService dashboardService,
			DashboardExternalService dashboardExternalService,
			NonBusinessTransactionGroupService nonBusinessTransactionGroupService,
			OcrResultService ocrResultService,
			OssService ossService,
			DataService dataService,
			SupplierBuyerGroupService supplierBuyerGroupService,
			AnomalyService anomalyService,
			InsightsService insightsService,
			SetupLoadTestService setupLoadService,
			CircularService circularService,
			BusinessTransactionGroupService businessTransactionGroupService) {
		
		JAXRSServerFactoryBean endpoint = new JAXRSServerFactoryBean();
		final Map<String, Object> endpointProps = new HashMap<>();
		endpointProps.put("org.apache.cxf.endpoint.private", "true");

		endpoint.setBus(bus);
		endpoint.setAddress("/");
		endpoint.setProperties(endpointProps);

		endpoint.setServiceBeans(Arrays.<Object>asList(
				tenantService,
				userService,
				dashboardService,
				dashboardExternalService,
				nonBusinessTransactionGroupService,
				ocrResultService,
				ossService,
				dataService,
				supplierBuyerGroupService,
				anomalyService,
				insightsService,
				setupLoadService,
				circularService,
				businessTransactionGroupService));

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
		objectMapper.setSerializationInclusion(Include.NON_NULL);
		objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
		JacksonJsonProvider jsonProvider = new JacksonJsonProvider(objectMapper);

		List<?> providers = Arrays.asList(jsonProvider);
		endpoint.setProviders(providers);
		List<Feature> features = new ArrayList<>(1);
		if ("true".equalsIgnoreCase(environment.getProperty("cxf.feature.logging.enabled", "true"))) {
			features.add(loggingFeature());
		}
		if ("true".equalsIgnoreCase(environment.getProperty("cxf.feature.swagger2.enabled", "true"))) {
			features.add(swagger2Feature());
		}
		endpoint.setFeatures(features);
		endpoint.getInInterceptors().add(new SubsystemContextInterceptor("WebServices"));
		return endpoint.create();
	}
}
