package com.adins.bsa.webservices.model.ocrresult;

import java.math.BigDecimal;

import com.adins.bsa.annotations.DecimalLimit;
import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class SaveBankStatementTransactionDetailRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("File source path")
	@Required(allowBlankString = false)
	private String fileSourcePath;
	
	@ValidationObjectName("Transaction date")
	@Required(allowBlankString = false)
	private String transactionDate; // yyyy-MM-dd
	
	@ValidationObjectName("Description")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 256)
	private String description;
	
	@ValidationObjectName("Amount")
	@Required
	@DecimalLimit(precision = 17, scale = 2)
	private BigDecimal amount;
	
	@ValidationObjectName("Ending balance")
	@Required
	@DecimalLimit(precision = 17, scale = 2)
	private BigDecimal endingBalance;
	
	@ValidationObjectName("Transaction type")
	@Required(allowBlankString = false)
	private String transactionType;
	
	public String getFileSourcePath() {
		return fileSourcePath;
	}
	public void setFileSourcePath(String fileSourcePath) {
		this.fileSourcePath = fileSourcePath;
	}
	public String getTransactionDate() {
		return transactionDate;
	}
	public void setTransactionDate(String transactionDate) {
		this.transactionDate = transactionDate;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public BigDecimal getAmount() {
		return amount;
	}
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public BigDecimal getEndingBalance() {
		return endingBalance;
	}
	public void setEndingBalance(BigDecimal endingBalance) {
		this.endingBalance = endingBalance;
	}
	public String getTransactionType() {
		return transactionType;
	}
	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}
	
}
