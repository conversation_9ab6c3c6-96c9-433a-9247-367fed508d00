package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigInteger;

public class CircularBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private BigInteger num;
	private String groupName;
	private String accountNo;
	private String accountName;
	private Integer transactionWindow;
	private String createDate;
	
	public BigInteger getNum() {
		return num;
	}
	public void setNum(BigInteger num) {
		this.num = num;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	public Integer getTransactionWindow() {
		return transactionWindow;
	}
	public void setTransactionWindow(Integer transactionWindow) {
		this.transactionWindow = transactionWindow;
	}
	public String getCreateDate() {
		return createDate;
	}
	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}
	
}
