package com.adins.bsa.dataaccess.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.DashboardAccountNoBean;
import com.adins.bsa.custom.DashboardBankBean;
import com.adins.bsa.custom.DashboardBean;
import com.adins.bsa.custom.DashboardPeriodBean;
import com.adins.bsa.custom.OcrResultBean;
import com.adins.bsa.custom.SupplierBuyerCashFlowDetailBean;
import com.adins.bsa.custom.queryfilter.ListDashboardFilter;
import com.adins.bsa.dataaccess.api.DashboardGroupDao;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupDTemp;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrDashboardGroupLastUpdateDate;

@Component
@Transactional
public class DashboardGroupDaoHbn extends BaseDaoHbn implements DashboardGroupDao {
	
	private static final String PARAM_TENANT = "tenant";
	private static final String PARAM_DASHBOARD_GROUP_H = "dashboardGroupH";

	@Override
	public void insertDashboardH(TrDashboardGroupH dashboardGroupH) {
		managerDAO.insert(dashboardGroupH);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDashboardHNewTrx(TrDashboardGroupH dashboardGroupH) {
		managerDAO.insert(dashboardGroupH);
	}

	@Override
	public void updateDashboardH(TrDashboardGroupH dashboardGroupH) {
		managerDAO.update(dashboardGroupH);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDashboardHNewTrx(TrDashboardGroupH dashboardGroupH) {
		managerDAO.update(dashboardGroupH);
	}
	
	private StringBuilder buildListDashboardConditionalParam(ListDashboardFilter filter, Map<String, Object> params) {
		StringBuilder builder = new StringBuilder();
		
		// is_active param
		builder.append("and dgh.is_active = '1' ");
		
		// tenant param
		builder.append("and dgh.id_ms_tenant = :idMsTenant ");
		params.put(MsTenant.ID_TENANT_HBM, filter.getMsTenant().getIdMsTenant());
		
		// user param
		if (filter.isFilterCurrentUserOnly()) {
			builder.append("and dgh.id_ms_user_creator = :idMsUser ");
		}
		params.put(AmMsuser.ID_MS_USER_HBM, filter.getRequestingUser().getIdMsUser());
		
		// dashboard name param
		if (StringUtils.isNotBlank(filter.getDashboardName())) {
			builder.append("and UPPER(dgh.dashboard_group_name) LIKE :dashboardName ");
			params.put(TrDashboardGroupH.PARAM_DASHBOARD_NAME, "%" + StringUtils.upperCase(filter.getDashboardName()) + "%");
		}
		
		// upload date param
		if (null != filter.getUploadDateStart()) {
			builder.append("and dgh.dtm_crt >= :uploadDatestart ");
			params.put("uploadDatestart", filter.getUploadDateStart());
		}
		
		if (null != filter.getUploadDateEnd()) {
			builder.append("and dgh.dtm_crt <= :uploadDateEnd ");
			params.put("uploadDateEnd", filter.getUploadDateEnd());
		}
		
		return builder;
	}

	@Override
	@Transactional(readOnly = true)
	public List<DashboardBean> getListDashboard(ListDashboardFilter filter) {
		
		Map<String, Object> lovParam = new HashMap<>();
		lovParam.put(MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_PROCESS_STATUS);
		lovParam.put(MsLov.CODE_HBM, GlobalVal.CODE_LOV_PROCESS_TYPE_PENDING);
		
		BigInteger idLov = (BigInteger) managerDAO.selectOneNativeString("select id_lov from ms_lov where lov_group = :lovGroup and code = :code ", lovParam);
		
		Map<String, Object> params = new HashMap<>();
		params.put("min", filter.getMin());
		params.put("max", filter.getMax());
		params.put(MsLov.ID_LOV_HBM, idLov.longValue());
		
		StringBuilder conditionalParam = buildListDashboardConditionalParam(filter, params);
		
		// Deletable when callerId = uploader and no pending bank statement
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by dgh.dtm_upd desc) as \"row_num\", ")
				.append("dgh.dashboard_group_name as \"dashboardName\", mu.full_name as \"createdBy\", ")
				.append("to_char(dgh.dtm_crt, 'DD/MM/YYYY HH24:MI') as \"uploadDate\", ")
				.append("coalesce(to_char(dgh.consolidate_date, 'DD/MM/YYYY HH24:MI'), '-') as \"consolidateDate\", ")
				.append("coalesce(to_char(dgh.dtm_upd, 'DD/MM/YYYY HH24:MI'), '-')  as \"lastUpdated\", ")
				.append("case when dgh.id_ms_user_creator = :idMsUser and dgh.is_consolidating = '0' then '1' ")
				.append("when exists ( select 1 from ms_supervisorofuser sou where sou.id_ms_user_supervisor = :idMsUser and sou.id_ms_user_staff = dgh.id_ms_user_creator and dgh.is_consolidating = '0') then '1' ")
				.append("else '0'  end as editable, ")
				.append("case when dgh.id_ms_user_creator = :idMsUser and dgh.is_consolidating = '0' and coalesce(dgd.active_count, 0) = 0 then '1' ")
				.append("when exists ( select 1 from ms_supervisorofuser sou where sou.id_ms_user_supervisor = :idMsUser and sou.id_ms_user_staff = dgh.id_ms_user_creator and dgh.is_consolidating = '0' and coalesce(dgd.active_count, 0) = 0) then '1' ")
				.append("else '0' end as deletable, ")
				.append("dgh.is_consolidating as \"isConsolidating\" ")
				.append("from tr_dashboard_group_h dgh ")
				.append("left join (")
					.append("select id_dashboard_group_h, count(1) AS active_count ")
					.append("from tr_dashboard_group_d ")
					.append("where is_active = '1' and lov_process_status = :idLov ")
					.append("group by id_dashboard_group_h ")
				.append(") dgd on dgh.id_dashboard_group_h = dgd.id_dashboard_group_h ")
				.append("join am_msuser mu on dgh.id_ms_user_creator = mu.id_ms_user ")
				.append("where 1=1 ")
				.append(conditionalParam)
			.append(")")
			.append("select \"dashboardName\", \"createdBy\", \"uploadDate\", \"consolidateDate\", \"lastUpdated\", editable, deletable, \"isConsolidating\" ")
			.append("from cte ")
			.append("where row_num between :min and :max ");
		
		return managerDAO.selectForListString(DashboardBean.class, query.toString(), params, null);
	}

	@Override
	@Transactional(readOnly = true)
	public long countListDashboard(ListDashboardFilter filter) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder conditionalParam = buildListDashboardConditionalParam(filter, params);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_dashboard_group_h dgh ")
			.append("join am_msuser mu on dgh.id_ms_user_creator = mu.id_ms_user ")
			.append("where 1=1 ")
			.append(conditionalParam);
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		return result.longValue();
	}

	@Override
	public TrDashboardGroupH getDashboardGroupH(String dashboardName, MsTenant tenant) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_DASHBOARD_NAME, dashboardName);
		params.put(PARAM_TENANT, tenant);
		
		return managerDAO.selectOne(
				"from TrDashboardGroupH dgh "
				+ "join fetch dgh.amMsuserCreator amc "
				+ "where dgh.dashboardGroupName = :dashboardName "
				+ "and dgh.msTenant = :tenant "
				+ "and dgh.isActive = '1' ", params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDashboardGroupH getDashboardGroupHNewTrx(String dashboardName, MsTenant tenant) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_DASHBOARD_NAME, dashboardName);
		params.put(PARAM_TENANT, tenant);
		
		return managerDAO.selectOne(
				"from TrDashboardGroupH dgh "
				+ "join fetch dgh.amMsuserCreator amc "
				+ "where dgh.dashboardGroupName = :dashboardName "
				+ "and dgh.msTenant = :tenant "
				+ "and dgh.isActive = '1' ", params);
	}

	@Override
	public void insertDashboardD(TrDashboardGroupD dashboardGroupD) {
		managerDAO.insert(dashboardGroupD);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDashboardDNewTrx(TrDashboardGroupD dashboardGroupD) {
		managerDAO.insert(dashboardGroupD);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDashboardDTempNewTrx(TrDashboardGroupDTemp dashboardGroupDTemp) {
		managerDAO.insert(dashboardGroupDTemp);
	}

	@Override
	public List<DashboardBankBean> getDashboardBankList(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		params.put("isActive", "1");
		
		StringBuilder query = new StringBuilder();
		query
			.append("select distinct(orh.account_bank) as \"bankName\" ")
			.append("from tr_dashboard_group_d dgd ")
			.append("join tr_ocr_result_header orh on dgd.id_dashboard_group_d = orh.id_dashboard_group_d ")
			.append("where 1=1 ")
			.append("and dgd.id_dashboard_group_h = :idDashboardGroupH ")
			.append("and dgd.is_active = :isActive ")
			.append("order by orh.account_bank asc ");
		
		return managerDAO.selectForListString(DashboardBankBean.class, query.toString(), params, null);
	}

	@Override
	public List<DashboardAccountNoBean> getDashboardAccountNoList(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		params.put("isActive", "1");
		
		StringBuilder query = new StringBuilder();
		query
			.append("select distinct(orh.account_number) as \"accountNo\" ")
			.append("from tr_dashboard_group_d dgd ")
			.append("join tr_ocr_result_header orh on dgd.id_dashboard_group_d = orh.id_dashboard_group_d ")
			.append("where 1=1 ")
			.append("and dgd.id_dashboard_group_h = :idDashboardGroupH ")
			.append("and dgd.is_active = :isActive ")
			.append("order by orh.account_number asc ");
		
		return managerDAO.selectForListString(DashboardAccountNoBean.class, query.toString(), params, null);
	}

	@Override
	public List<DashboardPeriodBean> getDashboardPeriodList(TrDashboardGroupH dashboardGroupH, String accountNo) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		params.put("accountNo", accountNo);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select to_char(transaction_date, 'yyyy-MM') as period ")
			.append("from tr_ocr_result_detail ord ")
			.append("join tr_ocr_result_header orh on ord.id_ocr_result_header = orh.id_ocr_result_header ")
			.append("where orh.id_dashboard_group_h = :idDashboardGroupH ")
			.append("and orh.account_number = :accountNo ")
			.append("group by period ")
			.append("order by period asc ");
		
		return managerDAO.selectForListString(DashboardPeriodBean.class, query.toString(), params, null);
	}

	@Override
	public void insertDashboardGroupLastUpdateDate(TrDashboardGroupLastUpdateDate dashboardGroupLastUpdateDate) {
		managerDAO.insert(dashboardGroupLastUpdateDate);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDashboardGroupLastUpdateDateNewTrx(TrDashboardGroupLastUpdateDate dashboardGroupLastUpdateDate) {
		managerDAO.insert(dashboardGroupLastUpdateDate);
	}

	@Override
	public void updateDashboardGroupLastUpdateDate(TrDashboardGroupLastUpdateDate dashboardGroupLastUpdateDate) {
		managerDAO.update(dashboardGroupLastUpdateDate);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDashboardGroupLastUpdateDateNewTrx(TrDashboardGroupLastUpdateDate dashboardGroupLastUpdateDate) {
		managerDAO.update(dashboardGroupLastUpdateDate);
	}

	@Override
	public TrDashboardGroupLastUpdateDate getDashboardLastUpdate(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(PARAM_DASHBOARD_GROUP_H, dashboardGroupH);
		
		return managerDAO.selectOne(
				"from TrDashboardGroupLastUpdateDate lud "
				+ "where lud.trDashboardGroupH = :dashboardGroupH ", params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDashboardGroupLastUpdateDate getDashboardLastUpdateNewTrx(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(PARAM_DASHBOARD_GROUP_H, dashboardGroupH);
		
		return managerDAO.selectOne(
				"from TrDashboardGroupLastUpdateDate lud "
				+ "where lud.trDashboardGroupH = :dashboardGroupH ", params);
	}

	@Override
	public TrDashboardGroupD getDashboardGroupDByFileSourcePath(String dashboardName, String fileSourcePath) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_DASHBOARD_NAME, dashboardName);
		params.put(TrDashboardGroupD.PARAM_FILE_SOURCE_PATH, fileSourcePath);
		
		return managerDAO.selectOne(
				"from TrDashboardGroupD dgd "
				+ "join fetch dgd.trDashboardGroupH dgh " 
				+ "join fetch dgh.amMsuserCreator amc "
				+ "where dgh.dashboardGroupName = :dashboardName "
				+ "and dgd.fileSourcePath = :fileSourcePath ", params);
	}
	
	@Override
	public void updateDashboardD(TrDashboardGroupD dashboardGroupD) {
		managerDAO.update(dashboardGroupD);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDashboardDNewTrx(TrDashboardGroupD dashboardGroupD) {
		managerDAO.update(dashboardGroupD);
	}

	@Override
	public TrDashboardGroupD getDashboardGroupD(TrDashboardGroupH dashboardGroupH, String fileSourcePath) {
		Map<String, Object> params = new HashMap<>();
		params.put(PARAM_DASHBOARD_GROUP_H, dashboardGroupH);
		params.put(TrDashboardGroupD.PARAM_FILE_SOURCE_PATH, fileSourcePath);
		
		return managerDAO.selectOne(
				"from TrDashboardGroupD dgd "
				+ "join fetch dgd.trDashboardGroupH dgh "
				+ "join fetch dgd.lovProcessStatus lps "
				+ "where dgd.trDashboardGroupH = :dashboardGroupH "
				+ "and dgd.fileSourcePath = :fileSourcePath "
				+ "and dgd.isActive = '1' ", params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDashboardGroupD getDashboardGroupDNewTrx(TrDashboardGroupH dashboardGroupH, String fileSourcePath) {
		Map<String, Object> params = new HashMap<>();
		params.put(PARAM_DASHBOARD_GROUP_H, dashboardGroupH);
		params.put(TrDashboardGroupD.PARAM_FILE_SOURCE_PATH, fileSourcePath);
		
		return managerDAO.selectOne(
				"from TrDashboardGroupD dgd "
				+ "join fetch dgd.trDashboardGroupH dgh "
				+ "join fetch dgd.lovProcessStatus lps "
				+ "where dgd.trDashboardGroupH = :dashboardGroupH "
				+ "and dgd.fileSourcePath = :fileSourcePath "
				+ "and dgd.isActive = '1' ", params);
	}

	@Override
	public TrDashboardGroupD getDashboardGroupDByFilename(TrDashboardGroupH dashboardGroupH, String filename) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(PARAM_DASHBOARD_GROUP_H, dashboardGroupH);
		params.put("filename", filename);
		
		return managerDAO.selectOne(
				"from TrDashboardGroupD dgd "
				+ "join fetch dgd.trDashboardGroupH dgh "
				+ "join fetch dgd.lovProcessStatus lps "
				+ "where dgd.trDashboardGroupH = :dashboardGroupH "
				+ "and dgd.fileName = :filename "
				+ "and dgd.isActive = '1' ", params);
	}

	@Override
	public TrDashboardGroupD getDashboardGroupD(MsTenant tenant, String fileSourcePath) {
		Map<String, Object> params = new HashMap<>();
		params.put(PARAM_TENANT, tenant);
		params.put(TrDashboardGroupD.PARAM_FILE_SOURCE_PATH, fileSourcePath);
		
		return managerDAO.selectOne(
				"from TrDashboardGroupD dgd "
				+ "join fetch dgd.trDashboardGroupH dgh "
				+ "where dgh.msTenant = :tenant "
				+ "and dgd.fileSourcePath = :fileSourcePath "
				+ "and dgd.isActive = '1' ", params);
	}

	@Override
	public long countDashboardGroupD(TrDashboardGroupH dashboardGroupH, MsLov lovProcessStatus) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		params.put(MsLov.ID_LOV_HBM, lovProcessStatus.getIdLov());
		params.put(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1");
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_dashboard_group_d ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ")
			.append("and lov_process_status = :idLov ")
			.append("and is_active = :isActive ");
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0L;
		}
		
		return result.longValue();
	}

	@Override
	public List<OcrResultBean> getConsolidatedBankStatements(TrDashboardGroupH dashboardGroupH) {
		
		MsLov lovProcessStatus = managerDAO.selectOne("from MsLov ml where ml.lovGroup = :lovGroup and ml.code = :code ", new Object[][] {
			{MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_PROCESS_STATUS},
			{MsLov.CODE_HBM, GlobalVal.CODE_LOV_PROCESS_TYPE_CONSOLIDATED}
		});
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		params.put(MsLov.ID_LOV_HBM, lovProcessStatus.getIdLov());
		params.put(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1");
		
		StringBuilder query = new StringBuilder();
		query
			.append("select row_number() over(order by dgd.id_dashboard_group_d asc) as \"rowNum\", ")
			.append("dgd.file_name as filename, orh.account_bank as bank, ")
			.append("orh.account_number as \"accountNo\", account_name as \"accountName\" ")
			.append("from tr_dashboard_group_d dgd ")
			.append("join tr_ocr_result_header orh on dgd.id_dashboard_group_d = orh.id_dashboard_group_d ")
			.append("where dgd.id_dashboard_group_h = :idDashboardGroupH ")
			.append("and dgd.lov_process_status = :idLov ")
			.append("and dgd.is_active = :isActive ");
		
		return managerDAO.selectForListString(OcrResultBean.class, query.toString(), params, null);
	}

	@Override
	public Object[] getSupplierBuyerHeaderData(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select COALESCE(SUM(supplier_amount_by_amt), 0) as \"suppAmount\", ")
			.append("COALESCE(SUM(buyer_amount_by_amt), 0) as \"buyerAmount\", ")
			.append("CAST(COALESCE(SUM(supplier_freq_by_freq), 0) AS int) AS \"suppFreq\", ")
			.append("CAST(COALESCE(SUM(buyer_freq_by_freq), 0) AS int) AS \"buyerFreq\" ")
			.append("from tr_consolidate_result_dashboard_overall ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ");
		
		Object result = managerDAO.selectOneNativeString(query.toString(), params);
		return (Object[]) result;
	}

	@Override
	public List<Map<String, Object>> getSupplierBuyerCashFlowAndGrowthRate(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select TO_CHAR(period, 'YYYY-MM') as period, ")
			.append("ROUND(AVG(net_cash), 2) as \"netCash\", ")
			.append("SUM(buyer_amt_by_amt) as \"cashIn\", ")
			.append("SUM(supplier_amt_by_amt)*-1 as \"cashOut\", ")
			.append("MAX(growth_rate_supplier_buyer) as \"growthRate\" ")
			.append("from tr_consolidate_result_dashboard_monthly tcrdm ")
			.append("where tcrdm.id_dashboard_group_h = :idDashboardGroupH ")
			.append("group by period ")
			.append("order by period ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<SupplierBuyerCashFlowDetailBean> getSupplierBuyerCashFlowDetails(TrDashboardGroupH dashboardGroupH) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select TO_CHAR(period, 'YYYY-MM') as period, ")
			.append("sum(buyer_amt_by_amt) as \"cashIn\", ")
			.append("sum(supplier_amt_by_amt) as \"cashOut\", ")
			.append("max(growth_rate_supplier_buyer) as \"growthRate\", ")
			.append("sum(supplier_amt_by_amt) as \"totalSupplierAmount\", ")
			.append("max(avg_supplier_amt) as \"avgSupplierAmount\", ")
			.append("cast(sum(supplier_freq_by_amt) AS bigint) as \"noOfSupplierTrx\", ")
			.append("max(unique_days_supplier_transaction) as \"freqDaysOfSupplierTrx\", ")
			.append("sum(buyer_amt_by_amt) as \"totalBuyerAmount\", ")
			.append("max(avg_buyer_amt) as \"avgBuyerAmount\", ")
			.append("cast(sum(buyer_freq_by_amt) as bigint) as \"noOfBuyerTrx\", ")
			.append("max(unique_days_buyer_transaction) as \"freqDaysOfBuyerTrx\", ")
			.append("max(net_cash) as \"netCash\", ")
			.append("max(buyer_supplier_ratio) as \"buyerSupplierRatio\" ")
			.append("from tr_consolidate_result_dashboard_monthly tcrdm  ")
			.append("where tcrdm.id_dashboard_group_h = :idDashboardGroupH ")
			.append("group by period ")
			.append("order by period ");
		
		return managerDAO.selectForListString(SupplierBuyerCashFlowDetailBean.class, query.toString(), params, null);
	}

	@Override
	public List<Map<String, Object>> getSupplierBuyerGpmWcrLr(TrDashboardGroupH dashboardGroupH) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select TO_CHAR(period, 'YYYY-MM') as period, ")
			.append("round(avg(gross_profit_margin), 2) as \"profitMargin\", ")
			.append("max(gross_profit_margin_reg_line) as \"regressionLine\", ")
			.append("max(working_capital_ratio) as \"workingCapitalRatio\", ")
			.append("max(working_capital_ratio_reg_line) as \"workingCapitalRegressionLine\", ")
			.append("max(liquidity_ratio) as \"liquidityRatio\" ")
			.append("from tr_consolidate_result_dashboard_monthly tcrdm ")
			.append("where tcrdm.id_dashboard_group_h = :idDashboardGroupH ")
			.append("group by period ")
			.append("order by period ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getTopFiveSupplier(TrDashboardGroupH dashboardGroupH) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select supplier_name_by_amt, supplier_amount_by_amt, supplier_freq_by_amt, ")
			.append("supplier_name_by_freq, supplier_amount_by_freq, supplier_freq_by_freq ")
			.append("from tr_consolidate_result_dashboard_overall tcrdo ")
			.append("where tcrdo.id_dashboard_group_h = :idDashboardGroupH ")
			.append("and tcrdo.supplier_name_by_amt is not null ")
			.append("order by id_consolidate_result_dashboard_overall asc ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getTopFiveBuyer(TrDashboardGroupH dashboardGroupH) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select buyer_name_by_amt, buyer_amount_by_amt, buyer_freq_by_amt, ")
			.append("buyer_name_by_freq, buyer_amount_by_freq, buyer_freq_by_freq ")
			.append("from tr_consolidate_result_dashboard_overall tcrdo ")
			.append("where tcrdo.id_dashboard_group_h = :idDashboardGroupH ")
			.append("and tcrdo.buyer_name_by_amt is not null ")
			.append("order by id_consolidate_result_dashboard_overall asc ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> getLatestDashboardNames(MsTenant tenant, String isActive, int limitData) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
		params.put(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1");
		params.put("totalData", limitData);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select dashboard_group_name ")
			.append("from tr_dashboard_group_h ")
			.append("where id_ms_tenant = :idMsTenant ")
			.append("and is_active = :isActive ")
			.append("order by id_dashboard_group_h desc ")
			.append("limit :totalData ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public void flagDashboardGroupAsCompleted(MsTenant tenant, String isActive, int limitData) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
		params.put(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1");
		params.put("totalData", limitData);
		
		StringBuilder query = new StringBuilder();
		query
			.append("update tr_dashboard_group_d ")
			.append("set lov_process_status = (select id_lov from ms_lov where lov_group = 'PROCESS_STATUS' and code = 'COMPLETE') ")
			.append("where id_dashboard_group_h in ( ")
				.append("select id_dashboard_group_h ")
				.append("from tr_dashboard_group_h ")
				.append("where id_ms_tenant = :idMsTenant ")
				.append("and is_active = :isActive ")
				.append("order by id_dashboard_group_h desc ")
				.append("limit :totalData ")
			.append(")");
		
		managerDAO.updateNativeString(query.toString(), params);
		
	}

	@Override
	public List<Map<String, Object>> getLatestDeletableBankStatements(MsTenant tenant, int limitData) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
		params.put("limitData", limitData);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select dgh.dashboard_group_name, dtl.file_source_path ")
			.append("from tr_dashboard_group_h dgh ")
			.append("join lateral ( ")
				.append("select file_source_path ")
				.append("from tr_dashboard_group_d dgd ")
				.append("where dgd.id_dashboard_group_h = dgh.id_dashboard_group_h ")
				.append("and is_active = '1' ")
				.append("and lov_process_status != (select id_lov from ms_lov where lov_group = 'PROCESS_STATUS' and code = 'PENDING') ")
			.append(") as dtl on true ")
			.append("where id_ms_tenant = :idMsTenant ")
			.append("and dgh.is_active = '1' ")
			.append("order by dgh.id_dashboard_group_h desc limit :limitData ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public Object[] getGeneralInsightsHeaderData(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select max(opening_balance_overall) as opening, max(ending_balance_overall) as ending, ")
			.append("max(total_credit_transaction_amount) as \"creditAmount\", max(total_debit_transaction_amount) as \"debitAmount\", ")
			.append("max(total_credit_transaction) as \"creditCount\", max(total_debit_transaction) as \"debitCount\" ")
			.append("from tr_consolidate_result_dashboard_overall ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ");
		
		Object result = managerDAO.selectOneNativeString(query.toString(), params);
		return (Object[]) result;
	}

	@Override
	public List<Map<String, Object>> getGeneralInsightCashFlowAnalysis(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select to_char(period, 'yyyy-MM') as period, ") // d0
			.append("max(net_cash_credit_debit) as net_cash, ") // d1
			.append("max(total_credit_transaction_amount) as cash_in, ")  // d2
			.append("max(total_debit_transaction_amount) as cash_out, ") // d3
			.append("max(ending_balance) as ending_balance, ") // d4
			.append("max(total_credit_transaction_amount) as total_credit_amount, ") // d5
			.append("max(avg_credit_amount) as average_credit_amount, ") // d6
			.append("max(total_freq_credit_days) as freq_days_of_credit_trx, ") // d7
			.append("max(total_debit_transaction_amount) as total_debit_amount, ") // d8
			.append("max(avg_debit_amount) as average_debit_amount, ") // d9
			.append("max(total_freq_debit_days) as freq_days_of_debit_trx, ") // d10
			.append("max(max_ending_balance_amount) as highest_balance, ") // d11
			.append("max(min_ending_balance_amount) as lowest_balance, ") // d12
			.append("max(daily_avg_balance_amount) as daily_average_balance, ") // d13
			.append("max(total_credit_transaction) as credit_count, ") // d14
			.append("max(total_debit_transaction) as debit_count ") // d15
			.append("from tr_consolidate_result_dashboard_monthly ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ")
			.append("group by period ")
			.append("order by period ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public Object[] getGeneralBodyCardData(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select MAX(max_ending_balance_amount) as max_ending_balance, ")
			.append("TO_CHAR(MAX(max_ending_balance_date), 'dd Mon yyyy') as max_ending_balance_date, ")
			.append("MAX(min_ending_balance_amount) as min_ending_balance_amount, ")
			.append("TO_CHAR(MAX(min_ending_balance_date), 'dd Mon yyyy') as min_ending_balance_date, ")
			.append("MAX(monthly_avg_balance_amount) as monthly_avg_balance, ")
			.append("MAX(daily_avg_balance_amount) as daily_avg_balance, ")
			.append("MAX(growth_rate) as growth_rate ")
			.append("from tr_consolidate_result_dashboard_overall ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ");
		
		Object result = managerDAO.selectOneNativeString(query.toString(), params);
		return (Object[]) result;
	}

	@Override
	public List<Map<String, Object>> getGeneralDailyAnalysisData(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select to_char(date, 'yyyy-MM-dd'), ")
			.append("ending_balance, moving_avg_amount, net_cash, ")
			.append("total_credit_transaction_amount as cash_in, total_debit_transaction_amount as cash_out, ")
			.append("total_credit_transaction, total_debit_transaction ")
			.append("from tr_consolidate_result_dashboard_daily ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ")
			.append("order by id_consolidate_result_dashboard_daily ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public Object[] getGeneralInsightsCircularHeaderData(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select max(real_total_circular_transaction) as real_circ_count, ")
			.append("max(total_circular_transaction) as circ_count, ")
			.append("max(ratio_circular_transaction) as count_ratio, ")
			.append("max(real_total_circular_transaction_amount) as real_circ_amount, ")
			.append("max(total_circular_transaction_amount) as circ_amount, ")
			.append("max(ratio_circular_transaction_amount) as amount_ratio ")
			.append("from tr_consolidate_result_dashboard_overall ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ");
		
		Object result = managerDAO.selectOneNativeString(query.toString(), params);
		return (Object[]) result;
	}

	@Override
	public List<Map<String, Object>> getGeneralInsightsCircularChartData(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select to_char(period, 'yyyy-MM') as period, ")
			.append("max(real_total_circular_transaction_amount) as circ_amount, ")
			.append("max(total_circular_transaction_amount) as total_amount, ")
			.append("max(circular_percentage) as circ_percent ")
			.append("from tr_consolidate_result_dashboard_monthly ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ")
			.append("group by period ")
			.append("order by period ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public long countDashboardGroupDWithHighRedPercentage(TrDashboardGroupH dashboardGroupH) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("SELECT COUNT(1) ")
			.append("FROM ( ")
				.append("SELECT dgd.id_dashboard_group_d, ")
				.append("COUNT(CASE WHEN ord.is_red = '1' AND ord.is_edited = '0' AND ord.is_deleted = '0' THEN 1 END) * 100.0 / NULLIF(COUNT(CASE WHEN ord.is_deleted = '0' THEN 1 END), 0) AS red_percentage ")
				.append("FROM tr_dashboard_group_d dgd ")
				.append("JOIN tr_ocr_result_detail ord ON dgd.id_dashboard_group_d = ord.id_dashboard_group_d ")
				.append("WHERE dgd.id_dashboard_group_h = :idDashboardGroupH ")
				.append("GROUP BY dgd.id_dashboard_group_d ")
			.append(") AS subquery ")
			.append("WHERE red_percentage >= 10.0 ");
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0L;
		}
		
		return result.longValue();
	}

	@Override
	public BigDecimal getDashboardGreatestCircularRatio(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("SELECT GREATEST(MAX(ratio_circular_transaction), MAX(ratio_circular_transaction_amount)) ")
			.append("FROM tr_consolidate_result_dashboard_overall ")
			.append("WHERE id_dashboard_group_h = :idDashboardGroupH ");
		
		BigDecimal result = (BigDecimal) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return BigDecimal.valueOf(0.0);
		}
		
		return result;
	}

}
