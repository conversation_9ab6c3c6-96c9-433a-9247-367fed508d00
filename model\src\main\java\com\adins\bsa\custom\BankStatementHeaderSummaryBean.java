package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigDecimal;

public class BankStatementHeaderSummaryBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private String period; 
	private BigDecimal beginningBalance;
	private BigDecimal endingBalance;
	
	public String getPeriod() {
		return period;
	}
	public void setPeriod(String period) {
		this.period = period;
	}
	public BigDecimal getBeginningBalance() {
		return beginningBalance;
	}
	public void setBeginningBalance(BigDecimal beginningBalance) {
		this.beginningBalance = beginningBalance;
	}
	public BigDecimal getEndingBalance() {
		return endingBalance;
	}
	public void setEndingBalance(BigDecimal endingBalance) {
		this.endingBalance = endingBalance;
	}   
	
	
}
