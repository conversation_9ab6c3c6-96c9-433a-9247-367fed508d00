package com.adins.bsa.custom;

import java.io.Serializable;

public class MenuUserBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private String code;
	private int order;
	private String prompt;
	private String path;
	private String icon;
	private String css;
	private String isHidden;
	private String isExternalLink;
	private String params;
	private String parentCode;
	
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public int getOrder() {
		return order;
	}
	public void setOrder(int order) {
		this.order = order;
	}
	public String getPrompt() {
		return prompt;
	}
	public void setPrompt(String prompt) {
		this.prompt = prompt;
	}
	public String getPath() {
		return path;
	}
	public void setPath(String path) {
		this.path = path;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public String getCss() {
		return css;
	}
	public void setCss(String css) {
		this.css = css;
	}
	public String getIsHidden() {
		return isHidden;
	}
	public void setIsHidden(String isHidden) {
		this.isHidden = isHidden;
	}
	public String getIsExternalLink() {
		return isExternalLink;
	}
	public void setIsExternalLink(String isExternalLink) {
		this.isExternalLink = isExternalLink;
	}
	public String getParams() {
		return params;
	}
	public void setParams(String params) {
		this.params = params;
	}
	public String getParentCode() {
		return parentCode;
	}
	public void setParentCode(String parentCode) {
		this.parentCode = parentCode;
	}
	
}
