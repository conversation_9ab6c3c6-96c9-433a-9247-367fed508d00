package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

public class SupplierBuyerCashFlowDetailBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private String period;
	private BigDecimal cashIn;
	private BigDecimal cashOut;
	private BigDecimal growthRate;
	private BigDecimal totalSupplierAmount;
	private BigDecimal avgSupplierAmount;
	private BigInteger noOfSupplierTrx;
	private BigInteger freqDaysOfSupplierTrx;
	private BigDecimal totalBuyerAmount;
	private BigDecimal avgBuyerAmount;
	private BigInteger noOfBuyerTrx;
	private BigInteger freqDaysOfBuyerTrx;
	private BigDecimal netCash;
	private BigDecimal buyerSupplierRatio;
	
	public String getPeriod() {
		return period;
	}
	public void setPeriod(String period) {
		this.period = period;
	}
	public BigDecimal getCashIn() {
		return cashIn;
	}
	public void setCashIn(BigDecimal cashIn) {
		this.cashIn = cashIn;
	}
	public BigDecimal getCashOut() {
		return cashOut;
	}
	public void setCashOut(BigDecimal cashOut) {
		this.cashOut = cashOut;
	}
	public BigDecimal getGrowthRate() {
		return growthRate;
	}
	public void setGrowthRate(BigDecimal growthRate) {
		this.growthRate = growthRate;
	}
	public BigDecimal getTotalSupplierAmount() {
		return totalSupplierAmount;
	}
	public void setTotalSupplierAmount(BigDecimal totalSupplierAmount) {
		this.totalSupplierAmount = totalSupplierAmount;
	}
	public BigDecimal getAvgSupplierAmount() {
		return avgSupplierAmount;
	}
	public void setAvgSupplierAmount(BigDecimal avgSupplierAmount) {
		this.avgSupplierAmount = avgSupplierAmount;
	}
	public BigInteger getNoOfSupplierTrx() {
		return noOfSupplierTrx;
	}
	public void setNoOfSupplierTrx(BigInteger noOfSupplierTrx) {
		this.noOfSupplierTrx = noOfSupplierTrx;
	}
	public BigInteger getFreqDaysOfSupplierTrx() {
		return freqDaysOfSupplierTrx;
	}
	public void setFreqDaysOfSupplierTrx(BigInteger freqDaysOfSupplierTrx) {
		this.freqDaysOfSupplierTrx = freqDaysOfSupplierTrx;
	}
	public BigDecimal getTotalBuyerAmount() {
		return totalBuyerAmount;
	}
	public void setTotalBuyerAmount(BigDecimal totalBuyerAmount) {
		this.totalBuyerAmount = totalBuyerAmount;
	}
	public BigDecimal getAvgBuyerAmount() {
		return avgBuyerAmount;
	}
	public void setAvgBuyerAmount(BigDecimal avgBuyerAmount) {
		this.avgBuyerAmount = avgBuyerAmount;
	}
	public BigInteger getNoOfBuyerTrx() {
		return noOfBuyerTrx;
	}
	public void setNoOfBuyerTrx(BigInteger noOfBuyerTrx) {
		this.noOfBuyerTrx = noOfBuyerTrx;
	}
	public BigInteger getFreqDaysOfBuyerTrx() {
		return freqDaysOfBuyerTrx;
	}
	public void setFreqDaysOfBuyerTrx(BigInteger freqDaysOfBuyerTrx) {
		this.freqDaysOfBuyerTrx = freqDaysOfBuyerTrx;
	}
	public BigDecimal getNetCash() {
		return netCash;
	}
	public void setNetCash(BigDecimal netCash) {
		this.netCash = netCash;
	}
	public BigDecimal getBuyerSupplierRatio() {
		return buyerSupplierRatio;
	}
	public void setBuyerSupplierRatio(BigDecimal buyerSupplierRatio) {
		this.buyerSupplierRatio = buyerSupplierRatio;
	}
	
}
