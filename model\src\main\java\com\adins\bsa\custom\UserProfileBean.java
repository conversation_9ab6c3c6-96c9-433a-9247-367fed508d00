package com.adins.bsa.custom;

import java.io.Serializable;
import java.util.List;

public class UserProfileBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private String loginId;
	private String fullname;
	private String changePwdLogin;
	private List<UserRoleBean> roles;
	
	public String getLoginId() {
		return loginId;
	}
	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}
	public String getFullname() {
		return fullname;
	}
	public void setFullname(String fullname) {
		this.fullname = fullname;
	}
	public String getChangePwdLogin() {
		return changePwdLogin;
	}
	public void setChangePwdLogin(String changePwdLogin) {
		this.changePwdLogin = changePwdLogin;
	}
	public List<UserRoleBean> getRoles() {
		return roles;
	}
	public void setRoles(List<UserRoleBean> roles) {
		this.roles = roles;
	}
	
}
