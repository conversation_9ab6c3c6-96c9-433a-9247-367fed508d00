package com.adins.bsa.businesslogic.api;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.bsa.webservices.model.user.CheckUserForgotPasswordRequest;
import com.adins.bsa.webservices.model.user.ResetPasswordRequest;
import com.adins.bsa.webservices.model.user.UserMenuRequest;
import com.adins.bsa.webservices.model.user.UserMenuResponse;
import com.adins.bsa.webservices.model.user.UserProfileRequest;
import com.adins.bsa.webservices.model.user.UserProfileResponse;
import com.adins.bsa.webservices.model.user.VerifyOtpRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface UserLogic {
	
	@PreAuthorize("@bsaSecurityServices.isValidUser(#audit.callerId, authentication)")
	UserProfileResponse getUserProfile(UserProfileRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	UserMenuResponse getUserMenu(UserMenuRequest request, AuditContext audit);
	
	MssResponseType checkUserForgotPassword(CheckUserForgotPasswordRequest request, AuditContext audit);
	MssResponseType sendOtpForgotPassword(CheckUserForgotPasswordRequest request, AuditContext audit);
	MssResponseType verifyOtpForgotPassword(VerifyOtpRequest request, AuditContext audit);
	MssResponseType resetPassword(ResetPasswordRequest request, AuditContext audit);
}
