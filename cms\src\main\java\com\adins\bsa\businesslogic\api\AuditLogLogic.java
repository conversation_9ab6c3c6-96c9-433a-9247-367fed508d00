package com.adins.bsa.businesslogic.api;

import com.adins.bsa.constants.enums.DashboardAction;
import com.adins.bsa.constants.enums.DashboardModule;
import com.adins.bsa.custom.AuditLogDetailBean;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface AuditLogLogic {
    
    void insertAuditLog(DashboardModule module, DashboardAction action, AuditLogDetailBean logDetail, AuditContext audit);
    void insertAuditLogNewTrx(DashboardModule module, DashboardAction action, AuditLogDetailBean logDetail, AuditContext audit);
}
