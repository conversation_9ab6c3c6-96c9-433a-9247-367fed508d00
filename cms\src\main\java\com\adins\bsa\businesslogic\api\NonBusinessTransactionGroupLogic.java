package com.adins.bsa.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.AddNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.DeleteNonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupDetailResponse;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListNonBusinessTransactionGroupResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface NonBusinessTransactionGroupLogic {
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	ListNonBusinessTransactionGroupResponse getListNonBusinessTransactionGroup(GenericDashboardPagingRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType addNonBusinessTransactionGroup(AddNonBusinessTransactionGroupRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType deleteNonBusinessTransactionGroup(DeleteNonBusinessTransactionGroupRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	ListNonBusinessTransactionGroupDetailResponse getListNonBusinessTransactionGroupDetail(ListNonBusinessTransactionGroupDetailRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType deleteNonBusinessTransactionGroupDetail(DeleteNonBusinessTransactionGroupDetailRequest request, AuditContext audit);

	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication)")
	MssResponseType addNonBusinessTransactionGroupDetail(AddNonBusinessTransactionGroupDetailRequest request, AuditContext audit);

}
