package com.adins.am.model.custom;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.StringUtils;

@MappedSuperclass
public abstract class CreateableEntity {
	protected String usrCrt;
	protected Date dtmCrt;

	@Column(name = "usr_crt", nullable = false, length = 36)
	public String getUsrCrt() {
		return this.usrCrt;
	}

	public void setUsrCrt(String usrCrt) {
		this.usrCrt = StringUtils.left(usrCrt, 36);
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "dtm_crt", nullable = false, length = 29)
	public Date getDtmCrt() {
		return this.dtmCrt;
	}

	public void setDtmCrt(Date dtmCrt) {
		this.dtmCrt = dtmCrt;
	}
}
