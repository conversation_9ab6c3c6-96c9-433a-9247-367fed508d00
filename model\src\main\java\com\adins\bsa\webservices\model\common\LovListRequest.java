package com.adins.bsa.webservices.model.common;

import java.io.Serializable;

import com.adins.framework.service.base.model.MssRequestType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Objek LOV List REQUEST")
public class LovListRequest extends MssRequestType implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "Di isi dengan string lovGroup", example = "SIGNER_TYPE", required = true)
	private String lovGroup;
	@ApiModelProperty(value = "Di kosongkan atau isi dengan string constraint")
	private String constraint1;
	@ApiModelProperty(value = "Di kosongkan atau isi dengan string constraint")
	private String constraint2;
	@ApiModelProperty(value = "Di kosongkan atau isi dengan string constraint")
	private String constraint3;
	@ApiModelProperty(value = "Di kosongkan atau isi dengan string constraint")
	private String constraint4;
	@ApiModelProperty(value = "Di kosongkan atau isi dengan string constraint")
	private String constraint5;
	
	@ApiModelProperty(value = "Di kosongkan atau isi dengan msg")
	private String msg;
	
	public String getLovGroup() {
		return lovGroup;
	}
	public void setLovGroup(String lovGroup) {
		this.lovGroup = lovGroup;
	}
	public String getConstraint1() {
		return constraint1;
	}
	public void setConstraint1(String constraint1) {
		this.constraint1 = constraint1;
	}
	public String getConstraint2() {
		return constraint2;
	}
	public void setConstraint2(String constraint2) {
		this.constraint2 = constraint2;
	}
	public String getConstraint3() {
		return constraint3;
	}
	public void setConstraint3(String constraint3) {
		this.constraint3 = constraint3;
	}
	public String getConstraint4() {
		return constraint4;
	}
	public void setConstraint4(String constraint4) {
		this.constraint4 = constraint4;
	}
	public String getConstraint5() {
		return constraint5;
	}
	public void setConstraint5(String constraint5) {
		this.constraint5 = constraint5;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	
	
}
