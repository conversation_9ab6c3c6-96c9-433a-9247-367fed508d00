package com.adins.bsa.businesslogic.impl;

import java.util.Iterator;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.stringtemplate.v4.ST;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.businesslogic.api.MessageTemplateLogic;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.model.MsMsgTemplate;

@Component
public class GenericMessageTemplateLogic extends BaseLogic implements MessageTemplateLogic {
	
	private static final char DELIMITER_START = '{';
	private static final char DELIMITER_STOP = '}';

	@Override
	public MsMsgTemplate getAndParseContent(String msgTemplateCode, Map<String, Object> templateParameters) {
		if (StringUtils.isEmpty(msgTemplateCode)) {
			return null;
		}
		
		MsMsgTemplate template = daoFactory.getMessageTemplateDao().getMessageTemplate(GlobalVal.TEMPLATE_TYPE_EMAIL, msgTemplateCode);
		if (null == template) {
			return null;
		}
		
		// Initialize new object because dirty object committed if use same object
		MsMsgTemplate returnTemplate = new MsMsgTemplate();
		
		ST templateBody = new ST(template.getBody(), DELIMITER_START, DELIMITER_STOP);
		this.setParamsToSt(templateParameters, templateBody);					
		String formattedContent = templateBody.render();
		returnTemplate.setBody(formattedContent);
		
		if (StringUtils.isNotBlank(template.getSubject())) {
			ST templateSubject = new ST(template.getSubject(), DELIMITER_START, DELIMITER_STOP);
			this.setParamsToSt(templateParameters, templateSubject);
			String formattedSubject = templateSubject.render();
			returnTemplate.setSubject(formattedSubject);
		}
		
		return returnTemplate;
	}
	
	private void setParamsToSt(Map<String, Object> templateParameters, ST template) {
		for (Iterator<String> iterator = templateParameters.keySet().iterator(); iterator.hasNext();) {
			String key = iterator.next();
			template.add(key, templateParameters.get(key));
		}
	}

}
