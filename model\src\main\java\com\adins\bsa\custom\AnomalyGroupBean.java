package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigInteger;

public class AnomalyGroupBean implements Serializable {
	private static final long serialVersionUID = 1L;

    private BigInteger no;
	private String reason;
	private String reasonCode;
	private String risk;
    private BigInteger trxCount;
    private String createdDate;
    private String updateDate;

    public BigInteger getNo() {
        return no;
    }
    public void setNo(BigInteger no) {
        this.no = no;
    }
    public String getReason() {
        return reason;
    }
    public void setReason(String reason) {
        this.reason = reason;
    }
    public String getReasonCode() {
        return reasonCode;
    }
    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }
    public String getRisk() {
        return risk;
    }
    public void setRisk(String risk) {
        this.risk = risk;
    }
    public BigInteger getTrxCount() {
        return trxCount;
    }
    public void setTrxCount(BigInteger trxCount) {
        this.trxCount = trxCount;
    }
    public String getCreatedDate() {
        return createdDate;
    }
    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }
    public String getUpdateDate() {
        return updateDate;
    }
    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }
    
}
