package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.circular.AddCircularGroupRequest;
import com.adins.bsa.webservices.model.circular.CheckCircularGroupNameRequest;
import com.adins.bsa.webservices.model.circular.ListCircularGroupRequest;
import com.adins.bsa.webservices.model.circular.ListCircularGroupResponse;
import com.adins.bsa.webservices.model.circular.ListCircularTransactionRequest;
import com.adins.bsa.webservices.model.circular.ListCircularTransactionResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface CircularService {
	ListCircularGroupResponse getListCircularGroup(ListCircularGroupRequest request);
	ListCircularTransactionResponse getListCircularTransaction(ListCircularTransactionRequest request);
	MssResponseType checkCircularGroupName(CheckCircularGroupNameRequest request);
	MssResponseType addCircularGroup(AddCircularGroupRequest request);
	MssResponseType deleteCircularGroup(CheckCircularGroupNameRequest request);
}
