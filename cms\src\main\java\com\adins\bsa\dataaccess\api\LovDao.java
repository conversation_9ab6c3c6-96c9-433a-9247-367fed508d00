package com.adins.bsa.dataaccess.api;

import java.util.List;
import java.util.Map;

import com.adins.bsa.model.MsLov;
import com.adins.bsa.webservices.model.common.LovListRequest;

public interface LovDao {
	
	MsLov getLovByGroupAndCode(String lovGroup, String code);
	MsLov getLovByGroupAndId(String lovGroup, long idLov);
	
	String getHighestSequenceLovCode(String lovGroup);
	String getLowestSequenceLovCode(String lovGroup);
	
	List<Map<String, Object>> getMsLovListByGroupAndConstraint(LovListRequest request);
}
