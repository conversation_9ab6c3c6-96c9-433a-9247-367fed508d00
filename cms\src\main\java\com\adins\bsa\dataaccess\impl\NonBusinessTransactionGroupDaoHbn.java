package com.adins.bsa.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.NonBusinessTransactionGroupDetailBean;
import com.adins.bsa.custom.NonBusinessTransactionGroupBean;
import com.adins.bsa.dataaccess.api.NonBusinessTransactionGroupDao;
import com.adins.bsa.model.TrConsolidateResultNonbusinessGroupingD;
import com.adins.bsa.model.TrConsolidateResultNonbusinessGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.framework.persistence.dao.model.AuditContext; 

@Component
@Transactional
public class NonBusinessTransactionGroupDaoHbn extends BaseDaoHbn implements NonBusinessTransactionGroupDao {

	@Override
	public void insertConsolidateResultNonbusinessGroupingH(TrConsolidateResultNonbusinessGroupingH consolidateResultNonbusinessGroupingH) {
		managerDAO.insert(consolidateResultNonbusinessGroupingH);
	}

	@Override
	public void updateConsolidateResultNonbusinessGroupingH(TrConsolidateResultNonbusinessGroupingH consolidateResultNonbusinessGroupingH) {
		managerDAO.update(consolidateResultNonbusinessGroupingH);
	}
	
	@Override
	@Transactional(readOnly = true)
	public List<NonBusinessTransactionGroupBean> getListNonBusinessTransactionGroup(TrDashboardGroupH dashboardGroupH, int min, int max) {
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalKey.PAGING_PARAM_MIN, min);
		params.put(GlobalKey.PAGING_PARAM_MAX, max);
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by crngh.dtm_upd desc) as \"rowNum\", ")
				.append("coalesce(crngh.group_name_edited, crngh.group_name) AS \"groupName\",")
				.append("to_char(crngh.dtm_crt, 'DD/MM/YYYY HH24:MI') as \"createdDate\", ")
				.append("coalesce(to_char(crngh.dtm_upd, 'DD/MM/YYYY HH24:MI'), '-')  as \"lastUpdated\" ")
				.append("from tr_consolidate_result_nonbusiness_grouping_h crngh ")
				.append("where 1=1 ")
				.append("and crngh.id_dashboard_group_h = :idDashboardGroupH ")
				.append("and crngh.is_deleted != '1'")
			.append(")")
			.append("select \"rowNum\",\"groupName\", \"createdDate\", \"lastUpdated\"")
			.append("from cte ")
			.append("where \"rowNum\" between :min and :max ")
	     	.append("order by \"lastUpdated\" desc ");
		
		return managerDAO.selectForListString(NonBusinessTransactionGroupBean.class, query.toString(), params, null);
	}

	@Override
	public long countListNonBusinessTransactionGroup(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_consolidate_result_nonbusiness_grouping_h crngh ")
			.append("where 1=1 ")
			.append("and crngh.id_dashboard_group_h = :idDashboardGroupH ")
			.append("and crngh.is_deleted != '1'");
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		return result.longValue();
	}

	@Override
	public TrConsolidateResultNonbusinessGroupingH getGroupingH(String groupName, TrDashboardGroupH dashboardGroupH) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("groupName", groupName);
		params.put("dashboardGroupH", dashboardGroupH);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultNonbusinessGroupingH ngh "
				+ "join fetch ngh.trDashboardGroupH dgh "
				+ "where ngh.groupName = :groupName "
				+ "and ngh.trDashboardGroupH = :dashboardGroupH ", params);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrConsolidateResultNonbusinessGroupingD> getGroupingDs(TrConsolidateResultNonbusinessGroupingH groupingH) {
		Map<String, Object> params = new HashMap<>();
		params.put("groupingH", groupingH);
		
		return (List<TrConsolidateResultNonbusinessGroupingD>) managerDAO.list(
				"from TrConsolidateResultNonbusinessGroupingD gd "
				+ "join fetch gd.trConsolidateResultNonbusinessGroupingH gh "
				+ "join fetch gd.trOcrResultDetail ord "
				+ "where gd.trConsolidateResultNonbusinessGroupingH = :groupingH ", params).get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public void updateGroupingD(TrConsolidateResultNonbusinessGroupingD groupingD) {
		managerDAO.update(groupingD);
	}

	@Override
	public List<NonBusinessTransactionGroupDetailBean> getListNonBusinessTransactionGRoupDetail(TrConsolidateResultNonbusinessGroupingH groupingH, int min, int max) {
		Map<String, Object> params = new HashMap<>();
		params.put("idGroupingH", groupingH.getIdConsolidateResultNonbusinessGroupingH());
		params.put(GlobalKey.PAGING_PARAM_MIN, min);
		params.put(GlobalKey.PAGING_PARAM_MAX, max);
		
		StringBuilder query = new StringBuilder();
		query
			.append("with cte as ( ")
				.append("select row_number() over(order by ngd.dtm_upd desc) as row, dgd.file_name as file, ")
				.append("ord.result_detail_id as \"detailId\", ")
				.append("orh.account_number as \"accountNo\", to_char(ord.transaction_date, 'DD/MM/YYYY') as date, ")
				.append("ord.transaction_desc as \"description\", ord.transaction_amount as amount, ord.transaction_type as type ")
				.append("from tr_consolidate_result_nonbusiness_grouping_d ngd ")
				.append("join tr_ocr_result_detail ord on ngd.id_ocr_result_detail = ord.id_ocr_result_detail ")
				.append("join tr_dashboard_group_d dgd on ord.id_dashboard_group_d = dgd.id_dashboard_group_d ")
				.append("join tr_ocr_result_header orh on ord.id_ocr_result_header = orh.id_ocr_result_header ")
				.append("where ngd.id_consolidate_result_nonbusiness_grouping_h = :idGroupingH ")
				.append("and ngd.is_deleted = '0' ")
			.append(") ")
			.append("select * from cte ")
			.append("where row between :min and :max ");
		
		return managerDAO.selectForListString(NonBusinessTransactionGroupDetailBean.class, query.toString(), params, null);
	}

	@Override
	public long countListNonBusinessTransactionGRoupDetail(TrConsolidateResultNonbusinessGroupingH groupingH) {
		Map<String, Object> params = new HashMap<>();
		params.put("idGroupingH", groupingH.getIdConsolidateResultNonbusinessGroupingH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_consolidate_result_nonbusiness_grouping_d ngd ")
			.append("join tr_ocr_result_detail ord on ngd.id_ocr_result_detail = ord.id_ocr_result_detail ")
			.append("join tr_dashboard_group_d dgd on ord.id_dashboard_group_d = dgd.id_dashboard_group_d ")
			.append("join tr_ocr_result_header orh on ord.id_ocr_result_header = orh.id_ocr_result_header ")
			.append("where ngd.id_consolidate_result_nonbusiness_grouping_h = :idGroupingH ")
			.append("and ngd.is_deleted = '0' ");
		
		BigInteger totalData = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		return totalData.longValue();
	}

	@Override
	public TrConsolidateResultNonbusinessGroupingH getGroupingH(String groupName, TrDashboardGroupH dashboardGroupH, String isDeleted) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("groupName", groupName);
		params.put("dashboardGroupH", dashboardGroupH);
		params.put(GlobalVal.QUERY_PARAM_IS_DELETED, isDeleted);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultNonbusinessGroupingH ngh "
				+ "join fetch ngh.trDashboardGroupH dgh "
				+ "where ngh.groupName = :groupName "
				+ "and ngh.trDashboardGroupH = :dashboardGroupH "
				+ "and ngh.isDeleted = :isDeleted ", params);
	}

	@Override
	public TrConsolidateResultNonbusinessGroupingD getGroupingD(TrConsolidateResultNonbusinessGroupingH groupingH, String resultDetailId, String isDeleted) {
		Map<String, Object> params = new HashMap<>();
		params.put("groupingH", groupingH);
		params.put("resultDetailId", resultDetailId);
		params.put(GlobalVal.QUERY_PARAM_IS_DELETED, isDeleted);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultNonbusinessGroupingD ngd "
				+ "join fetch ngd.trOcrResultDetail rd "
				+ "where ngd.trConsolidateResultNonbusinessGroupingH = :groupingH "
				+ "and rd.resultDetailId = :resultDetailId "
				+ "and ngd.isDeleted = :isDeleted ", params);
	}

	@Override
	public void deleteGroupingD(TrConsolidateResultNonbusinessGroupingD groupingD) {
		managerDAO.delete(groupingD);
	}

	@Override
	public void deleteGroupingDByGroupingH(TrConsolidateResultNonbusinessGroupingH groupingH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultNonbusinessGroupingH.PARAM_ID_GROUPING_H, groupingH.getIdConsolidateResultNonbusinessGroupingH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("delete from tr_consolidate_result_nonbusiness_grouping_d ")
			.append("where id_consolidate_result_nonbusiness_grouping_h = :idGroupingH ");
		
		managerDAO.deleteNativeString(query.toString(), params);
	}

	@Override
	public void deleteConsolidateResultNonbusinessGroupingH(TrConsolidateResultNonbusinessGroupingH consolidateResultNonbusinessGroupingH) {
		managerDAO.delete(consolidateResultNonbusinessGroupingH);
	}

	@Override
	public void deleteGroupingDByGroupingHAndIsUserEdited(TrConsolidateResultNonbusinessGroupingH groupingH, String isUserEdited) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultNonbusinessGroupingH.PARAM_ID_GROUPING_H, groupingH.getIdConsolidateResultNonbusinessGroupingH());
		params.put("isUserEdited", isUserEdited);
		
		StringBuilder query = new StringBuilder();
		query
			.append("delete from tr_consolidate_result_nonbusiness_grouping_d ")
			.append("where id_consolidate_result_nonbusiness_grouping_h = :idGroupingH ")
			.append("and is_user_edited = :isUserEdited ");
		
		managerDAO.deleteNativeString(query.toString(), params);
	}
	
	@Override
	public void updateGroupingDetailIsDeletedByGroupingHeaderAndIsUserEdited(TrConsolidateResultNonbusinessGroupingH groupingH, String isUserEdited, String isDeleted, AuditContext audit) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrConsolidateResultNonbusinessGroupingH.PARAM_ID_GROUPING_H, groupingH.getIdConsolidateResultNonbusinessGroupingH());
		params.put("isUserEdited", isUserEdited);
		params.put(GlobalVal.QUERY_PARAM_IS_DELETED, isDeleted);
		params.put("usrUpd", audit.getCallerId());
		
		StringBuilder query = new StringBuilder();
		query
			.append("update tr_consolidate_result_nonbusiness_grouping_d ")
			.append("set is_deleted = :isDeleted , ")
			.append("is_user_edited = '1', ")
			.append("usr_upd = :usrUpd , ")
			.append("dtm_upd = now() ")
			.append("where id_consolidate_result_nonbusiness_grouping_h = :idGroupingH ")
			.append("and is_user_edited = :isUserEdited ");
		
		managerDAO.updateNativeString(query.toString(), params);
	}

	@Override
	public TrConsolidateResultNonbusinessGroupingD getGroupingD(TrOcrResultDetail ocrResultDetail, String isDeleted) {
		Map<String, Object> params = new HashMap<>();
		params.put("ocrResultDetail", ocrResultDetail);
		params.put(GlobalVal.QUERY_PARAM_IS_DELETED, isDeleted);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultNonbusinessGroupingD gd "
				+ "join fetch gd.trConsolidateResultNonbusinessGroupingH gh "
				+ "join fetch gd.trOcrResultDetail "
				+ "where gd.trOcrResultDetail = :ocrResultDetail "
				+ "and gd.isDeleted = :isDeleted ", params);
	}

	@Override
	public void insertGroupingD(TrConsolidateResultNonbusinessGroupingD groupingD) {
		managerDAO.insert(groupingD);
	}
	
	
}
