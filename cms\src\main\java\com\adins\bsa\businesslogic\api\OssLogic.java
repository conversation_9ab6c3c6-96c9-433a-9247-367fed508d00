package com.adins.bsa.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import com.adins.bsa.webservices.model.aliyun.OssSignatureResponse;
import com.adins.framework.service.base.model.MssRequestType;

public interface OssLogic {
	@RolesAllowed("ROLE_DASHBOARD")
	OssSignatureResponse generateSignature(MssRequestType request);
	
	String generateDownloadSignedUrl(String filePath);
	boolean isFileExist(String filePath);
}
