package com.adins.bsa.validator.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrDashboardGroupD;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.validator.api.DashboardValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericDashboardValidator extends BaseLogic implements DashboardValidator {
	
	private static final String CONST_FILENAME = "Filename";
	private static final String CONST_BANK_STATEMENT = "Bank statement";

	@Override
	public TrDashboardGroupH getDashboardGroupH(MsTenant tenant, String dashboardName, boolean objectMustExists, AuditContext audit) {
		
		if (StringUtils.isBlank(dashboardName)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Dashboard name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrDashboardGroupH dashboardGroupH = daoFactory.getDashboardGroupDao().getDashboardGroupH(dashboardName, tenant);
		if (null == dashboardGroupH && objectMustExists) {
			throw new CommonException(getMessage("businesslogic.global.objectnotfound1", new String[] {"Dashboard"}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return dashboardGroupH;
	}
	
	@Override
	public TrDashboardGroupH getDashboardGroupHNewTrx(MsTenant tenant, String dashboardName, boolean objectMustExists, AuditContext audit) {
		if (StringUtils.isBlank(dashboardName)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Dashboard name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrDashboardGroupH dashboardGroupH = daoFactory.getDashboardGroupDao().getDashboardGroupHNewTrx(dashboardName, tenant);
		if (null == dashboardGroupH && objectMustExists) {
			throw new CommonException(getMessage("businesslogic.global.objectnotfound1", new String[] {"Dashboard"}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return dashboardGroupH;
	}

	@Override
	public TrDashboardGroupD getDashboardGroupD(TrDashboardGroupH groupH, String fileSourcePath, boolean objectMustExists, AuditContext audit) {
		
		if (StringUtils.isBlank(fileSourcePath)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_FILENAME}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrDashboardGroupD dashboardGroupD = daoFactory.getDashboardGroupDao().getDashboardGroupD(groupH, fileSourcePath);
		if (null == dashboardGroupD && objectMustExists) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {CONST_BANK_STATEMENT}, audit),  ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return dashboardGroupD;
	}
	
	@Override
	public TrDashboardGroupD getDashboardGroupDNewTrx(TrDashboardGroupH groupH, String fileSourcePath, boolean objectMustExists, AuditContext audit) {
		if (StringUtils.isBlank(fileSourcePath)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_FILENAME}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrDashboardGroupD dashboardGroupD = daoFactory.getDashboardGroupDao().getDashboardGroupDNewTrx(groupH, fileSourcePath);
		if (null == dashboardGroupD && objectMustExists) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {CONST_BANK_STATEMENT}, audit),  ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return dashboardGroupD;
	}

	@Override
	public TrDashboardGroupD getDashboardGroupD(MsTenant tenant, String fileSourcePath, boolean objectMustExists, AuditContext audit) {
		
		if (StringUtils.isBlank(fileSourcePath)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {CONST_FILENAME}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrDashboardGroupD dashboardGroupD = daoFactory.getDashboardGroupDao().getDashboardGroupD(tenant, fileSourcePath);
		if (null == dashboardGroupD && objectMustExists) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {CONST_BANK_STATEMENT}, audit),  ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return dashboardGroupD;
	}

}
