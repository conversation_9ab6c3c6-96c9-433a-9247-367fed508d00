package com.adins.bsa.webservices.model.dashboard;

import com.adins.framework.service.base.model.MssRequestType;

public class GenericDashboardPagingRequest extends MssRequestType {
 
	private static final long serialVersionUID = 1L;
	private String tenantCode;
	private String dashboardName; 
	private int page;
	
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getDashboardName() {
		return dashboardName;
	}
	public void setDashboardName(String dashboardName) {
		this.dashboardName = dashboardName;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}

}
