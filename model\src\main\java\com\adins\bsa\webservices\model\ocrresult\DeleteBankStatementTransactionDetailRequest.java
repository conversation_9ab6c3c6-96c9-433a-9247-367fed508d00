package com.adins.bsa.webservices.model.ocrresult;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class DeleteBankStatementTransactionDetailRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	private String fileSourcePath;
	private String resultDetailId;
	
	public String getFileSourcePath() {
		return fileSourcePath;
	}
	public void setFileSourcePath(String fileSourcePath) {
		this.fileSourcePath = fileSourcePath;
	}
	public String getResultDetailId() {
		return resultDetailId;
	}
	public void setResultDetailId(String resultDetailId) {
		this.resultDetailId = resultDetailId;
	}
	
}
