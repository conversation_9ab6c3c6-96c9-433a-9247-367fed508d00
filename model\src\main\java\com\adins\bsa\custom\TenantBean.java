package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigInteger;

public class TenantBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private BigInteger row;
	private String tenantName;
	private String tenantCode;
	private String isActive;
	
	public BigInteger getRow() {
		return row;
	}
	public void setRow(BigInteger row) {
		this.row = row;
	}
	public String getTenantName() {
		return tenantName;
	}
	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getIsActive() {
		return isActive;
	}
	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}
	
}
