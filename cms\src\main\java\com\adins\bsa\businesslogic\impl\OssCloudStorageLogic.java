package com.adins.bsa.businesslogic.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.CloudStorageLogic;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.model.MsTenant;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.utils.IOUtils;
import com.aliyun.oss.internal.OSSUtils;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.DeleteObjectsResult;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.google.common.base.Stopwatch;

@Component
public class OssCloudStorageLogic implements CloudStorageLogic {
	
	@Autowired private OSS ossClient;
	
	@Value("${alicloud.access-key}") private String accessKey;
	@Value("${alicloud.oss.bucket}") private String bucket;
	@Value("${alicloud.oss.folder.directupload}") private String directUploadFolder;
	
	private static final Logger LOG = LoggerFactory.getLogger(OssCloudStorageLogic.class);
	
	private void uploadToOss(String fileName, byte[] bytearr) {
		LOG.info("Storing {} to OSS", fileName);
		Stopwatch sw = Stopwatch.createStarted();
		ossClient.putObject(bucket, fileName, new ByteArrayInputStream(bytearr));
		sw.stop();
		LOG.info("Uploaded {} bytes of data. Duration: {} ms", bytearr.length, sw.elapsed(TimeUnit.MILLISECONDS));
	}
	
	private byte[] downloadFromOss(String fileName) {
		if (!OSSUtils.validateObjectKey(fileName)) {
			return ArrayUtils.EMPTY_BYTE_ARRAY;
		}
		
		if (!ossClient.doesObjectExist(bucket, fileName)) {
			LOG.info("{} does not exist. Returning empty array.");
			return ArrayUtils.EMPTY_BYTE_ARRAY;
		}
		
		LOG.info("Getting {} from OSS", fileName);
		Stopwatch sw = Stopwatch.createStarted();
		try {
			OSSObject object = ossClient.getObject(bucket, fileName);		
			sw.stop();
			InputStream is = object.getObjectContent();
			
			byte[] bytearr = IOUtils.readStreamAsByteArray(is);
			LOG.info("Downloaded {} bytes of data. Duration: {} ms", bytearr.length, sw.elapsed(TimeUnit.MILLISECONDS));
			return bytearr;
		}
		catch (IOException e) {
			throw new CommonException("Download from OSS failed for key: " + fileName, ReasonCommon.UNKNOWN);
		}
		catch (OSSException e) {
			LOG.warn("Failed to download {} from OSS with error code: {}, message: {}", fileName, e.getErrorCode(), e.getErrorMessage(), e);
			return ArrayUtils.EMPTY_BYTE_ARRAY;
		}
	}
	
	private void deleteFromOss(String fileName) {
		Stopwatch sw = Stopwatch.createStarted();
		String nextMarker = null;
		ObjectListing objectListing = null;
		LOG.info("Deleting {} from OSS", fileName);
		do {
			ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucket)
	                .withPrefix(fileName)
	                .withMarker(nextMarker);		
			objectListing = ossClient.listObjects(listObjectsRequest);
			
			if(!objectListing.getObjectSummaries().isEmpty()) {
				List<String> keys = new ArrayList<>();
                for (OSSObjectSummary s : objectListing.getObjectSummaries()) {
                    LOG.info("Key name: {}", s.getKey());
                    keys.add(s.getKey());
                }
				DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(bucket).withKeys(keys);
				DeleteObjectsResult deleteObjectsResult = ossClient.deleteObjects(deleteObjectsRequest);
	            List<String> deletedObjects = deleteObjectsResult.getDeletedObjects();
	            for(String obj : deletedObjects) {
	               LOG.info("Deleted object: {}", obj);
	            }
			}
			nextMarker = objectListing.getNextMarker();
		} while (objectListing.isTruncated());
		sw.stop();
		LOG.info("Object deleted from OSS. Duration: {} ms", sw.elapsed(TimeUnit.MILLISECONDS));
	}
	
	@Override
	public String storeTemporaryDashboardFile(String filename, byte[] fileByteArray) {
		String pathName = directUploadFolder + filename;
		uploadToOss(pathName, fileByteArray);
		return pathName;
	}

	@Override
	public byte[] getTemporaryDashboardFile(String filename) {
		String pathName = directUploadFolder + filename;
		return downloadFromOss(pathName);
	}

	@Override
	public void deleteTemporaryDashboardFile(String filename) {
		String pathName = directUploadFolder + filename;
		deleteFromOss(pathName);
	}

	@Override
	public String storeDashboardFile(MsTenant tenant, String filename, byte[] fileByteArray) {
		String pathName = String.format(GlobalVal.OSS_DASHBOARD_DOC_FORMAT, tenant.getTenantName(), filename);
		uploadToOss(pathName, fileByteArray);
		return pathName;
	}

	@Override
	public byte[] getDashboardFile(MsTenant tenant, String filename) {
		String pathName = String.format(GlobalVal.OSS_DASHBOARD_DOC_FORMAT, tenant.getTenantName(), filename);
		return downloadFromOss(pathName);
	}

	@Override
	public String storeConsolidatePdf(MsTenant tenant, String filename, byte[] fileByteArray) {
		String pathName = String.format(GlobalVal.OSS_INSIGHTS_PDF_FORMAT, tenant.getTenantCode(), filename);
		uploadToOss(pathName, fileByteArray);
		return pathName;
	}

	@Override
	public byte[] getConsolidatePdf(MsTenant tenant, String filename) {
		String pathName = String.format(GlobalVal.OSS_INSIGHTS_PDF_FORMAT, tenant.getTenantCode(), filename);
		return downloadFromOss(pathName);
	}

	@Override
	public void deleteConsolidatePdf(MsTenant tenant, String filename) {
		String pathName = String.format(GlobalVal.OSS_INSIGHTS_PDF_FORMAT, tenant.getTenantCode(), filename);
		deleteFromOss(pathName);
	}

	@Override
	public String storeConsolidateXlsx(MsTenant tenant, String filename, byte[] fileByteArray) {
		String pathName = String.format(GlobalVal.OSS_INSIGHTS_XLSX_FORMAT, tenant.getTenantCode(), filename);
		uploadToOss(pathName, fileByteArray);
		return pathName;
	}

	@Override
	public byte[] getConsolidateXlsx(MsTenant tenant, String filename) {
		String pathName = String.format(GlobalVal.OSS_INSIGHTS_XLSX_FORMAT, tenant.getTenantCode(), filename);
		return downloadFromOss(pathName);
	}

	@Override
	public void deleteConsolidateXlsx(MsTenant tenant, String filename) {
		String pathName = String.format(GlobalVal.OSS_INSIGHTS_XLSX_FORMAT, tenant.getTenantCode(), filename);
		deleteFromOss(pathName);
	}

}
