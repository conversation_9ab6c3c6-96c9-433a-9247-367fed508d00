package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.dashboard.AddDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardAccountNoListResponse;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardBankListResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardConsolidateStatusResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardPeriodListRequest;
import com.adins.bsa.webservices.model.dashboard.DashboardPeriodListResponse;
import com.adins.bsa.webservices.model.dashboard.DashboardWarningStatusResponse;
import com.adins.bsa.webservices.model.dashboard.DeleteDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.ListDashboardRequest;
import com.adins.bsa.webservices.model.dashboard.ListDashboardResponse;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListTransactionnonBusinessTransactionGroupRequest;
import com.adins.bsa.webservices.model.nonbusinesstransactiongroup.ListTransactionnonBusinessTransactionGroupResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface DashboardService {
	ListDashboardResponse getListDashboard(ListDashboardRequest request);
	MssResponseType addDashboard(AddDashboardRequest request);
	MssResponseType addNewBankStatement(AddDashboardRequest request);
	MssResponseType deleteDashboard(DeleteDashboardRequest request);
	DashboardBankListResponse getDashboardBankList(GenericDashboardDropdownListRequest request);
	DashboardAccountNoListResponse getDashboardAccountNoList(GenericDashboardDropdownListRequest request);
	DashboardPeriodListResponse getDashboardPeriodList(DashboardPeriodListRequest request);
	ListTransactionnonBusinessTransactionGroupResponse getListTransaction(ListTransactionnonBusinessTransactionGroupRequest request);
	MssResponseType startConsolidate(GenericDashboardDropdownListRequest request);
	DashboardConsolidateStatusResponse getConsolidateStatus(GenericDashboardDropdownListRequest request);
	DashboardWarningStatusResponse getDashboardWarningStatus(GenericDashboardDropdownListRequest request);
}
