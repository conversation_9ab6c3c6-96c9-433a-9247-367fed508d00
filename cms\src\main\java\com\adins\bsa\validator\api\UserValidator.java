package com.adins.bsa.validator.api;

import com.adins.am.model.AmMsuser;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface UserValidator {
	AmMsuser validateGetUserByEmail(String email, boolean userMustExists, AuditContext audit);
	AmMsuser validateGetUserByEmailNewTrx(String email, boolean userMustExists, AuditContext audit);
	void validateUserDashboardAccess(AmMsuser requestingUser, TrDashboardGroupH dashboardGroupH, AuditContext audit);
	boolean canUserAccessDashboard(AmMsuser requestingUser, TrDashboardGroupH dashboardGroupH, AuditContext audit);
}
