package com.adins.bsa.custom;

import java.io.Serializable;

public class DashboardFileBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private String actualFilename;
	private String ossFilename;
	private String password;
	
	public String getActualFilename() {
		return actualFilename;
	}
	public void setActualFilename(String actualFilename) {
		this.actualFilename = actualFilename;
	}
	public String getOssFilename() {
		return ossFilename;
	}
	public void setOssFilename(String ossFilename) {
		this.ossFilename = ossFilename;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	
}
