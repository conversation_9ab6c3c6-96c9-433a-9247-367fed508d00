package com.adins.bsa.model;

import java.util.HashSet;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;

/**
 * MsTenant generated by hbm2java
 */
@Entity
@Table(name = "ms_tenant")
public class MsTenant extends ActiveAndUpdateableEntity implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	public static final String ID_TENANT_HBM = "idMsTenant";
	public static final String TENANT_CODE_HBM = "tenantCode";
	public static final String TENANT_CODE_SMALL_HBM = "tenantcode";

	private long idMsTenant;
	private String tenantCode;
	private String tenantName;
	private String apiKey;
	
	private Set<MsUseroftenant> msUseroftenants = new HashSet<>(0);
	private Set<AmGeneralsetting> amGeneralsettings = new HashSet<>(0);
	private Set<AmMsrole> amMsRoles = new HashSet<>(0);
	private Set<MsTenantSettings> msTenantSettings = new HashSet<>(0);
	private Set<TrDashboardGroupH> trDashboardGroupHs = new HashSet<>(0); 

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_tenant", unique = true, nullable = false)
	public long getIdMsTenant() {
		return this.idMsTenant;
	}
	
	public void setIdMsTenant(long idMsTenant) {
		this.idMsTenant = idMsTenant;
	}

	@Column(name = "tenant_code", nullable = false, length = 20)
	public String getTenantCode() {
		return this.tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	@Column(name = "tenant_name", nullable = false, length = 100)
	public String getTenantName() {
		return this.tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	@Column(name = "api_key", length = 80)
	public String getApiKey() {
		return this.apiKey;
	}

	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsUseroftenant> getMsUseroftenants() {
		return this.msUseroftenants;
	}

	public void setMsUseroftenants(Set<MsUseroftenant> msUseroftenants) {
		this.msUseroftenants = msUseroftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<AmGeneralsetting> getAmGeneralsettings() {
		return this.amGeneralsettings;
	}

	public void setAmGeneralsettings(Set<AmGeneralsetting> amGeneralsettings) {
		this.amGeneralsettings = amGeneralsettings;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<AmMsrole> getAmMsRoles() {
		return amMsRoles;
	}

	public void setAmMsRoles(Set<AmMsrole> amMsRoles) {
		this.amMsRoles = amMsRoles;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<MsTenantSettings> getMsTenantSettings() {
		return msTenantSettings;
	}

	public void setMsTenantSettings(Set<MsTenantSettings> msTenantSettings) {
		this.msTenantSettings = msTenantSettings;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msTenant")
	public Set<TrDashboardGroupH> getTrDashboardGroupHs() {
		return trDashboardGroupHs;
	}

	public void setTrDashboardGroupHs(Set<TrDashboardGroupH> trDashboardGroupHs) {
		this.trDashboardGroupHs = trDashboardGroupHs;
	}
	
}
