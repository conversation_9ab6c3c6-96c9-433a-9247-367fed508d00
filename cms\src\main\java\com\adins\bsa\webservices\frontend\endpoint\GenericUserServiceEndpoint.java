package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.UserLogic;
import com.adins.bsa.webservices.frontend.api.UserService;
import com.adins.bsa.webservices.model.user.CheckUserForgotPasswordRequest;
import com.adins.bsa.webservices.model.user.ResetPasswordRequest;
import com.adins.bsa.webservices.model.user.UserMenuRequest;
import com.adins.bsa.webservices.model.user.UserMenuResponse;
import com.adins.bsa.webservices.model.user.UserProfileRequest;
import com.adins.bsa.webservices.model.user.UserProfileResponse;
import com.adins.bsa.webservices.model.user.VerifyOtpRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/user")
@Api(value = "UserService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericUserServiceEndpoint implements UserService {
	
	@Autowired private UserLogic userLogic;

	@Override
	@POST
	@Path("/s/profiles")
	public UserProfileResponse getUserProfile(UserProfileRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getUserProfile(request, audit);
	}

	@Override
	@POST
	@Path("/s/menu")
	public UserMenuResponse getUserMenu(UserMenuRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.getUserMenu(request, audit);
	}

	@Override
	@POST
	@Path("/checkUserForgotPassword")
	public MssResponseType checkUserForgotPassword(CheckUserForgotPasswordRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.checkUserForgotPassword(request, audit);
	}

	@Override
	@POST
	@Path("/sendOtpForgotPassword")
	public MssResponseType sendOtpForgotPassword(CheckUserForgotPasswordRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.sendOtpForgotPassword(request, audit);
	}

	@Override
	@POST
	@Path("/verifyOtpForgotPassword")
	public MssResponseType verifyOtpForgotPassword(VerifyOtpRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.verifyOtpForgotPassword(request, audit);
	}

	@Override
	@POST
	@Path("/resetPassword")
	public MssResponseType resetPassword(ResetPasswordRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userLogic.resetPassword(request, audit);
	}

}
