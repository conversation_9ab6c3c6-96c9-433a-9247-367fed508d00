package com.adins.bsa.custom.validation;
import java.util.Objects;

public class DashboardNameValidationBean {
    
    private String dashboardName;
    private String tenantCode;

    public String getDashboardName() {
        return this.dashboardName;
    }

    public void setDashboardName(String dashboardName) {
        this.dashboardName = dashboardName;
    }

    public String getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) {
            return true;
        }

        if (null == object) {
            return false;
        }

        if (!(object instanceof DashboardNameValidationBean)) {
            return false;
        }

        DashboardNameValidationBean bean = (DashboardNameValidationBean) object;
        
        if (!Objects.equals(dashboardName, bean.getDashboardName())) {
            return false;
        }

        return Objects.equals(tenantCode, bean.getTenantCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.dashboardName, this.tenantCode);
    }


    @Override
    public String toString() {
        return "{" +
            " dashboardName='" + getDashboardName() + "'" +
            ", tenantCode='" + getTenantCode() + "'" +
            "}";
    }

}
