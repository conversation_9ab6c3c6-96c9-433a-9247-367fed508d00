package com.adins.bsa.custom.email;

import java.io.Serializable;

public class EmailAttachmentBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private byte[] binary;
	private String fileName;
	
	public EmailAttachmentBean(byte[] binary, String fileName) {
		this.binary = binary;
		this.fileName = fileName;
	}
	
	public byte[] getBinary() {
		return binary;
	}
	public void setBinary(byte[] binary) {
		this.binary = binary;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	
}
