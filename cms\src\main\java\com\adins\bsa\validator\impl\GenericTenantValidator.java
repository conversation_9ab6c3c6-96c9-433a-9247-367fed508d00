package com.adins.bsa.validator.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.validator.api.TenantValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericTenantValidator extends BaseLogic implements TenantValidator {

	@Override
	public MsTenant validateGetTenant(String tenantCode, boolean tenantMustExists, AuditContext audit) {
		
		if (StringUtils.isBlank(tenantCode)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"tenantCode"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(tenantCode);
		if (tenantMustExists && null == tenant) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_OBJ_NOT_FOUND, new String[] {"Tenant code", tenantCode}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return tenant;
	}

	@Override
	public MsTenant validateGetTenantByXApiKey(String xApiKey, AuditContext audit) {
		
		String[] apiKeyStripped = StringUtils.splitPreserveAllTokens(xApiKey, '@');
		String key = apiKeyStripped[0];
		String tenantCode = StringUtils.upperCase(apiKeyStripped[1]);
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByApiKeyAndTenantCode(key, tenantCode);
		if (null == tenant) {
			throw new CommonException(getMessage("businesslogic.tenant.incorrectapikey", null, audit), ReasonCommon.INVALID_CONDITION);
		}
		
		return tenant;
	}

}
