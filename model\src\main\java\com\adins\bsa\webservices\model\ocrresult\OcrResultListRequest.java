package com.adins.bsa.webservices.model.ocrresult;

import com.adins.bsa.webservices.model.dashboard.GenericDashboardPagingRequest;

public class OcrResultListRequest extends GenericDashboardPagingRequest {
	
	private static final long serialVersionUID = 1L;
	private String bankName;
	private String lovProcessResultCode;
	
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getLovProcessResultCode() {
		return lovProcessResultCode;
	}
	public void setLovProcessResultCode(String lovProcessResultCode) {
		this.lovProcessResultCode = lovProcessResultCode;
	}
	
}
