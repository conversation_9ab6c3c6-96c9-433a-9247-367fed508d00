package com.adins.bsa.webservices.model.insights;

import java.util.List;

import com.adins.bsa.custom.InsightsChartBean;
import com.adins.framework.service.base.model.MssResponseType;

public class SupplierBuyerGpmWcrLrResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<String> labels;
	private List<InsightsChartBean> grossProfitMargin;
	private List<InsightsChartBean> workingCapitalRatio;
	private List<InsightsChartBean> liquidityRatio;
	
	public List<String> getLabels() {
		return labels;
	}
	public void setLabels(List<String> labels) {
		this.labels = labels;
	}
	public List<InsightsChartBean> getGrossProfitMargin() {
		return grossProfitMargin;
	}
	public void setGrossProfitMargin(List<InsightsChartBean> grossProfitMargin) {
		this.grossProfitMargin = grossProfitMargin;
	}
	public List<InsightsChartBean> getWorkingCapitalRatio() {
		return workingCapitalRatio;
	}
	public void setWorkingCapitalRatio(List<InsightsChartBean> workingCapitalRatio) {
		this.workingCapitalRatio = workingCapitalRatio;
	}
	public List<InsightsChartBean> getLiquidityRatio() {
		return liquidityRatio;
	}
	public void setLiquidityRatio(List<InsightsChartBean> liquidityRatio) {
		this.liquidityRatio = liquidityRatio;
	}
	
}
