package com.adins.bsa.model;

import java.util.HashSet;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.adins.am.model.AmUserpwdhistory;
import com.adins.am.model.custom.ActiveDeleteAndUpdateableEntity;

/**
 * MsLov generated by hbm2java
 */
@Entity
@Table(name = "ms_lov", uniqueConstraints = @UniqueConstraint(columnNames = "code"))
public class MsLov extends ActiveDeleteAndUpdateableEntity implements java.io.Serializable {
	public static final String ID_LOV_HBM = "idLov";
	public static final String LOV_GROUP_HBM = "lovGroup";
	public static final String CODE_HBM = "code";
	public static final String DESCRIPTION_HBM = "description";
	public static final String SEQUENCE_HBM = "sequence";
	public static final String CONSTRAINT_1_HBM = "constraint1";
	public static final String CONSTRAINT_2_HBM = "constraint2";
	public static final String CONSTRAINT_3_HBM = "constraint3";
	public static final String CONSTRAINT_4_HBM = "constraint4";
	public static final String CONSTRAINT_5_HBM = "constraint5";

	private static final long serialVersionUID = 1L;

	private long idLov;
	private String lovGroup;
	private String code;
	private String description;
	private Integer sequence;
	private String constraint1;
	private String constraint2;
	private String constraint3;
	private String constraint4;
	private String constraint5;
	
	private Set<AmUserpwdhistory> amUserpwdhistories = new HashSet<>(0);
	private Set<MsTenantSettings> msTenantSettings = new HashSet<>(0);

	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_lov", unique = true, nullable = false)
	public long getIdLov() {
		return this.idLov;
	}

	public void setIdLov(long idLov) {
		this.idLov = idLov;
	}

	@Column(name = "lov_group", length = 80)
	public String getLovGroup() {
		return this.lovGroup;
	}

	public void setLovGroup(String lovGroup) {
		this.lovGroup = lovGroup;
	}

	@Column(name = "code", length = 80)
	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	@Column(name = "description", length = 200)
	public String getDescription() {
		return this.description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Column(name = "sequence")
	public Integer getSequence() {
		return this.sequence;
	}

	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}

	@Column(name = "constraint_1", length = 80)
	public String getConstraint1() {
		return this.constraint1;
	}

	public void setConstraint1(String constraint1) {
		this.constraint1 = constraint1;
	}

	@Column(name = "constraint_2", length = 80)
	public String getConstraint2() {
		return this.constraint2;
	}

	public void setConstraint2(String constraint2) {
		this.constraint2 = constraint2;
	}

	@Column(name = "constraint_3", length = 80)
	public String getConstraint3() {
		return this.constraint3;
	}

	public void setConstraint3(String constraint3) {
		this.constraint3 = constraint3;
	}

	@Column(name = "constraint_4", length = 80)
	public String getConstraint4() {
		return this.constraint4;
	}

	public void setConstraint4(String constraint4) {
		this.constraint4 = constraint4;
	}

	@Column(name = "constraint_5", length = 80)
	public String getConstraint5() {
		return this.constraint5;
	}

	public void setConstraint5(String constraint5) {
		this.constraint5 = constraint5;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "msLov")
	public Set<AmUserpwdhistory> getAmUserpwdhistories() {
		return this.amUserpwdhistories;
	}

	public void setAmUserpwdhistories(Set<AmUserpwdhistory> amUserpwdhistories) {
		this.amUserpwdhistories = amUserpwdhistories;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "lovSettingType")
	public Set<MsTenantSettings> getMsTenantSettings() {
		return msTenantSettings;
	}

	public void setMsTenantSettings(Set<MsTenantSettings> msTenantSettings) {
		this.msTenantSettings = msTenantSettings;
	}

}
