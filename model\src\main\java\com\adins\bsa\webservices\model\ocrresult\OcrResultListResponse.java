package com.adins.bsa.webservices.model.ocrresult;

import java.util.List;

import com.adins.bsa.custom.OcrResultBean;
import com.adins.framework.service.base.model.MssResponseType;

public class OcrResultListResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<OcrResultBean> results;
	private int page;
	private long totalPage;
	private long totalResult;
	
	public List<OcrResultBean> getResults() {
		return results;
	}
	public void setResults(List<OcrResultBean> results) {
		this.results = results;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public long getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}
	public long getTotalResult() {
		return totalResult;
	}
	public void setTotalResult(long totalResult) {
		this.totalResult = totalResult;
	}
	
}
