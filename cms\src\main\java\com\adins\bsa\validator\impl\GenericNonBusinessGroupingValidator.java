package com.adins.bsa.validator.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.model.TrConsolidateResultNonbusinessGroupingD;
import com.adins.bsa.model.TrConsolidateResultNonbusinessGroupingH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.validator.api.NonBusinessGroupingValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericNonBusinessGroupingValidator extends BaseLogic implements NonBusinessGroupingValidator {

	@Override
	public TrConsolidateResultNonbusinessGroupingH validateGetGroupingHeader(TrDashboardGroupH dashboardGroupH, String groupName, boolean objectMustExist, AuditContext audit) {
		
		if (StringUtils.isBlank(groupName)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Group name"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrConsolidateResultNonbusinessGroupingH groupingH = daoFactory.getNonBusinessTransactionGroupDao().getGroupingH(groupName, dashboardGroupH, "0");
		if (null == groupingH && objectMustExist) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_OBJ_NOT_FOUND, new String[] {"Group name", groupName}, audit) , ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return groupingH;
	}

	@Override
	public TrConsolidateResultNonbusinessGroupingD validateGetGroupingDetail(TrConsolidateResultNonbusinessGroupingH groupingH, String resultDetailId, boolean objectMustExist, AuditContext audit) {
		
		if (StringUtils.isBlank(resultDetailId)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Result detail ID"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		TrConsolidateResultNonbusinessGroupingD groupingD = daoFactory.getNonBusinessTransactionGroupDao().getGroupingD(groupingH, resultDetailId, "0");
		if (null == groupingD && objectMustExist) {
			throw new CommonException(getMessage("businesslogic.global.objectnotfound1", new String[] {"Group detail"}, audit) , ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return groupingD;
	}

}
