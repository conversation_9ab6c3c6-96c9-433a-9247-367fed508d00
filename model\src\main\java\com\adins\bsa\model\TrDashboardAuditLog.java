package com.adins.bsa.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.CreateableEntity;

@Entity
@Table(name = "tr_dashboard_audit_log")
public class TrDashboardAuditLog extends CreateableEntity {

    private long idDashboardAuditLog;
    private Date auditLogDate;
    private TrDashboardGroupH trDashboardGroupH;
    private TrDashboardGroupD trDashboardGroupD;
    private AmMsuser amMsuser;
    private MsLov lovModule;
    private MsLov lovAction;
    private String field;
    private String previousValue;
    private String newValue;
    private String notes;

    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_dashboard_audit_log", unique = true, nullable = false)
    public long getIdDashboardAuditLog() {
        return this.idDashboardAuditLog;
    }

    public void setIdDashboardAuditLog(long idDashboardAuditLog) {
        this.idDashboardAuditLog = idDashboardAuditLog;
    }

    @Column(name = "audit_log_date", nullable = false)
    public Date getAuditLogDate() {
        return this.auditLogDate;
    }

    public void setAuditLogDate(Date auditLogDate) {
        this.auditLogDate = auditLogDate;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_h", nullable = false)
    public TrDashboardGroupH getTrDashboardGroupH() {
        return this.trDashboardGroupH;
    }

    public void setTrDashboardGroupH(TrDashboardGroupH trDashboardGroupH) {
        this.trDashboardGroupH = trDashboardGroupH;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_d")
    public TrDashboardGroupD getTrDashboardGroupD() {
        return this.trDashboardGroupD;
    }

    public void setTrDashboardGroupD(TrDashboardGroupD trDashboardGroupD) {
        this.trDashboardGroupD = trDashboardGroupD;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user")
    public AmMsuser getAmMsuser() {
        return this.amMsuser;
    }

    public void setAmMsuser(AmMsuser amMsuser) {
        this.amMsuser = amMsuser;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_module")
    public MsLov getLovModule() {
        return this.lovModule;
    }

    public void setLovModule(MsLov lovModule) {
        this.lovModule = lovModule;
    }

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_action")
    public MsLov getLovAction() {
        return this.lovAction;
    }

    public void setLovAction(MsLov lovAction) {
        this.lovAction = lovAction;
    }

    @Column(name = "field", nullable = false, length = 64)
    public String getField() {
        return this.field;
    }

    public void setField(String field) {
        this.field = field;
    }

    @Column(name = "previous_value", length = 512)
    public String getPreviousValue() {
        return this.previousValue;
    }

    public void setPreviousValue(String previousValue) {
        this.previousValue = previousValue;
    }

    @Column(name = "new_value", length = 512)
    public String getNewValue() {
        return this.newValue;
    }

    public void setNewValue(String newValue) {
        this.newValue = newValue;
    }

    @Column(name = "notes", length = 128)
    public String getNotes() {
        return this.notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

}
