package com.adins.bsa.webservices.model.nonbusinesstransactiongroup;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class DeleteNonBusinessTransactionGroupDetailRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("Result detail ID")
	@Required(allowBlankString = false)
	private String detailId;
	
	@ValidationObjectName("Group name")
	@Required(allowBlankString = false)
	private String groupName;
	
	public String getDetailId() {
		return detailId;
	}
	public void setDetailId(String detailId) {
		this.detailId = detailId;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	
}
