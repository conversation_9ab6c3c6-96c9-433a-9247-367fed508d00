package com.adins.bsa.webservices.model.anomaly;

import java.util.List;

import com.adins.bsa.custom.AddAnomalyRequestBean;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class AddAnomaliesRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	private List<AddAnomalyRequestBean> anomalies;

	public List<AddAnomalyRequestBean> getAnomalies() {
		return anomalies;
	}

	public void setAnomalies(List<AddAnomalyRequestBean> anomalies) {
		this.anomalies = anomalies;
	}
	
}
