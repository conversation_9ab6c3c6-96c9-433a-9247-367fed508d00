package com.adins.bsa;

import java.util.Properties;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

@Configuration
public class MailConfiguration {
	
	@Qualifier("primaryMailProperties")
	@Bean
	@ConfigurationProperties(prefix = "spring.mail.properties.mail.smtp")
	public SmtpProperties primarySmtpProperties() {
		return new SmtpProperties();
	}
	
	@Bean(name = "primaryMailSender")
	@ConfigurationProperties(prefix = "spring.mail")
	public JavaMailSender primarySender(@Qualifier("primaryMailProperties") SmtpProperties primarySmtpProperties) {
		JavaMailSender jMailSender = new JavaMailSenderImpl();
		
		Properties props = new Properties();
		props.put("mail.transport.protocol", "smtp");
		props.put("mail.smtp.ssl.enable", primarySmtpProperties.isSslEnable());
		props.put("mail.smtp.starttls.enable", primarySmtpProperties.isStarttlsEnable());
		props.put("mail.smtp.auth", primarySmtpProperties.isAuth());
		props.put("mail.smtp.connectiontimeout", primarySmtpProperties.getConnectiontimeout());
		props.put("mail.smtp.timeout", primarySmtpProperties.getTimeout());
		props.put("mail.smtp.writetimeout", primarySmtpProperties.getWritetimeout());
		props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
		
		((JavaMailSenderImpl) jMailSender).setJavaMailProperties(props);
        
        return jMailSender;
    }
	
}
