package com.adins.bsa;

import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
public class JasyptConfig {
	
	@Bean(name = "jasyptStringEncryptor")
	public StringEncryptor encryptor(Environment environment) {
		SimpleStringPBEConfig config = new SimpleStringPBEConfig();
		config.setPassword(environment.getProperty("jasypt.encryptor.password"));
		config.setAlgorithm(environment.getProperty("jasypt.encryptor.algorithm"));
		config.setKeyObtentionIterations(environment.getProperty("jasypt.encryptor.key-obtention-iterations"));
		config.setPoolSize(environment.getProperty("jasypt.encryptor.pool-size"));
		config.setProviderName(null);
		config.setProviderClassName(null);
		config.setSaltGeneratorClassName(environment.getProperty("jasypt.encryptor.salt-generator-classname"));
		config.setIvGeneratorClassName(environment.getProperty("jasypt.encryptor.iv-generator-classname"));
		config.setStringOutputType(environment.getProperty("jasypt.encryptor.string-output-type"));
		
		PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
		encryptor.setConfig(config);
		return encryptor;
	}
}
