package com.adins.bsa.validator.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.bsa.constants.GlobalKey;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.validator.api.LovValidator;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
@Component
public class GenericLovValidator extends BaseLogic implements LovValidator {
	
	@Override
	public MsLov getLovByGroupAndCode(String lovGroup, String code, AuditContext audit) {
		
		if (StringUtils.isBlank(lovGroup)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"lovGroup"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (StringUtils.isBlank(code)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"Code"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		MsLov lov = daoFactory.getLovDao().getLovByGroupAndCode(lovGroup, code);
		if (null == lov) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_OBJ_NOT_FOUND, new String[] {lovGroup, code}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return lov;
	}

	@Override
	public MsLov getLovByGroupAndId(String lovGroup, long idLov, AuditContext audit) {
		if (StringUtils.isBlank(lovGroup)) {
			throw new CommonException(getMessage(GlobalKey.MSG_COMMON_MANDATORY, new String[] {"lovGroup"}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		MsLov lov = daoFactory.getLovDao().getLovByGroupAndId(lovGroup, idLov);
		if (null == lov) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_OBJECTNOTFOUND_1, new String[] {lovGroup}, audit), ReasonCommon.OBJECT_NOT_FOUND);
		}
		
		return lov;
	}
	
}
