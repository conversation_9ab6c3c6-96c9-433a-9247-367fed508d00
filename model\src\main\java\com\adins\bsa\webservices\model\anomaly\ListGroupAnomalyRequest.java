package com.adins.bsa.webservices.model.anomaly;

import com.adins.framework.service.base.model.MssRequestType;

public class ListGroupAnomalyRequest extends MssRequestType{
	private static final long serialVersionUID = 1L;

    private int page;
	private String reason;
	private String tenantCode;
	private String dashboardName;
	private String risk;

    public int getPage() {
        return page;
    }
    public void setPage(int page) {
        this.page = page;
    }
    public String getReason() {
        return reason;
    }
    public void setReason(String reason) {
        this.reason = reason;
    }
    public String getTenantCode() {
        return tenantCode;
    }
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
    public String getDashboardName() {
        return dashboardName;
    }
    public void setDashboardName(String dashboardName) {
        this.dashboardName = dashboardName;
    }
    public String getRisk() {
        return risk;
    }
    public void setRisk(String risk) {
        this.risk = risk;
    }
    
}
