package com.adins.bsa.dataaccess.api;

import java.util.List;
import java.util.Map;

import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserpwdhistory;
import com.adins.bsa.model.MsSupervisorOfUser;

public interface UserDao {
	
	void insertUser(AmMsuser user);
	void updateUser(AmMsuser user);
	
	AmMsuser getUserByIdMsUser(long idMsUser);
	AmMsuser getUserByEmail(String email);
	AmMsuser getUserByEmailNewTrx(String email);
	AmMsuser getUserByPhone(String phone);
	MsSupervisorOfUser getUserByIdMsUserSupervisorAndStaff(long idMsUserSupervisor, long idMsUserStaff);
	
	void insertUserPwdHistory(AmUserpwdhistory history);
	
	// For load test setup
	List<Map<String, Object>> getEmailsForLoadTest(int totalData);
}
