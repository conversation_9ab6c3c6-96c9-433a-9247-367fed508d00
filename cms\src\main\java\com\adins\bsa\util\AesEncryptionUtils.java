package com.adins.bsa.util;

import java.nio.charset.StandardCharsets;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import com.adins.framework.tool.encryption.EncryptionException;

public class AesEncryptionUtils {
	
	private AesEncryptionUtils() {}
	public static final String AES = "AES";
	public static final String AES_ECB_PKCS5 = "AES/ECB/PKCS5Padding";

	public static String encrypt(String plainText, String key) {
		byte[] crypted = null;
		try {		
			SecretKeySpec skey = new SecretKeySpec(key.getBytes(), AES);
			
			Cipher cipher = Cipher.getInstance(AES_ECB_PKCS5);
			cipher.init(Cipher.ENCRYPT_MODE, skey);
			crypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
		}
		catch (Exception e) {
			throw new EncryptionException(e.getMessage(), e);
		}
		java.util.Base64.Encoder encoder = java.util.Base64.getEncoder();
		
		return encoder.encodeToString(crypted);
	}

	public static String decrypt(String input, String key) {
		byte[] output = null;
		try {
			java.util.Base64.Decoder decoder = java.util.Base64.getDecoder();
			SecretKeySpec skey = new SecretKeySpec(key.getBytes(), AES);
			Cipher cipher = Cipher.getInstance(AES_ECB_PKCS5);
			cipher.init(Cipher.DECRYPT_MODE, skey);
			output = cipher.doFinal(decoder.decode(input));
		}
		catch (Exception e) {
			throw new EncryptionException(e.getMessage(), e);
		}
		return new String(output, StandardCharsets.UTF_8);
	}
}
