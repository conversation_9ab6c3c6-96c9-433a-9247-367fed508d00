package com.adins.bsa.webservices.model.dashboard;

import java.util.List;

import com.adins.bsa.custom.SupplierBuyerSubGroupOfMainGroupBean;
import com.adins.framework.service.base.model.MssResponseType;

public class ListSubGroupSupplierBuyerGroupResponse extends MssResponseType {

	private static final long serialVersionUID = 1L;
	
	private List<SupplierBuyerSubGroupOfMainGroupBean> listSubGroupName;

	public List<SupplierBuyerSubGroupOfMainGroupBean> getListSubGroupName() {
		return listSubGroupName;
	}

	public void setListSubGroupName(List<SupplierBuyerSubGroupOfMainGroupBean> listSubGroupName) {
		this.listSubGroupName = listSubGroupName;
	}
	
}
