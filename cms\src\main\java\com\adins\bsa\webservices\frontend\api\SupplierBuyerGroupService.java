package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.dashboard.ListSubGroupSupplierBuyerGroupRequest;
import com.adins.bsa.webservices.model.dashboard.ListSubGroupSupplierBuyerGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerSubGroupMembersRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.AddSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.DeleteSupplierBuyerMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.DeleteSupplierBuyerSubGroupMemberRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.EditSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListMasterSupplierBuyerSubGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListMasterSupplierBuyerSubGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerGroupResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupMemberRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupMemberResponse;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupOfMainGroupRequest;
import com.adins.bsa.webservices.model.supplierbuyergroup.ListSupplierBuyerSubGroupOfMainGroupResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface SupplierBuyerGroupService {
	ListSupplierBuyerGroupResponse getList(ListSupplierBuyerGroupRequest request);
	ListSubGroupSupplierBuyerGroupResponse getListSubGroup(ListSubGroupSupplierBuyerGroupRequest request);
	MssResponseType addMainGroup(AddSupplierBuyerMainGroupRequest request);
	MssResponseType deleteMainGroup(DeleteSupplierBuyerMainGroupRequest request);
	MssResponseType addSubGroup(AddSupplierBuyerSubGroupRequest request);
	ListSupplierBuyerSubGroupOfMainGroupResponse getListSubGroupOfMainGroup(ListSupplierBuyerSubGroupOfMainGroupRequest request);
	ListMasterSupplierBuyerSubGroupResponse getListMasterSubGroup(ListMasterSupplierBuyerSubGroupRequest request);
	MssResponseType deleteSupplierBuyerSubGroup(AddSupplierBuyerSubGroupRequest request);
	ListSupplierBuyerSubGroupMemberResponse getListSubGroupMember(ListSupplierBuyerSubGroupMemberRequest request);
	MssResponseType deleteSubGroupMember(DeleteSupplierBuyerSubGroupMemberRequest request);
	MssResponseType addSupplierBuyerSubGroupMembers(AddSupplierBuyerSubGroupMembersRequest request);
	MssResponseType editSupplierBuyerSubGroup(EditSupplierBuyerSubGroupRequest request);
}
