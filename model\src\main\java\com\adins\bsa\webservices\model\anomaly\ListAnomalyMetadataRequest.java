package com.adins.bsa.webservices.model.anomaly;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.framework.service.base.model.MssRequestType;

public class ListAnomalyMetadataRequest extends MssRequestType {
	private static final long serialVersionUID = 1L;
    
	@ValidationObjectName("reason")
	@Required(allowBlankString = false)
	private String reason;

    private int page;
	private String tenantCode;
	private String dashboardName;
    public String getReason() {
        return reason;
    }
    public void setReason(String reason) {
        this.reason = reason;
    }
    public int getPage() {
        return page;
    }
    public void setPage(int page) {
        this.page = page;
    }
    public String getTenantCode() {
        return tenantCode;
    }
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
    public String getDashboardName() {
        return dashboardName;
    }
    public void setDashboardName(String dashboardName) {
        this.dashboardName = dashboardName;
    }

}
