package com.adins.bsa.webservices.frontend.api;

import com.adins.bsa.webservices.model.user.CheckUserForgotPasswordRequest;
import com.adins.bsa.webservices.model.user.ResetPasswordRequest;
import com.adins.bsa.webservices.model.user.UserMenuRequest;
import com.adins.bsa.webservices.model.user.UserMenuResponse;
import com.adins.bsa.webservices.model.user.UserProfileRequest;
import com.adins.bsa.webservices.model.user.UserProfileResponse;
import com.adins.bsa.webservices.model.user.VerifyOtpRequest;
import com.adins.framework.service.base.model.MssResponseType;

public interface UserService {
	UserProfileResponse getUserProfile(UserProfileRequest request);
	UserMenuResponse getUserMenu(UserMenuRequest request);
	MssResponseType checkUserForgotPassword(CheckUserForgotPasswordRequest request);
	MssResponseType sendOtpForgotPassword(CheckUserForgotPasswordRequest request);
	MssResponseType verifyOtpForgotPassword(VerifyOtpRequest request);
	MssResponseType resetPassword(ResetPasswordRequest request);
}
