package com.adins.bsa.webservices.model.circular;

import java.util.List;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.bsa.webservices.model.dashboard.GenericDashboardDropdownListRequest;

public class AddCircularGroupRequest extends GenericDashboardDropdownListRequest {
	
	private static final long serialVersionUID = 1L;
	
	@ValidationObjectName("Group name")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 64)
	private String groupName;
	
	@ValidationObjectName("Transaction(s)")
	@Required(allowEmptyCollection = false)
	private List<String> resultDetailIds;
	
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public List<String> getResultDetailIds() {
		return resultDetailIds;
	}
	public void setResultDetailIds(List<String> resultDetailIds) {
		this.resultDetailIds = resultDetailIds;
	}
	
}
