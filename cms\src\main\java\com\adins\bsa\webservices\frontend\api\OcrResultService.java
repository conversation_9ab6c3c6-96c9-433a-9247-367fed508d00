package com.adins.bsa.webservices.frontend.api;
 
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionFileResponse;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionRequest;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionResponse;
import com.adins.bsa.webservices.model.ocrresult.BankStatementHeaderTransactionSummaryResponse;
import com.adins.bsa.webservices.model.ocrresult.DeleteBankStatementRequest;
import com.adins.bsa.webservices.model.ocrresult.DeleteBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.EditBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.GetBankStatementTransactionSummaryCalculationRequest;
import com.adins.bsa.webservices.model.ocrresult.GetBankStatementTransactionSummaryCalculationResponse;
import com.adins.bsa.webservices.model.ocrresult.GetListBankStatementTransactionDetailRequest;
import com.adins.bsa.webservices.model.ocrresult.GetListBankStatementTransactionDetailResponse;
import com.adins.bsa.webservices.model.ocrresult.OcrResultListRequest;
import com.adins.bsa.webservices.model.ocrresult.OcrResultListResponse;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementHeaderRequest;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementSummaryRequest;
import com.adins.bsa.webservices.model.ocrresult.SaveBankStatementTransactionDetailRequest;
import com.adins.framework.service.base.model.MssResponseType;

public interface OcrResultService {
	
	OcrResultListResponse getOcrResultList(OcrResultListRequest request);
	BankStatementHeaderTransactionResponse getBankStatementHeaderTransaction(BankStatementHeaderTransactionRequest request);
	BankStatementHeaderTransactionFileResponse getBankStatementHeaderTransactionFile(BankStatementHeaderTransactionRequest request);
	BankStatementHeaderTransactionSummaryResponse getBankStatementHeaderTransactionSummary(BankStatementHeaderTransactionRequest request);
	GetListBankStatementTransactionDetailResponse getListBankStatementTransactionDetail(GetListBankStatementTransactionDetailRequest request);
	MssResponseType deleteBankStatement(DeleteBankStatementRequest request);
	MssResponseType saveBankStatementHeader(SaveBankStatementHeaderRequest request);
	GetBankStatementTransactionSummaryCalculationResponse getListBankStatementSummary(GetBankStatementTransactionSummaryCalculationRequest request);
	MssResponseType saveBankStatementSummary(SaveBankStatementSummaryRequest request);
	MssResponseType saveBankStatementTransactionDetail(SaveBankStatementTransactionDetailRequest request);
	MssResponseType deleteBankStatementTransactionDetail(DeleteBankStatementTransactionDetailRequest request);
	MssResponseType editBankStatementTransactionDetail(EditBankStatementTransactionDetailRequest request);
}
