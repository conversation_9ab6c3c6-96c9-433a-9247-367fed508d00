package com.adins.bsa.custom;

import java.io.Serializable;
import java.math.BigInteger;

public class OcrResultBean implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private BigInteger rowNum;
	private String filename;
	private String bank;
	private String accountNo;
	private String accountName;
	private String page;
	private String uploadDate;
	private String lastUpdated;
	private String status;
	private String editable;
	private String fileSourcePath;
	private String redPercentage;
	private String redPercentageWarning;
	
	public BigInteger getRowNum() {
		return rowNum;
	}
	public void setRowNum(BigInteger rowNum) {
		this.rowNum = rowNum;
	}
	public String getFilename() {
		return filename;
	}
	public void setFilename(String filename) {
		this.filename = filename;
	}
	public String getBank() {
		return bank;
	}
	public void setBank(String bank) {
		this.bank = bank;
	}
	public String getAccountNo() {
		return accountNo;
	}
	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	public String getPage() {
		return page;
	}
	public void setPage(String page) {
		this.page = page;
	}
	public String getUploadDate() {
		return uploadDate;
	}
	public void setUploadDate(String uploadDate) {
		this.uploadDate = uploadDate;
	}
	public String getLastUpdated() {
		return lastUpdated;
	}
	public void setLastUpdated(String lastUpdated) {
		this.lastUpdated = lastUpdated;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getEditable() {
		return editable;
	}
	public void setEditable(String editable) {
		this.editable = editable;
	}
	public String getFileSourcePath() {
		return fileSourcePath;
	}
	public void setFileSourcePath(String fileSourcePath) {
		this.fileSourcePath = fileSourcePath;
	}
	public String getRedPercentage() {
		return redPercentage;
	}
	public void setRedPercentage(String redPercentage) {
		this.redPercentage = redPercentage;
	}
	public String getRedPercentageWarning() {
		return redPercentageWarning;
	}
	public void setRedPercentageWarning(String redPercentageWarning) {
		this.redPercentageWarning = redPercentageWarning;
	}
	
}
