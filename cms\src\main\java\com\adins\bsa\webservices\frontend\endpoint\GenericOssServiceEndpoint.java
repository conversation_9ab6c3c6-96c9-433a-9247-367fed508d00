package com.adins.bsa.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.bsa.businesslogic.api.OssLogic;
import com.adins.bsa.webservices.frontend.api.OssService;
import com.adins.bsa.webservices.model.aliyun.OssSignatureResponse;
import com.adins.framework.service.base.model.MssRequestType;

import io.swagger.annotations.Api;

@Component
@Path("/oss")
@Api(value = "OssService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericOssServiceEndpoint implements OssService {
	
	@Autowired private OssLogic ossLogic;

	@Override
	@POST
	@Path("/s/generateSignature")
	public OssSignatureResponse generateSignature(MssRequestType request) {
		return ossLogic.generateSignature(request);
	}

}
