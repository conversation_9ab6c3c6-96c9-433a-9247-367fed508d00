package com.adins.bsa.webservices.model.dataanalytics;

import com.google.gson.annotations.SerializedName;

public class DataAnalyticsConsolidateResponse {

	@SerializedName("consolidate_duration") private String consolidateDuration;
	@SerializedName("memory_usage_mb") private Double memoryUsageMb;
	private String message;
	@SerializedName("process_status") private String processStatus;
	private String status;
	private String timestamp;
	
	public String getConsolidateDuration() {
		return consolidateDuration;
	}
	public void setConsolidateDuration(String consolidateDuration) {
		this.consolidateDuration = consolidateDuration;
	}
	public Double getMemoryUsageMb() {
		return memoryUsageMb;
	}
	public void setMemoryUsageMb(Double memoryUsageMb) {
		this.memoryUsageMb = memoryUsageMb;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getProcessStatus() {
		return processStatus;
	}
	public void setProcessStatus(String processStatus) {
		this.processStatus = processStatus;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
	
}
