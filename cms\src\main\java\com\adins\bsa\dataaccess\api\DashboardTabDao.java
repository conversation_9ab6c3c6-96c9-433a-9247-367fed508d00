package com.adins.bsa.dataaccess.api;

import com.adins.am.model.AmMsrole;
import com.adins.bsa.model.MsDashboardTab;
import com.adins.bsa.model.MsDashboardTabOfRole;

public interface DashboardTabDao {
    
    // ms_dashboard_tab
    MsDashboardTab getDashboardTab(String dashboardTabCode);

    // ms_dashboard_tab_of_role
    MsDashboardTabOfRole getDashboardTabOfRoleReadOnly(AmMsrole role, MsDashboardTab dashboardTab);
}
