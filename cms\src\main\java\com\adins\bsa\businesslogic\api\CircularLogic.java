package com.adins.bsa.businesslogic.api;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.bsa.webservices.model.circular.AddCircularGroupRequest;
import com.adins.bsa.webservices.model.circular.CheckCircularGroupNameRequest;
import com.adins.bsa.webservices.model.circular.ListCircularGroupRequest;
import com.adins.bsa.webservices.model.circular.ListCircularGroupResponse;
import com.adins.bsa.webservices.model.circular.ListCircularTransactionRequest;
import com.adins.bsa.webservices.model.circular.ListCircularTransactionResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface CircularLogic {
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	ListCircularGroupResponse getListCircularGroup(ListCircularGroupRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	ListCircularTransactionResponse getListCircularTransaction(ListCircularTransactionRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType checkCircularGroupName(CheckCircularGroupNameRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType addCircularGroup(AddCircularGroupRequest request, AuditContext audit);
	
	@PreAuthorize("@bsaSecurityServices.isValidUserTenant(#audit.callerId, #request.tenantCode, authentication) and @bsaSecurityServices.isValidTenantRole('ROLE_DASHBOARD', #request.tenantCode, authentication)")
	MssResponseType deleteCircularGroup(CheckCircularGroupNameRequest request, AuditContext audit);
	
	
}
