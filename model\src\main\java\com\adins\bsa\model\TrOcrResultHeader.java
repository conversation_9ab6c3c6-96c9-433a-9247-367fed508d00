package com.adins.bsa.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.ActiveAndUpdateableEntity;

@Entity
@Table(name = "tr_ocr_result_header")
public class TrOcrResultHeader extends ActiveAndUpdateableEntity {
	
	public static final String PARAM_ID_OCR_RESULT_HEADER =  "idOcrResultHeader";
	public static final String PARAM_ACCOUNT_NUMBER = "accountNumber";
	
	private long idOcrResultHeader;
	private TrDashboardGroupH trDashboardGroupH;
	private TrDashboardGroupD trDashboardGroupD;
	private String accountName;
	private Double accountNameConfidence;
	private String accountNameBoxLocation;
	private Integer accountNameBoxPage;
	private String accountNumber;
	private Double accountNumberConfidence;
	private String accountNumberBoxLocation;
	private Integer accountNumberBoxPage;
	private String accountBank;
	private String accountBankOffice;
	private Double accountBankOfficeConfidence;
	private String accountBankOfficeBoxLocation;
	private Integer accountBankOfficeBoxPage;
	private String accountCurrency;
	private Double accountCurrencyConfidence;
	private String accountCurrencyBoxLocation;
	private Integer accountCurrencyBoxPage;
	private String accountAddress;
	private Double accountAddresConfidence;
	private String accountAddressBoxLocation;
	private Integer accountAddressBoxPage;
	private String isEdited;

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ocr_result_header", unique = true, nullable = false)
	public long getIdOcrResultHeader() {
		return idOcrResultHeader;
	}

	public void setIdOcrResultHeader(long idOcrResultHeader) {
		this.idOcrResultHeader = idOcrResultHeader;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_h", nullable = false)
	public TrDashboardGroupH getTrDashboardGroupH() {
		return trDashboardGroupH;
	}

	public void setTrDashboardGroupH(TrDashboardGroupH trDashboardGroupH) {
		this.trDashboardGroupH = trDashboardGroupH;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_dashboard_group_d", nullable = false)
	public TrDashboardGroupD getTrDashboardGroupD() {
		return trDashboardGroupD;
	}

	public void setTrDashboardGroupD(TrDashboardGroupD trDashboardGroupD) {
		this.trDashboardGroupD = trDashboardGroupD;
	}

	@Column(name = "account_name", length = 64)
	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	@Column(name = "account_name_confidence")
	public Double getAccountNameConfidence() {
		return accountNameConfidence;
	}

	public void setAccountNameConfidence(Double accountNameConfidence) {
		this.accountNameConfidence = accountNameConfidence;
	}

	@Column(name = "account_name_box_location", length = 64)
	public String getAccountNameBoxLocation() {
		return accountNameBoxLocation;
	}

	public void setAccountNameBoxLocation(String accountNameBoxLocation) {
		this.accountNameBoxLocation = accountNameBoxLocation;
	}

	@Column(name = "account_name_box_page")
	public Integer getAccountNameBoxPage() {
		return accountNameBoxPage;
	}

	public void setAccountNameBoxPage(Integer accountNameBoxPage) {
		this.accountNameBoxPage = accountNameBoxPage;
	}

	@Column(name = "account_number", length = 64)
	public String getAccountNumber() {
		return accountNumber;
	}

	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}

	@Column(name = "account_number_confidence")
	public Double getAccountNumberConfidence() {
		return accountNumberConfidence;
	}

	public void setAccountNumberConfidence(Double accountNumberConfidence) {
		this.accountNumberConfidence = accountNumberConfidence;
	}

	@Column(name = "account_number_box_location", length = 64)
	public String getAccountNumberBoxLocation() {
		return accountNumberBoxLocation;
	}

	public void setAccountNumberBoxLocation(String accountNumberBoxLocation) {
		this.accountNumberBoxLocation = accountNumberBoxLocation;
	}

	@Column(name = "account_number_box_page")
	public Integer getAccountNumberBoxPage() {
		return accountNumberBoxPage;
	}

	public void setAccountNumberBoxPage(Integer accountNumberBoxPage) {
		this.accountNumberBoxPage = accountNumberBoxPage;
	}

	@Column(name = "account_bank", length = 64)
	public String getAccountBank() {
		return accountBank;
	}

	public void setAccountBank(String accountBank) {
		this.accountBank = accountBank;
	}

	@Column(name = "account_bank_office", length = 64)
	public String getAccountBankOffice() {
		return accountBankOffice;
	}

	public void setAccountBankOffice(String accountBankOffice) {
		this.accountBankOffice = accountBankOffice;
	}

	@Column(name = "account_bank_office_confidence")
	public Double getAccountBankOfficeConfidence() {
		return accountBankOfficeConfidence;
	}

	public void setAccountBankOfficeConfidence(Double accountBankOfficeConfidence) {
		this.accountBankOfficeConfidence = accountBankOfficeConfidence;
	}

	@Column(name = "account_bank_office_box_location")
	public String getAccountBankOfficeBoxLocation() {
		return accountBankOfficeBoxLocation;
	}

	public void setAccountBankOfficeBoxLocation(String accountBankOfficeBoxLocation) {
		this.accountBankOfficeBoxLocation = accountBankOfficeBoxLocation;
	}

	@Column(name = "account_bank_office_box_page")
	public Integer getAccountBankOfficeBoxPage() {
		return accountBankOfficeBoxPage;
	}

	public void setAccountBankOfficeBoxPage(Integer accountBankOfficeBoxPage) {
		this.accountBankOfficeBoxPage = accountBankOfficeBoxPage;
	}

	@Column(name = "account_currency", length = 32)
	public String getAccountCurrency() {
		return accountCurrency;
	}

	public void setAccountCurrency(String accountCurrency) {
		this.accountCurrency = accountCurrency;
	}

	@Column(name = "account_currency_confidence")
	public Double getAccountCurrencyConfidence() {
		return accountCurrencyConfidence;
	}

	public void setAccountCurrencyConfidence(Double accountCurrencyConfidence) {
		this.accountCurrencyConfidence = accountCurrencyConfidence;
	}

	@Column(name = "account_currency_box_location", length = 64)
	public String getAccountCurrencyBoxLocation() {
		return accountCurrencyBoxLocation;
	}

	public void setAccountCurrencyBoxLocation(String accountCurrencyBoxLocation) {
		this.accountCurrencyBoxLocation = accountCurrencyBoxLocation;
	}

	@Column(name = "account_currency_box_page")
	public Integer getAccountCurrencyBoxPage() {
		return accountCurrencyBoxPage;
	}

	public void setAccountCurrencyBoxPage(Integer accountCurrencyBoxPage) {
		this.accountCurrencyBoxPage = accountCurrencyBoxPage;
	}

	@Column(name = "account_address", length = 128)
	public String getAccountAddress() {
		return accountAddress;
	}

	public void setAccountAddress(String accountAddress) {
		this.accountAddress = accountAddress;
	}

	@Column(name = "account_addres_confidence")
	public Double getAccountAddresConfidence() {
		return accountAddresConfidence;
	}

	public void setAccountAddresConfidence(Double accountAddresConfidence) {
		this.accountAddresConfidence = accountAddresConfidence;
	}

	@Column(name = "account_address_box_location", length = 64)
	public String getAccountAddressBoxLocation() {
		return accountAddressBoxLocation;
	}

	public void setAccountAddressBoxLocation(String accountAddressBoxLocation) {
		this.accountAddressBoxLocation = accountAddressBoxLocation;
	}

	@Column(name = "account_address_box_page")
	public Integer getAccountAddressBoxPage() {
		return accountAddressBoxPage;
	}

	public void setAccountAddressBoxPage(Integer accountAddressBoxPage) {
		this.accountAddressBoxPage = accountAddressBoxPage;
	}

	@Column(name = "is_edited", length = 1)
	public String getIsEdited() {
		return isEdited;
	}

	public void setIsEdited(String isEdited) {
		this.isEdited = isEdited;
	}
	
}
