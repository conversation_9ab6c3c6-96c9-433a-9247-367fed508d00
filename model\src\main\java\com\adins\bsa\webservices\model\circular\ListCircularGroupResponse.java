package com.adins.bsa.webservices.model.circular;

import java.util.List;

import com.adins.bsa.custom.CircularBean;
import com.adins.framework.service.base.model.MssResponseType;

public class ListCircularGroupResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private List<CircularBean> circulars;
	private int page;
	private long totalPage;
	private long totalResult;
	
	public List<CircularBean> getCirculars() {
		return circulars;
	}
	public void setCirculars(List<CircularBean> circulars) {
		this.circulars = circulars;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public long getTotalPage() {
		return totalPage;
	}
	public void setTotalPage(long totalPage) {
		this.totalPage = totalPage;
	}
	public long getTotalResult() {
		return totalResult;
	}
	public void setTotalResult(long totalResult) {
		this.totalResult = totalResult;
	}
	
}
