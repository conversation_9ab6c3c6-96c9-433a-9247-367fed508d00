package com.adins.bsa.webservices.model.user;

import com.adins.bsa.annotations.Required;
import com.adins.bsa.annotations.StringLength;
import com.adins.bsa.annotations.ValidationObjectName;
import com.adins.framework.service.base.model.MssRequestType;

public class VerifyOtpRequest extends MssRequestType {
	
	private static final long serialVersionUID = 1L;

	@ValidationObjectName("Login ID")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 80)
	private String loginId;
	
	@ValidationObjectName("OTP")
	@Required(allowBlankString = false)
	@StringLength(maxValue = 6)
	private String otp;

	public String getLoginId() {
		return loginId;
	}

	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}

	public String getOtp() {
		return otp;
	}

	public void setOtp(String otp) {
		this.otp = otp;
	}
	
}
