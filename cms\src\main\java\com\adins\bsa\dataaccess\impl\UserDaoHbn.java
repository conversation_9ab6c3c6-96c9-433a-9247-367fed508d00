package com.adins.bsa.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserpwdhistory;
import com.adins.bsa.dataaccess.api.UserDao;
import com.adins.bsa.model.MsSupervisorOfUser;
import com.adins.bsa.util.MssTool;

@Transactional
@Component
public class UserDaoHbn extends BaseDaoHbn implements UserDao {

	@Override
	public void insertUser(AmMsuser user) {
		managerDAO.insert(user);
	}
	
	@Override
	public void updateUser(AmMsuser user) {
		managerDAO.update(user);
	}

	@Override
	public AmMsuser getUserByIdMsUser(long idMsUser) {
		return managerDAO.selectOne(
				"from AmMsuser u "
				+ "join fetch u.msOffice mo "
				+ "where u.idMsUser = :idMsUser and u.isDeleted ='0' and u.isActive = '1' ", 
				new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser}});
	}

	@Override
	public AmMsuser getUserByEmail(String email) {
		Map<String, Object> params = new HashMap<>();
		params.put("email", StringUtils.upperCase(email));
		
		return managerDAO.selectOne("from AmMsuser mu where mu.loginId = :email ", params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public AmMsuser getUserByEmailNewTrx(String email) {
		Map<String, Object> params = new HashMap<>();
		params.put("email", StringUtils.upperCase(email));
		
		return managerDAO.selectOne("from AmMsuser mu where mu.loginId = :email ", params);
	}

	@Override
	public AmMsuser getUserByPhone(String phone) {
		Map<String, Object> params = new HashMap<>();
		params.put("phone", MssTool.getHashedString(phone));
		
		return managerDAO.selectOne("from AmMsuser mu where mu.hashedPhone = :phone ", params);
	}

	@Override
	public void insertUserPwdHistory(AmUserpwdhistory history) {
		managerDAO.insert(history);
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> getEmailsForLoadTest(int totalData) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("totalData", totalData);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select login_id, reset_code ")
			.append("from am_msuser mu ")
			.append("where mu.login_id like '%LTC.%' ")
			.append("order by mu.id_ms_user desc ")
			.append("limit :totalData ");
		
		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public MsSupervisorOfUser getUserByIdMsUserSupervisorAndStaff(long idMsUserSupervisor, long idMsUserStaff) {
		Map<String, Object> params = new HashMap<>();
		params.put("idMsUserSupervisor", idMsUserSupervisor);
		params.put("idMsUserStaff", idMsUserStaff);
		
		return managerDAO.selectOne(
				"from MsSupervisorOfUser sou "
				+ "join fetch sou.msUserSupervisor spv "
				+ "join fetch sou.msUserStaff s "
				+ "where sou.msUserSupervisor.idMsUser = :idMsUserSupervisor and sou.msUserStaff.idMsUser = :idMsUserStaff", 
				params);
	}	
	
}
