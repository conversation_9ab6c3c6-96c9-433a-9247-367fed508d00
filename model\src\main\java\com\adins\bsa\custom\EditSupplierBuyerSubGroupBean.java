package com.adins.bsa.custom;

import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupD;

public class EditSupplierBuyerSubGroupBean {

	private TrConsolidateResultSupplierBuyerGroupD groupD;
	private boolean isDeleted;
	
	public TrConsolidateResultSupplierBuyerGroupD getGroupD() {
		return groupD;
	}
	public void setGroupD(TrConsolidateResultSupplierBuyerGroupD groupD) {
		this.groupD = groupD;
	}
	public boolean isDeleted() {
		return isDeleted;
	}
	public void setDeleted(boolean isDeleted) {
		this.isDeleted = isDeleted;
	}
	
}
