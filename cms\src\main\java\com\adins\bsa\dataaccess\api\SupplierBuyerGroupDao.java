package com.adins.bsa.dataaccess.api;

import java.util.List;

import com.adins.bsa.custom.SupplierBuyerGroupBean;
import com.adins.bsa.custom.SupplierBuyerSubGroupMemberBean;
import com.adins.bsa.custom.SupplierBuyerSubGroupOfMainGroupBean;
import com.adins.bsa.custom.queryfilter.ListMasterSubGroupFilter;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupD;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupDMember;
import com.adins.bsa.model.TrConsolidateResultSupplierBuyerGroupH;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface SupplierBuyerGroupDao {
	
	// tr_consolidate_result_supplier_buyer_group_h
	void insertGroupH(TrConsolidateResultSupplierBuyerGroupH groupH);
	void updateGroupH(TrConsolidateResultSupplierBuyerGroupH groupH);
	void deleteGroupH(TrConsolidateResultSupplierBuyerGroupH groupH);
	TrConsolidateResultSupplierBuyerGroupH getTrConsolidateResultSupplierBuyerGroupH(TrDashboardGroupH trDashboardGroupH, String groupName);
	
	// tr_consolidate_result_supplier_buyer_group_d
	void insertGroupD(TrConsolidateResultSupplierBuyerGroupD groupD);
	void updateGroupD(TrConsolidateResultSupplierBuyerGroupD groupD);
	void deleteGroupD(TrConsolidateResultSupplierBuyerGroupD groupD);
	
	TrConsolidateResultSupplierBuyerGroupD getTrConsolidateResultSupplierBuyerGroupD(TrDashboardGroupH trDashboardGroupH, String subGroupName);
	
	List<SupplierBuyerGroupBean> getList(String type, long idDashboarGroupH, int min, int max);
	long countList(String type, long idDashboarGroupH);
	
	List<SupplierBuyerSubGroupOfMainGroupBean> getListMasterSubGroup(ListMasterSubGroupFilter filter);
	long countListMasterSubGroup(ListMasterSubGroupFilter filter);
	
	void updateGroupDSetGroupHeaderNull(TrConsolidateResultSupplierBuyerGroupH groupH, AuditContext audit);
	
	List<SupplierBuyerSubGroupOfMainGroupBean> getSubGroupNames(TrConsolidateResultSupplierBuyerGroupH groupH);
	List<SupplierBuyerSubGroupOfMainGroupBean> getListSubGroupByMainGroup(TrDashboardGroupH dashboardGroupH, TrConsolidateResultSupplierBuyerGroupH trConsolidateResultSupplierBuyerGroupH);
	
	// tr_consolidate_result_supplier_buyer_group_d_member
	TrConsolidateResultSupplierBuyerGroupDMember getGroupDetailMember(String resultDetailId);
	TrConsolidateResultSupplierBuyerGroupDMember getGroupDetailMember(TrConsolidateResultSupplierBuyerGroupD groupD, String resultDetailId);
	
	void insertGroupDetailMember(TrConsolidateResultSupplierBuyerGroupDMember member);
	
	void updateGroupDetailMember(TrConsolidateResultSupplierBuyerGroupDMember member);
	void updateGroupDetailMembersFlagAsDeleted(TrConsolidateResultSupplierBuyerGroupD groupD, String isUserEdited, AuditContext audit);
	
	void deleteGroupDetailMember(TrConsolidateResultSupplierBuyerGroupDMember member);
	void deleteGroupDetailMembers(TrConsolidateResultSupplierBuyerGroupD groupD);
	void deleteGroupDetailMembers(TrConsolidateResultSupplierBuyerGroupD groupD, String isUserEdited);
	
	List<SupplierBuyerSubGroupMemberBean> getListSupplierBuyerSubGroupMember(TrConsolidateResultSupplierBuyerGroupD groupD, int min, int max);
	long countListSupplierBuyerSubGroupMember(TrConsolidateResultSupplierBuyerGroupD groupD);
}
