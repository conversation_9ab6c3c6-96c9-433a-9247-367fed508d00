package com.adins.bsa.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.bsa.constants.GlobalVal;
import com.adins.bsa.custom.AnomalyBean;
import com.adins.bsa.custom.AnomalyGroupBean;
import com.adins.bsa.custom.AnomalyMetadataBean;
import com.adins.bsa.dataaccess.api.AnomalyDao;
import com.adins.bsa.model.MsAnomalyReasonRisk;
import com.adins.bsa.model.MsLov;
import com.adins.bsa.model.MsTenant;
import com.adins.bsa.model.TrConsolidateResultAnomaly;
import com.adins.bsa.model.TrDashboardGroupH;
import com.adins.bsa.model.TrOcrResultDetail;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyMetadataRequest;
import com.adins.bsa.webservices.model.anomaly.ListAnomalyRequest;
import com.adins.bsa.webservices.model.anomaly.ListGroupAnomalyRequest;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
@Transactional
public class AnomalyDaoHbn extends BaseDaoHbn implements AnomalyDao {

	private static final String PARAM_LOV_REASON = "lovReason";

	@Override
	public long countList(ListAnomalyRequest request, long idDashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, idDashboardGroupH);
		
		String conditionalParams = buildGetListConditionalParams(request, params);
		String conditionalParamsDescription = buildGetListConditionalParamsDescription(request, params);

		StringBuilder query = new StringBuilder();
		query.append("select count(1) from ( ");
		if (StringUtils.isBlank(request.getType())) {
			query.append(queryListAnomalyOcrResultDetail(conditionalParams))
				.append("union ")
				.append(queryListAnomalyOcrResultHeader(conditionalParams))
				.append("union ")
				.append(queryListAnomalyOcrResultSummaryPeriod(conditionalParams));
		} else if ((GlobalVal.TRX_TYPE_DEBIT.equalsIgnoreCase(request.getType()) || GlobalVal.TRX_TYPE_CREDIT.equalsIgnoreCase(request.getType()))) {
			query.append(queryListAnomalyOcrResultDetail(conditionalParams));
		} else if ("Header".equalsIgnoreCase(request.getType())) {
			query.append(queryListAnomalyOcrResultHeader(conditionalParams));
		} else if ("Summary".equalsIgnoreCase(request.getType())) {
			query.append(queryListAnomalyOcrResultSummaryPeriod(conditionalParams));
		}
		query.append(") as a where 1=1 ")
		.append(conditionalParamsDescription);
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		return result.longValue();
	}

	@Override
	public List<AnomalyBean> getList(ListAnomalyRequest request, long idDashboardGroupH, int min, int max) {
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, idDashboardGroupH);
		String conditionalParams = buildGetListConditionalParams(request, params);
		String conditionalParamsDescription = buildGetListConditionalParamsDescription(request, params);

		StringBuilder query = new StringBuilder();
		query.append("select * from ( ")
			 .append("select row_number() over(order by no asc) as \"no\", file, \"accountNo\", \"accountNoBoxLocation\", \"accountNoBoxPage\", ")
			 .append("date, \"dateBoxLocation\", \"dateBoxPage\", ")
			 .append("description, \"descriptionBoxLocation\", \"descriptionBoxPage\", ")
			 .append("amount, \"amountBoxLocation\", \"amountBoxPage\", ")
			 .append("type, \"typeBoxLocation\", \"typeBoxPage\", risk, \"anomalyId\", ")
			 .append("\"endingBalance\", \"endingBalanceBoxLocation\", \"endingBalanceBoxPage\", \"fileSourcePath\" ")
			 .append("from (");
	 
		if (StringUtils.isBlank(request.getType())) {
			query.append(queryListAnomalyOcrResultDetail(conditionalParams))
			.append("union ")
			.append(queryListAnomalyOcrResultHeader(conditionalParams))
			.append("union ")
			.append(queryListAnomalyOcrResultSummaryPeriod(conditionalParams));
		} else if ((GlobalVal.TRX_TYPE_DEBIT.equalsIgnoreCase(request.getType()) || GlobalVal.TRX_TYPE_CREDIT.equalsIgnoreCase(request.getType()))) {
			query.append(queryListAnomalyOcrResultDetail(conditionalParams));
		} else if ("Header".equalsIgnoreCase(request.getType())) {
			query.append(queryListAnomalyOcrResultHeader(conditionalParams));
		} else if ("Summary".equalsIgnoreCase(request.getType())) {
			query.append(queryListAnomalyOcrResultSummaryPeriod(conditionalParams));
		}
		
		query.append(") as a ")
			 .append(") as b where no between :min and :max ")
			 .append(conditionalParamsDescription)
			 .append("order by date asc");
			 
		return managerDAO.selectForListString(AnomalyBean.class, query.toString(), params, null);
	}
	
	private String queryListAnomalyOcrResultDetail(String conditionalParams) {
		// "risk" is updated to lowercase, so the frontend can capitalize it if needed
		StringBuilder query = new StringBuilder();
		query.append("select a.dtm_crt as \"no\", dd.file_name as \"file\", rh.account_number as \"accountNo\", to_char(rd.transaction_date, 'YYYY-MM-DD') as \"date\", ")
			 .append("rd.transaction_desc as \"description\", rd.transaction_amount as \"amount\", initcap(rd.transaction_type) as \"type\", lre.description as \"reason\", LOWER(lri.code) as \"risk\", a.anomaly_id as \"anomalyId\", rd.transaction_ending_balance as \"endingBalance\", ")
			 .append("rh.account_number_box_location as \"accountNoBoxLocation\", rh.account_number_box_page as \"accountNoBoxPage\", ")
			 .append("rd.transaction_date_box_location as \"dateBoxLocation\", rd.transaction_date_box_page as \"dateBoxPage\", ")
			 .append("rd.transaction_desc_box_location as \"descriptionBoxLocation\", rd.transaction_desc_box_page as \"descriptionBoxPage\", ")
			 .append("rd.transaction_amount_box_location as \"amountBoxLocation\", rd.transaction_amount_box_page as \"amountBoxPage\", ")
			 .append("rd.transaction_ending_balace_box_location as \"endingBalanceBoxLocation\", rd.transaction_ending_balance_box_page as \"endingBalanceBoxPage\", ")
			 .append("rd.transaction_type_box_location as \"typeBoxLocation\", rd.transaction_type_box_page as \"typeBoxPage\", dd.file_source_path as \"fileSourcePath\" ")
			 .append("from tr_consolidate_result_anomaly a ")
			 .append("join tr_dashboard_group_h dh on dh.id_dashboard_group_h = a.id_dashboard_group_h ")
			 .append("join tr_dashboard_group_d dd on a.id_dashboard_group_d = dd.id_dashboard_group_d ")
			 .append("join tr_ocr_result_detail rd on a.id_ocr_result_detail = rd.id_ocr_result_detail ")
			 .append("join tr_ocr_result_header rh on rd.id_ocr_result_header = rh.id_ocr_result_header ")
			 .append("join ms_lov lre on lre.id_lov = a.lov_anomaly_reason ")
			 .append("left join ms_lov lri on lri.id_lov = a.lov_anomaly_risk ")
			 .append("where a.is_deleted = '0' and a.id_dashboard_group_h = :idDashboardGroupH ")
			 .append(conditionalParams);
		
		return query.toString();
	}
	
	private String queryListAnomalyOcrResultHeader(String conditionalParams) {
		// "risk" is updated to lowercase, so the frontend can capitalize it if needed
		StringBuilder query = new StringBuilder();
		query.append("select a.dtm_crt as \"no\", dd.file_name as \"file\", rh.account_number as \"accountNo\", null as \"date\",  ")
			 .append("null as \"description\", null as \"amount\", case when lre.code = 'FRAUD_RULE_INCONSISTENT_METADATA' then 'Metadata' else 'Header' end as \"type\", ")
			 .append("lre.description as \"reason\", LOWER(lri.code) as \"risk\", a.anomaly_id as \"anomalyId\", null as \"endingBalance\", ")
			 .append("case when lre.code = 'FRAUD_RULE_INCONSISTENT_METADATA' then null else rh.account_number_box_location end as \"accountNoBoxLocation\", ")
			 .append("case when lre.code = 'FRAUD_RULE_INCONSISTENT_METADATA' then null else rh.account_number_box_page end as \"accountNoBoxPage\", ")
			 .append("null as \"dateBoxLocation\", null as \"dateBoxPage\", ")
			 .append("null as \"descriptionBoxLocation\", null as \"descriptionBoxPage\", ")
			 .append("null as \"amountBoxLocation\", null as \"amountBoxPage\", ")
			 .append("null as \"endingBalanceBoxLocation\", null as \"endingBalanceBoxPage\", ")
			 .append("null as \"typeBoxLocation\", null as \"typeBoxPage\", dd.file_source_path as \"fileSourcePath\" ")
			 .append("from tr_consolidate_result_anomaly a ")
			 .append("join tr_dashboard_group_h dh on dh.id_dashboard_group_h = a.id_dashboard_group_h ")
			 .append("join tr_dashboard_group_d dd on a.id_dashboard_group_d = dd.id_dashboard_group_d ")
			 .append("join tr_ocr_result_header rh on rh.id_ocr_result_header = a.id_ocr_result_header ")
			 .append("join ms_lov lre on lre.id_lov = a.lov_anomaly_reason ")
			 .append("left join ms_lov lri on lri.id_lov = a.lov_anomaly_risk ")
			 .append("where a.is_deleted = '0' and a.id_dashboard_group_h = :idDashboardGroupH and a.id_ocr_result_detail is null ")
			 .append(conditionalParams);
		
		return query.toString();
	}
	
	private String queryListAnomalyOcrResultSummaryPeriod(String conditionalParams) {
		// "risk" is updated to lowercase, so the frontend can capitalize it if needed
		StringBuilder query = new StringBuilder(); 
		query.append("select a.dtm_crt as \"no\", dd.file_name as \"file\", rh.account_number as \"accountNo\", to_char(sp.period, 'YYYY-MM-DD') as \"date\", ")
			 .append("null as \"description\", null as \"amount\", 'Summary' as \"type\", lre.description as \"reason\", LOWER(lri.code) as \"risk\", a.anomaly_id as \"anomalyId\", null as \"endingBalance\", ")
			 .append("rh.account_number_box_location as \"accountNoBoxLocation\", rh.account_number_box_page as \"accountNoBoxPage\", ")
			 .append("null as \"dateBoxLocation\", null as \"dateBoxPage\", ")
			 .append("null as \"descriptionBoxLocation\", null as \"descriptionBoxPage\", ")
			 .append("null as \"amountBoxLocation\", null as \"amountBoxPage\", ")
			 .append("null as \"endingBalanceBoxLocation\", null as \"endingBalanceBoxPage\", ")
			 .append("null as \"typeBoxLocation\", null as \"typeBoxPage\", dd.file_source_path as \"fileSourcePath\" ")
			 .append("from tr_consolidate_result_anomaly a ")
			 .append("join tr_dashboard_group_h dh on dh.id_dashboard_group_h = a.id_dashboard_group_h ")
			 .append("join tr_dashboard_group_d dd on a.id_dashboard_group_d = dd.id_dashboard_group_d ")
			 .append("join tr_ocr_result_summary_period sp on a.id_ocr_result_summary_period = sp.id_ocr_result_summary_period ")
			 .append("join tr_ocr_result_header rh on sp.id_ocr_result_header = rh.id_ocr_result_header ")
			 .append("join ms_lov lre on lre.id_lov = a.lov_anomaly_reason ")
			 .append("left join ms_lov lri on lri.id_lov = a.lov_anomaly_risk ")
			 .append("where a.is_deleted = '0' and a.id_dashboard_group_h = :idDashboardGroupH ")
			 .append(conditionalParams);
		
		return query.toString();
	}
	
	private String buildGetListConditionalParams(ListAnomalyRequest request, Map<String, Object> params) {
		StringBuilder query = new StringBuilder();
		
		if (StringUtils.isNotBlank(request.getAccountNo())) {
			query.append("and rh.account_number = :accountNo ");
			params.put("accountNo", request.getAccountNo());
		}
		
		if (StringUtils.isNotBlank(request.getReason())) {
			query.append("and lre.code = :reason ");
			params.put("reason", request.getReason());
		}
				
		if (StringUtils.isNotBlank(request.getType()) 
				&& (GlobalVal.TRX_TYPE_DEBIT.equalsIgnoreCase(request.getType()) || GlobalVal.TRX_TYPE_CREDIT.equalsIgnoreCase(request.getType()))) {
			query.append("and rd.transaction_type = :type ");
			params.put("type", StringUtils.upperCase(request.getType()));
		}
		
		return query.toString();
	}

 	private String buildGetListConditionalParamsDescription(ListAnomalyRequest request, Map<String, Object> params) {
		StringBuilder query = new StringBuilder();

		if(StringUtils.isNotBlank(request.getDescription())){
			params.put("description", "%" + StringUtils.lowerCase(request.getDescription()) + "%");
			query.append("and lower(description) like :description ");
		}
		
		return query.toString();
	}
 

	@Override
	public TrConsolidateResultAnomaly getAnomaly(TrDashboardGroupH dashboardGroupH, TrOcrResultDetail ocrResultDetail) {
		Map<String, Object> params = new HashMap<>();
		params.put("dashboardGroupH", dashboardGroupH);
		params.put("ocrResultDetail", ocrResultDetail);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultAnomaly ra "
				+ "where ra.trDashboardGroupH = :dashboardGroupH "
				+ "and ra.trOcrResultDetail = :ocrResultDetail "
				+ "and ra.isDeleted = '0' ", params);
	}

	@Override
	public void insertAnomaly(TrConsolidateResultAnomaly anomaly) {
		managerDAO.insert(anomaly);
	}

	@Override
	public TrConsolidateResultAnomaly getAnomaly(TrDashboardGroupH dashboardGroupH, String anomalyId) {
		Map<String, Object> params = new HashMap<>();
		params.put("dashboardGroupH", dashboardGroupH);
		params.put("anomalyId", anomalyId);
		
		return managerDAO.selectOne(
				"from TrConsolidateResultAnomaly ra "
				+ "join fetch ra.trDashboardGroupH gh "
				+ "where ra.trDashboardGroupH = :dashboardGroupH "
				+ "and ra.anomalyId = :anomalyId "
				+ "and ra.isDeleted = '0' ", params);
	}

	@Override
	public void updateAnomaly(TrConsolidateResultAnomaly anomaly) {
		managerDAO.update(anomaly);
	}

	@Override
	public void deleteAnomaly(TrConsolidateResultAnomaly anomaly) {
		managerDAO.delete(anomaly);
	}

	@Override
	@Transactional(readOnly = true)
	public long countHighRiskAnomaly(TrDashboardGroupH dashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_consolidate_result_anomaly ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ")
			.append("and lov_anomaly_risk in (select id_lov from ms_lov where lov_group = 'ANOMALY_RISK' and code in ('HIGH','VERY HIGH')) ")
			.append("and is_deleted = '0' ");
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0L;
		}
		
		return result.longValue();
	}

	@Override
	@Transactional(readOnly = true)
	public long countMediumRiskAnomaly(TrDashboardGroupH dashboardGroupH) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, dashboardGroupH.getIdDashboardGroupH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_consolidate_result_anomaly ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ")
			.append("and lov_anomaly_risk = (select id_lov from ms_lov where lov_group = 'ANOMALY_RISK' and code = 'MEDIUM') ")
			.append("and is_deleted = '0' ");
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0L;
		}
		
		return result.longValue();
	}

	@Override
	public MsAnomalyReasonRisk getRiskByReasonAndTenant(MsLov lovReason, MsTenant msTenant) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(PARAM_LOV_REASON, lovReason);
		params.put("msTenant", msTenant);

		MsAnomalyReasonRisk result = managerDAO.selectOne(
				"from MsAnomalyReasonRisk r "
				+ "join fetch r.lovAnomalyRisk lar "
				+ "where r.lovAnomalyReason = :lovReason and r.msTenant = :msTenant ", params);
		
		if (null != result) {
			return result;
		}
		
		params.remove("msTenant");
		
		return managerDAO.selectOne(
				"from MsAnomalyReasonRisk r "
				+ "join fetch r.lovAnomalyRisk lar "
				+ "where r.lovAnomalyReason = :lovReason and r.msTenant is null ", params);
	}

	@Override
	public long countListGroupAnomaly(ListGroupAnomalyRequest request, long idDashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, idDashboardGroupH);
		
		String conditionalParams = buildGetListGroupAnomalyConditionalParams(request, params);
		
		StringBuilder query = new StringBuilder();
		query.append("select count(distinct lov_anomaly_reason) as count ")
		.append("from tr_consolidate_result_anomaly a join ms_lov b on a.lov_anomaly_reason = b.id_lov  ")
		.append("join ms_lov c on a.lov_anomaly_risk = c.id_lov ")
		.append("where a.is_deleted = '0' and id_dashboard_group_h = :idDashboardGroupH ")
		.append(conditionalParams);
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		return result.longValue();
	}

	@Override
	public List<AnomalyGroupBean> getListAnomalyGroup(ListGroupAnomalyRequest request, long idDashboardGroupH, int min,
			int max) {
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, idDashboardGroupH);
		String conditionalParams = buildGetListGroupAnomalyConditionalParams(request, params);
		
		StringBuilder query = new StringBuilder();
		query.append("select * from ( ")
				.append("select row_number() over(order by \"riskLevel\" desc, \"trxCount\" desc, \"createdDate\" asc, \"updateDate\" desc, \"reason\" asc) as \"no\", \"reason\",\"reasonCode\",\"risk\",\"trxCount\",\"createdDate\",\"updateDate\" ")
				.append("from (")
				.append(queryListGroupAnomaly(conditionalParams))
				.append(") as dtx ")
				.append(") as data where \"no\" between :min and :max " );
		
		return managerDAO.selectForListString(AnomalyGroupBean.class, query.toString(), params, null);
	}

	private String buildGetListGroupAnomalyConditionalParams(ListGroupAnomalyRequest request, Map<String, Object> params) {
		StringBuilder query = new StringBuilder();
		
		if (StringUtils.isNotBlank(request.getReason())) {
			query.append("and b.code = :reason ");
			params.put("reason", request.getReason());
		}

		if (StringUtils.isNotBlank(request.getRisk())) {
			query.append("and c.code = :risk ");
			params.put("risk", StringUtils.upperCase(request.getRisk()));
		}
		
		return query.toString();
	}

	private String queryListGroupAnomaly(String conditionalParams) {
		StringBuilder query = new StringBuilder(); 
		query.append("select b.description as \"reason\",b.code AS \"reasonCode\",c.code as \"risk\",count(cra.lov_anomaly_reason) as \"trxCount\",to_char(min(cra.dtm_crt), 'DD-Mon-YYYY') as \"createdDate\", ")
			 .append("to_char(max(cra.dtm_upd), 'DD-Mon-YYYY') as \"updateDate\",c.description as \"riskLevel\" ")
			 .append("from tr_consolidate_result_anomaly cra join ms_lov b on cra.lov_anomaly_reason = b.id_lov join ms_lov c on cra.lov_anomaly_risk = c.id_lov ")
			 .append("where cra.is_deleted = '0' and id_dashboard_group_h = :idDashboardGroupH ")
			 .append(conditionalParams)
			 .append("group by b.description, b.code, c.code, c.description order by \"riskLevel\" desc, \"trxCount\" desc, \"createdDate\" asc, \"updateDate\" desc, \"reason\" asc ");
			 
		return query.toString();
	}
	
	@Override
	public void deleteAnomalyByGroup(long idDashboardGroupH, long lovReason, String isUserEdited) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, idDashboardGroupH);
		params.put(PARAM_LOV_REASON, lovReason);
		params.put("isUserEdited", isUserEdited);
		
		StringBuilder query = new StringBuilder();
		query
			.append("delete from tr_consolidate_result_anomaly ")
			.append("where id_dashboard_group_h = :idDashboardGroupH ")
			.append("and lov_anomaly_reason = :lovReason ")
			.append("and is_user_edited = :isUserEdited ");
		
		managerDAO.deleteNativeString(query.toString(), params);
	}

	@Override
	public void updateAnomalyByGroupDeleted(long idDashboardGroupH, long lovReason, String isUserEdited,
			String isDeleted, AuditContext audit) {
				Map<String, Object> params = new HashMap<>();
				params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, idDashboardGroupH);
				params.put(PARAM_LOV_REASON, lovReason);
				params.put("isUserEdited", isUserEdited);
				params.put("isDeleted", isDeleted);
				params.put("usrUpd", audit.getCallerId());
				
				StringBuilder query = new StringBuilder();
				query
					.append("update tr_consolidate_result_anomaly ")
					.append("set is_deleted = :isDeleted , ")
					.append("is_user_edited = '1', ")
					.append("usr_upd = :usrUpd , ")
					.append("dtm_upd = now() ")
					.append("where id_dashboard_group_h = :idDashboardGroupH ")
					.append("and lov_anomaly_reason = :lovReason ")
					.append("and is_user_edited = :isUserEdited ");
				
				managerDAO.updateNativeString(query.toString(), params);
	}

	@Override
	public long countListAnomalyMetadata(ListAnomalyMetadataRequest request, long idDashboardGroupH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, idDashboardGroupH);
		params.put(MsLov.CODE_HBM, request.getReason().toUpperCase());
		
		StringBuilder query = new StringBuilder();
		query.append("select count(1) as count ")
		.append("from tr_consolidate_result_anomaly a join tr_dashboard_group_d c on a.id_dashboard_group_d = c.id_dashboard_group_d  ")
		.append("join ms_lov d on a.lov_anomaly_reason = d.id_lov ")
		.append("where d.code = :code and a.id_dashboard_group_h = :idDashboardGroupH ");
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		return result.longValue();
	}

	@Override
	public List<AnomalyMetadataBean> getListAnomalyMetadata(ListAnomalyMetadataRequest request, long idDashboardGroupH,
			int min, int max) {
				Map<String, Object> params = new HashMap<>();
				params.put("min", min);
				params.put("max", max);
				params.put(TrDashboardGroupH.PARAM_ID_DASHBOARD_GROUP_H, idDashboardGroupH);
				params.put(MsLov.CODE_HBM, request.getReason().toUpperCase());
				
				StringBuilder query = new StringBuilder();
				query.append("select * from ( ")
						.append("select row_number() over(order by c.file_name) as \"no\", c.file_name as \"file\", c.creator as \"creator\", c.producer as \"producer\", ")
						.append("to_char(c.created_date_file, 'DD/MM/YYYY hh:mm') as \"createdDateFile\", to_char(c.modification_date_file, 'DD/MM/YYYY hh:mm') as \"modificationDateFile\", a.anomaly_id as \"anomalyId\", 'Metadata' as \"type\" ")
						.append("from tr_consolidate_result_anomaly a join tr_dashboard_group_d c on a.id_dashboard_group_d = c.id_dashboard_group_d ")
						.append("join ms_lov d on a.lov_anomaly_reason = d.id_lov ")
						.append("where d.code = :code and a.id_dashboard_group_h = :idDashboardGroupH ")
						.append(") as sub ")
						.append("where \"no\" between :min and :max " );
				
				return managerDAO.selectForListString(AnomalyMetadataBean.class, query.toString(), params, null);
	}

}
